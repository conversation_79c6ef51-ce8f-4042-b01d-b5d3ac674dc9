# 环境配置
ENVIRONMENT=development

# 应用配置
APP_NAME=美团联盟小程序API
APP_VERSION=1.0.0
DEBUG=True

# 数据库配置
DATABASE_PATH=data/database.db

# JWT配置
SECRET_KEY=your-super-secret-key-change-in-production-please
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 美团联盟API配置
MEITUAN_APP_KEY=your_meituan_app_key_here
MEITUAN_APP_SECRET=your_meituan_app_secret_here
MEITUAN_BASE_URL=https://openapi.meituan.com

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id_here
WECHAT_APP_SECRET=your_wechat_app_secret_here
WECHAT_API_BASE_URL=https://api.weixin.qq.com

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=data/logs/app.log

# 广告配置
ADS_CONFIG_PATH=ads_config.json

# 服务器配置
HOST=0.0.0.0
PORT=8000

# CORS配置（开发环境可以使用*，生产环境应该指定具体域名）
ALLOWED_ORIGINS=*

# 文件上传配置
UPLOAD_DIR=data/uploads
MAX_FILE_SIZE=10485760

# 缓存配置
CACHE_EXPIRE_TIME=300

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
