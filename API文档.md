# 美团联盟小程序 API 文档

## 目录
- [概述](#概述)
- [技术架构](#技术架构)
- [认证方式](#认证方式)
- [美团联盟API](#美团联盟api)
- [Python后端API](#python后端api)
- [数据库设计](#数据库设计)
- [广告配置](#广告配置)
- [错误处理](#错误处理)
- [部署指南](#部署指南)
- [常见问题](#常见问题)

## 概述

本文档详细介绍了美团联盟小程序中使用的所有API接口，采用Python + SQLite技术栈构建后端服务，通过JSON文件配置广告信息。

### API基础信息
- **美团联盟API域名**: `https://openapi.meituan.com`
- **Python后端API域名**: `https://your-domain.com/api`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **数据库**: SQLite
- **后端框架**: FastAPI/Flask

## 技术架构

### 后端技术栈
- **Python 3.9+**: 主要开发语言
- **FastAPI**: Web框架，提供高性能API服务
- **SQLite**: 轻量级数据库，适合中小型应用
- **SQLAlchemy**: ORM框架，简化数据库操作
- **Pydantic**: 数据验证和序列化
- **uvicorn**: ASGI服务器
- **python-jose**: JWT token处理
- **passlib**: 密码加密
- **httpx**: HTTP客户端，调用美团联盟API

### 项目结构
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用入口
│   ├── config.py            # 配置文件
│   ├── database.py          # 数据库连接
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── content.py
│   │   └── favorite.py
│   ├── schemas/             # Pydantic模型
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── content.py
│   │   └── response.py
│   ├── api/                 # API路由
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── users.py
│   │   ├── products.py
│   │   └── content.py
│   ├── services/            # 业务逻辑
│   │   ├── __init__.py
│   │   ├── auth_service.py
│   │   ├── user_service.py
│   │   ├── meituan_service.py
│   │   └── ad_service.py
│   └── utils/               # 工具函数
│       ├── __init__.py
│       ├── security.py
│       ├── logger.py
│       └── helpers.py
├── data/
│   ├── database.db          # SQLite数据库文件
│   └── ads_config.json      # 广告配置文件
├── requirements.txt         # Python依赖
├── Dockerfile              # Docker配置
└── README.md               # 项目说明
```

## 认证方式

### 美团联盟API认证

美团联盟API使用签名认证方式，Python实现如下：

```python
import hashlib
import time
from typing import Dict, Any

class MeituanAuth:
    def __init__(self, app_key: str, app_secret: str):
        self.app_key = app_key
        self.app_secret = app_secret

    def generate_sign(self, params: Dict[str, Any]) -> str:
        """
        生成美团联盟API签名

        Args:
            params: 请求参数字典

        Returns:
            签名字符串
        """
        # 1. 参数排序
        sorted_keys = sorted(params.keys())

        # 2. 拼接字符串
        sign_str = ''
        for key in sorted_keys:
            sign_str += f"{key}{params[key]}"

        # 3. 添加密钥
        sign_str = f"{self.app_secret}{sign_str}{self.app_secret}"

        # 4. MD5加密并转大写
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()

    def build_request_params(self, method: str, **kwargs) -> Dict[str, Any]:
        """
        构建请求参数

        Args:
            method: API方法名
            **kwargs: 其他参数

        Returns:
            完整的请求参数
        """
        params = {
            'app_key': self.app_key,
            'method': method,
            'timestamp': str(int(time.time())),
            'v': '1.0',
            'format': 'json',
            **kwargs
        }

        # 生成签名
        params['sign'] = self.generate_sign(params)

        return params
```

### Python后端API认证

使用JWT Token认证：

```python
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

# 配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

class AuthService:
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """生成密码哈希"""
        return pwd_context.hash(password)

    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=15)

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt

    @staticmethod
    def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
        """验证JWT令牌"""
        try:
            payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
            user_id: str = payload.get("sub")
            if user_id is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的认证凭据",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return user_id
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
```

## 美团联盟API

### Python美团联盟服务封装

```python
import httpx
import asyncio
from typing import Dict, List, Optional, Any
from app.utils.logger import logger

class MeituanService:
    def __init__(self, app_key: str, app_secret: str):
        self.auth = MeituanAuth(app_key, app_secret)
        self.base_url = "https://openapi.meituan.com"
        self.client = httpx.AsyncClient(timeout=30.0)

    async def get_poi_list(
        self,
        category: Optional[int] = None,
        city_id: Optional[int] = None,
        offset: int = 0,
        limit: int = 20
    ) -> Dict[str, Any]:
        """
        获取推广物料列表

        Args:
            category: 品类ID
            city_id: 城市ID
            offset: 偏移量
            limit: 返回数量

        Returns:
            API响应数据
        """
        params = self.auth.build_request_params(
            method="meituan.poi.getpoilist",
            category=category or '',
            city_id=city_id or '',
            offset=offset,
            limit=limit
        )

        try:
            response = await self.client.post(
                f"{self.base_url}/api/poi/getpoilist",
                data=params
            )
            response.raise_for_status()

            result = response.json()
            if result.get('status') == 0:
                return result.get('data', {})
            else:
                logger.error(f"美团联盟API错误: {result.get('msg')}")
                raise Exception(f"API调用失败: {result.get('msg')}")

        except httpx.RequestError as e:
            logger.error(f"请求美团联盟API失败: {e}")
            raise Exception("网络请求失败")
        except Exception as e:
            logger.error(f"获取推广物料失败: {e}")
            raise

    async def generate_promotion_link(
        self,
        poi_id: str,
        sid: Optional[str] = None
    ) -> str:
        """
        生成推广链接

        Args:
            poi_id: 商户ID
            sid: 推广位ID

        Returns:
            推广链接
        """
        params = self.auth.build_request_params(
            method="meituan.generate.link",
            poi_id=poi_id,
            sid=sid or ''
        )

        try:
            response = await self.client.post(
                f"{self.base_url}/api/generate/link",
                data=params
            )
            response.raise_for_status()

            result = response.json()
            if result.get('status') == 0:
                return result.get('data', {}).get('promotion_url', '')
            else:
                logger.error(f"生成推广链接失败: {result.get('msg')}")
                raise Exception(f"生成推广链接失败: {result.get('msg')}")

        except Exception as e:
            logger.error(f"生成推广链接异常: {e}")
            raise

    async def get_order_list(
        self,
        start_time: str,
        end_time: str,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        获取订单数据

        Args:
            start_time: 开始时间
            end_time: 结束时间
            page: 页码
            page_size: 每页数量

        Returns:
            订单数据
        """
        params = self.auth.build_request_params(
            method="meituan.order.getorderlist",
            start_time=start_time,
            end_time=end_time,
            page=page,
            page_size=page_size
        )

        try:
            response = await self.client.post(
                f"{self.base_url}/api/order/getorderlist",
                data=params
            )
            response.raise_for_status()

            result = response.json()
            if result.get('status') == 0:
                return result.get('data', {})
            else:
                logger.error(f"获取订单数据失败: {result.get('msg')}")
                raise Exception(f"获取订单数据失败: {result.get('msg')}")

        except Exception as e:
            logger.error(f"获取订单数据异常: {e}")
            raise

    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()
```

## Python后端API

### FastAPI应用结构

```python
# app/main.py
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer
from app.database import engine, Base
from app.api import auth, users, products, content
from app.config import settings

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="美团联盟小程序API",
    description="基于Python + SQLite的后端API服务",
    version="1.0.0"
)

# 跨域中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(users.router, prefix="/api/users", tags=["用户"])
app.include_router(products.router, prefix="/api/products", tags=["商品"])
app.include_router(content.router, prefix="/api/content", tags=["内容"])

@app.get("/")
async def root():
    return {"message": "美团联盟小程序API服务"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}
```

### 1. 用户管理

#### 数据模型定义

```python
# app/models/user.py
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float
from sqlalchemy.sql import func
from app.database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    openid = Column(String(100), unique=True, index=True, nullable=False)
    unionid = Column(String(100), index=True)
    nickname = Column(String(100))
    avatar = Column(String(500))
    phone = Column(String(20))
    gender = Column(Integer, default=0)  # 0:未知, 1:男, 2:女
    city = Column(String(50))
    province = Column(String(50))
    country = Column(String(50))
    status = Column(Boolean, default=True)
    total_commission = Column(Float, default=0.0)
    total_orders = Column(Integer, default=0)
    last_login_time = Column(DateTime)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

# app/schemas/user.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    phone: Optional[str] = None
    gender: Optional[int] = 0
    city: Optional[str] = None
    province: Optional[str] = None
    country: Optional[str] = None

class UserCreate(UserBase):
    openid: str
    unionid: Optional[str] = None

class UserUpdate(UserBase):
    pass

class UserResponse(UserBase):
    id: int
    openid: str
    status: bool
    total_commission: float
    total_orders: int
    created_at: datetime

    class Config:
        from_attributes = True
```

#### 用户登录API

```python
# app/api/auth.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.database import get_db
from app.services.auth_service import AuthService
from app.schemas.user import UserCreate, UserResponse
from app.schemas.response import TokenResponse

router = APIRouter()

@router.post("/login", response_model=TokenResponse)
async def login(
    code: str,
    user_info: UserCreate,
    db: Session = Depends(get_db)
):
    """
    微信小程序登录

    Args:
        code: 微信登录code
        user_info: 用户信息
        db: 数据库会话

    Returns:
        包含token和用户信息的响应
    """
    try:
        # 通过code获取openid
        openid = await AuthService.get_openid_by_code(code)
        if not openid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的登录code"
            )

        # 创建或更新用户
        user_info.openid = openid
        user = await AuthService.create_or_update_user(db, user_info)

        # 生成token
        access_token = AuthService.create_access_token(
            data={"sub": str(user.id)}
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user_info": user
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )

@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    current_user_id: str = Depends(AuthService.verify_token),
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        user = db.query(User).filter(User.id == int(current_user_id)).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        access_token = AuthService.create_access_token(
            data={"sub": str(user.id)}
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user_info": user
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"刷新令牌失败: {str(e)}"
        )
```

#### 用户信息API

```python
# app/api/users.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db
from app.models.user import User
from app.schemas.user import UserResponse, UserUpdate
from app.services.auth_service import AuthService

router = APIRouter()

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user_id: str = Depends(AuthService.verify_token),
    db: Session = Depends(get_db)
):
    """获取当前用户信息"""
    user = db.query(User).filter(User.id == int(current_user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user

@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    user_update: UserUpdate,
    current_user_id: str = Depends(AuthService.verify_token),
    db: Session = Depends(get_db)
):
    """更新用户信息"""
    user = db.query(User).filter(User.id == int(current_user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 更新用户信息
    for field, value in user_update.dict(exclude_unset=True).items():
        setattr(user, field, value)

    db.commit()
    db.refresh(user)
    return user

@router.get("/stats")
async def get_user_stats(
    current_user_id: str = Depends(AuthService.verify_token),
    db: Session = Depends(get_db)
):
    """获取用户统计数据"""
    user = db.query(User).filter(User.id == int(current_user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 计算统计数据
    stats = {
        "total_commission": user.total_commission,
        "total_orders": user.total_orders,
        "month_commission": 0.0,  # 需要根据实际业务计算
        "month_orders": 0,        # 需要根据实际业务计算
        "conversion_rate": 0.15,  # 需要根据实际业务计算
        "favorite_count": 0       # 需要查询收藏表
    }

    return {"code": 0, "data": stats}
```

### 2. 商品管理API

```python
# app/api/products.py
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
from app.database import get_db
from app.services.meituan_service import MeituanService
from app.services.auth_service import AuthService
from app.config import settings

router = APIRouter()
meituan_service = MeituanService(settings.MEITUAN_APP_KEY, settings.MEITUAN_APP_SECRET)

@router.get("/list")
async def get_products(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[int] = None,
    city_id: Optional[int] = None,
    keyword: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取商品列表

    Args:
        page: 页码
        limit: 每页数量
        category: 分类ID
        city_id: 城市ID
        keyword: 搜索关键词

    Returns:
        商品列表数据
    """
    try:
        offset = (page - 1) * limit

        # 调用美团联盟API
        result = await meituan_service.get_poi_list(
            category=category,
            city_id=city_id,
            offset=offset,
            limit=limit
        )

        return {
            "code": 0,
            "message": "success",
            "data": {
                "list": result.get("list", []),
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": result.get("total", 0),
                    "pages": (result.get("total", 0) + limit - 1) // limit
                }
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取商品列表失败: {str(e)}")

@router.get("/{product_id}")
async def get_product_detail(
    product_id: str,
    db: Session = Depends(get_db)
):
    """获取商品详情"""
    try:
        # 这里可以添加缓存逻辑
        detail = await meituan_service.get_product_detail(product_id)

        return {
            "code": 0,
            "message": "success",
            "data": detail
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取商品详情失败: {str(e)}")

@router.post("/{product_id}/promotion-link")
async def generate_promotion_link(
    product_id: str,
    sid: Optional[str] = None,
    current_user_id: str = Depends(AuthService.verify_token),
    db: Session = Depends(get_db)
):
    """生成推广链接"""
    try:
        promotion_url = await meituan_service.generate_promotion_link(
            poi_id=product_id,
            sid=sid
        )

        # 记录推广链接生成日志
        # TODO: 添加推广记录到数据库

        return {
            "code": 0,
            "message": "success",
            "data": {
                "promotion_url": promotion_url
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成推广链接失败: {str(e)}")
```

### 3. 收藏管理API

```python
# app/models/favorite.py
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.sql import func
from app.database import Base

class Favorite(Base):
    __tablename__ = "favorites"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, nullable=False)
    target_type = Column(String(20), nullable=False)  # product, content
    target_id = Column(String(50), nullable=False)
    target_data = Column(JSON)  # 目标数据快照
    created_at = Column(DateTime, server_default=func.now())

# app/api/favorites.py
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db
from app.models.favorite import Favorite
from app.services.auth_service import AuthService

router = APIRouter()

@router.post("/")
async def add_favorite(
    target_type: str,
    target_id: str,
    target_data: dict,
    current_user_id: str = Depends(AuthService.verify_token),
    db: Session = Depends(get_db)
):
    """添加收藏"""
    # 检查是否已收藏
    existing = db.query(Favorite).filter(
        Favorite.user_id == int(current_user_id),
        Favorite.target_type == target_type,
        Favorite.target_id == target_id
    ).first()

    if existing:
        raise HTTPException(status_code=400, detail="已经收藏过了")

    favorite = Favorite(
        user_id=int(current_user_id),
        target_type=target_type,
        target_id=target_id,
        target_data=target_data
    )

    db.add(favorite)
    db.commit()
    db.refresh(favorite)

    return {"code": 0, "message": "收藏成功", "data": {"id": favorite.id}}

@router.get("/")
async def get_favorites(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    target_type: str = Query(None),
    current_user_id: str = Depends(AuthService.verify_token),
    db: Session = Depends(get_db)
):
    """获取收藏列表"""
    query = db.query(Favorite).filter(Favorite.user_id == int(current_user_id))

    if target_type:
        query = query.filter(Favorite.target_type == target_type)

    total = query.count()
    favorites = query.offset((page - 1) * limit).limit(limit).all()

    return {
        "code": 0,
        "message": "success",
        "data": {
            "list": [
                {
                    "id": fav.id,
                    "target_type": fav.target_type,
                    "target_id": fav.target_id,
                    "target_data": fav.target_data,
                    "created_at": fav.created_at
                }
                for fav in favorites
            ],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total,
                "pages": (total + limit - 1) // limit
            }
        }
    }

@router.delete("/{favorite_id}")
async def remove_favorite(
    favorite_id: int,
    current_user_id: str = Depends(AuthService.verify_token),
    db: Session = Depends(get_db)
):
    """取消收藏"""
    favorite = db.query(Favorite).filter(
        Favorite.id == favorite_id,
        Favorite.user_id == int(current_user_id)
    ).first()

    if not favorite:
        raise HTTPException(status_code=404, detail="收藏不存在")

    db.delete(favorite)
    db.commit()

    return {"code": 0, "message": "取消收藏成功"}
```

## 数据库设计

### SQLite数据库配置

```python
# app/database.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings

# SQLite数据库URL
SQLALCHEMY_DATABASE_URL = f"sqlite:///{settings.DATABASE_PATH}"

# 创建数据库引擎
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},  # SQLite特有配置
    echo=settings.DEBUG  # 开发环境显示SQL语句
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# app/config.py
from pydantic import BaseSettings
import os

class Settings(BaseSettings):
    # 数据库配置
    DATABASE_PATH: str = "data/database.db"

    # JWT配置
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 美团联盟配置
    MEITUAN_APP_KEY: str = os.getenv("MEITUAN_APP_KEY", "")
    MEITUAN_APP_SECRET: str = os.getenv("MEITUAN_APP_SECRET", "")

    # 微信小程序配置
    WECHAT_APP_ID: str = os.getenv("WECHAT_APP_ID", "")
    WECHAT_APP_SECRET: str = os.getenv("WECHAT_APP_SECRET", "")

    # 应用配置
    DEBUG: bool = True
    LOG_LEVEL: str = "INFO"

    class Config:
        env_file = ".env"

settings = Settings()
```

### 数据库表结构

```sql
-- 用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    openid VARCHAR(100) UNIQUE NOT NULL,
    unionid VARCHAR(100),
    nickname VARCHAR(100),
    avatar VARCHAR(500),
    phone VARCHAR(20),
    gender INTEGER DEFAULT 0,
    city VARCHAR(50),
    province VARCHAR(50),
    country VARCHAR(50),
    status BOOLEAN DEFAULT 1,
    total_commission REAL DEFAULT 0.0,
    total_orders INTEGER DEFAULT 0,
    last_login_time DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 收藏表
CREATE TABLE favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    target_type VARCHAR(20) NOT NULL,
    target_id VARCHAR(50) NOT NULL,
    target_data JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, target_type, target_id)
);

-- 内容表
CREATE TABLE contents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    type VARCHAR(20) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    images JSON,
    video_url VARCHAR(500),
    tags JSON,
    category_id INTEGER,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    is_featured BOOLEAN DEFAULT 0,
    published_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 统计表
CREATE TABLE statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    event_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(20),
    target_id VARCHAR(50),
    extra_data JSON,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_users_unionid ON users(unionid);
CREATE INDEX idx_favorites_user_id ON favorites(user_id);
CREATE INDEX idx_favorites_target ON favorites(target_type, target_id);
CREATE INDEX idx_contents_user_id ON contents(user_id);
CREATE INDEX idx_contents_type ON contents(type);
CREATE INDEX idx_contents_status ON contents(status);
CREATE INDEX idx_statistics_user_id ON statistics(user_id);
CREATE INDEX idx_statistics_event_type ON statistics(event_type);
```

## 广告配置

### 广告配置JSON文件

```json
{
  "ads_config": {
    "version": "1.0.0",
    "last_updated": "2024-01-15T10:00:00Z",
    "ads": [
      {
        "id": "banner_001",
        "type": "banner",
        "position": "home_top",
        "title": "美团外卖优惠券",
        "description": "新用户专享，满30减15",
        "image_url": "https://example.com/images/banner1.jpg",
        "link_url": "https://example.com/promotion/001",
        "link_type": "internal",
        "priority": 1,
        "status": "active",
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-12-31T23:59:59Z",
        "target_audience": {
          "user_type": "new",
          "city": ["北京", "上海", "广州", "深圳"],
          "age_range": [18, 35]
        },
        "display_rules": {
          "max_daily_impressions": 1000,
          "max_user_impressions": 3,
          "frequency_cap": "1/hour"
        }
      },
      {
        "id": "popup_001",
        "type": "popup",
        "position": "app_launch",
        "title": "限时抢购",
        "description": "精选商品5折起，仅限今日",
        "image_url": "https://example.com/images/popup1.jpg",
        "link_url": "https://example.com/flash-sale",
        "link_type": "external",
        "priority": 2,
        "status": "active",
        "start_time": "2024-01-15T00:00:00Z",
        "end_time": "2024-01-15T23:59:59Z",
        "target_audience": {
          "user_type": "all",
          "city": ["全国"],
          "purchase_history": "has_orders"
        },
        "display_rules": {
          "max_daily_impressions": 500,
          "max_user_impressions": 1,
          "frequency_cap": "1/day"
        }
      },
      {
        "id": "native_001",
        "type": "native",
        "position": "product_list",
        "title": "推荐商品",
        "description": "为您精选的优质商品",
        "image_url": "https://example.com/images/native1.jpg",
        "link_url": "https://example.com/products/recommended",
        "link_type": "internal",
        "priority": 3,
        "status": "active",
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-12-31T23:59:59Z",
        "target_audience": {
          "user_type": "all",
          "interests": ["美食", "购物", "生活服务"]
        },
        "display_rules": {
          "max_daily_impressions": 2000,
          "max_user_impressions": 5,
          "frequency_cap": "1/30min"
        }
      }
    ],
    "global_settings": {
      "ad_timeout": 5000,
      "retry_attempts": 3,
      "fallback_ads": ["banner_001"],
      "analytics_enabled": true,
      "user_consent_required": false
    }
  }
}
```

### 广告服务API

```python
# app/services/ad_service.py
import json
import asyncio
from typing import List, Dict, Optional
from datetime import datetime
from app.utils.logger import logger

class AdService:
    def __init__(self, config_path: str = "data/ads_config.json"):
        self.config_path = config_path
        self.ads_config = self.load_config()

    def load_config(self) -> Dict:
        """加载广告配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"广告配置文件不存在: {self.config_path}")
            return {"ads_config": {"ads": [], "global_settings": {}}}
        except json.JSONDecodeError as e:
            logger.error(f"广告配置文件格式错误: {e}")
            return {"ads_config": {"ads": [], "global_settings": {}}}

    def reload_config(self):
        """重新加载配置"""
        self.ads_config = self.load_config()
        logger.info("广告配置已重新加载")

    def get_ads_by_position(
        self,
        position: str,
        user_info: Optional[Dict] = None,
        limit: int = 10
    ) -> List[Dict]:
        """
        根据位置获取广告

        Args:
            position: 广告位置
            user_info: 用户信息（用于定向）
            limit: 返回数量限制

        Returns:
            符合条件的广告列表
        """
        ads = self.ads_config.get("ads_config", {}).get("ads", [])
        filtered_ads = []

        for ad in ads:
            # 检查基本条件
            if not self._is_ad_valid(ad, position):
                continue

            # 检查定向条件
            if user_info and not self._check_targeting(ad, user_info):
                continue

            filtered_ads.append(ad)

        # 按优先级排序
        filtered_ads.sort(key=lambda x: x.get("priority", 999))

        return filtered_ads[:limit]

    def _is_ad_valid(self, ad: Dict, position: str) -> bool:
        """检查广告是否有效"""
        # 检查状态
        if ad.get("status") != "active":
            return False

        # 检查位置
        if ad.get("position") != position:
            return False

        # 检查时间
        now = datetime.utcnow()
        start_time = datetime.fromisoformat(ad.get("start_time", "").replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(ad.get("end_time", "").replace('Z', '+00:00'))

        if not (start_time <= now <= end_time):
            return False

        return True

    def _check_targeting(self, ad: Dict, user_info: Dict) -> bool:
        """检查定向条件"""
        target_audience = ad.get("target_audience", {})

        # 检查用户类型
        user_type = target_audience.get("user_type")
        if user_type and user_type != "all":
            if user_type == "new" and user_info.get("total_orders", 0) > 0:
                return False
            if user_type == "old" and user_info.get("total_orders", 0) == 0:
                return False

        # 检查城市
        target_cities = target_audience.get("city", [])
        if target_cities and "全国" not in target_cities:
            user_city = user_info.get("city", "")
            if user_city not in target_cities:
                return False

        # 检查年龄范围
        age_range = target_audience.get("age_range")
        if age_range and len(age_range) == 2:
            user_age = user_info.get("age", 0)
            if not (age_range[0] <= user_age <= age_range[1]):
                return False

        return True

    async def record_ad_impression(self, ad_id: str, user_id: Optional[int] = None):
        """记录广告展示"""
        # 这里可以记录到数据库或日志
        logger.info(f"广告展示记录: ad_id={ad_id}, user_id={user_id}")

    async def record_ad_click(self, ad_id: str, user_id: Optional[int] = None):
        """记录广告点击"""
        # 这里可以记录到数据库或日志
        logger.info(f"广告点击记录: ad_id={ad_id}, user_id={user_id}")

# 全局广告服务实例
ad_service = AdService()
```

### 广告API接口

```python
# app/api/ads.py
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional
from app.database import get_db
from app.services.ad_service import ad_service
from app.services.auth_service import AuthService
from app.models.user import User

router = APIRouter()

@router.get("/")
async def get_ads(
    position: str = Query(..., description="广告位置"),
    limit: int = Query(10, ge=1, le=50),
    current_user_id: Optional[str] = Depends(AuthService.verify_token_optional),
    db: Session = Depends(get_db)
):
    """
    获取指定位置的广告

    Args:
        position: 广告位置 (home_top, app_launch, product_list等)
        limit: 返回数量限制
        current_user_id: 当前用户ID（可选）

    Returns:
        广告列表
    """
    user_info = None

    # 如果用户已登录，获取用户信息用于广告定向
    if current_user_id:
        user = db.query(User).filter(User.id == int(current_user_id)).first()
        if user:
            user_info = {
                "city": user.city,
                "total_orders": user.total_orders,
                "age": 25,  # 需要根据实际业务计算
            }

    ads = ad_service.get_ads_by_position(position, user_info, limit)

    return {
        "code": 0,
        "message": "success",
        "data": {
            "ads": ads,
            "position": position,
            "count": len(ads)
        }
    }

@router.post("/{ad_id}/impression")
async def record_impression(
    ad_id: str,
    current_user_id: Optional[str] = Depends(AuthService.verify_token_optional)
):
    """记录广告展示"""
    user_id = int(current_user_id) if current_user_id else None
    await ad_service.record_ad_impression(ad_id, user_id)

    return {"code": 0, "message": "记录成功"}

@router.post("/{ad_id}/click")
async def record_click(
    ad_id: str,
    current_user_id: Optional[str] = Depends(AuthService.verify_token_optional)
):
    """记录广告点击"""
    user_id = int(current_user_id) if current_user_id else None
    await ad_service.record_ad_click(ad_id, user_id)

    return {"code": 0, "message": "记录成功"}

@router.post("/reload-config")
async def reload_ad_config():
    """重新加载广告配置（管理员接口）"""
    ad_service.reload_config()
    return {"code": 0, "message": "配置重新加载成功"}
```

## 错误处理

### 统一错误响应格式

```python
# app/schemas/response.py
from pydantic import BaseModel
from typing import Any, Optional, List

class BaseResponse(BaseModel):
    code: int
    message: str
    data: Optional[Any] = None
    timestamp: Optional[int] = None

class ErrorResponse(BaseModel):
    code: int
    message: str
    detail: Optional[str] = None
    errors: Optional[List[dict]] = None
    timestamp: Optional[int] = None

class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    user_info: dict

class PaginationResponse(BaseModel):
    page: int
    limit: int
    total: int
    pages: int

class ListResponse(BaseModel):
    code: int = 0
    message: str = "success"
    data: dict
```

### 全局异常处理

```python
# app/utils/exceptions.py
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import time
import traceback
from app.utils.logger import logger

class CustomHTTPException(HTTPException):
    def __init__(self, status_code: int, detail: str, code: int = None):
        super().__init__(status_code, detail)
        self.code = code or status_code

async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": getattr(exc, 'code', exc.status_code),
            "message": exc.detail,
            "timestamp": int(time.time() * 1000)
        }
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """参数验证异常处理器"""
    errors = []
    for error in exc.errors():
        errors.append({
            "field": ".".join(str(x) for x in error["loc"][1:]),
            "message": error["msg"],
            "type": error["type"]
        })

    return JSONResponse(
        status_code=422,
        content={
            "code": 422,
            "message": "参数验证失败",
            "errors": errors,
            "timestamp": int(time.time() * 1000)
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    logger.error(traceback.format_exc())

    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "服务器内部错误",
            "timestamp": int(time.time() * 1000)
        }
    )

# 在main.py中注册异常处理器
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)
```

### 错误码定义

```python
# app/utils/error_codes.py
class ErrorCode:
    # 成功
    SUCCESS = 0

    # 客户端错误 (400-499)
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    VALIDATION_ERROR = 422

    # 服务器错误 (500-599)
    INTERNAL_ERROR = 500
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503

    # 业务错误 (1000+)
    USER_NOT_FOUND = 1001
    USER_ALREADY_EXISTS = 1002
    INVALID_LOGIN_CODE = 1003
    TOKEN_EXPIRED = 1004
    PERMISSION_DENIED = 1005

    # 美团联盟API错误 (2000+)
    MEITUAN_API_ERROR = 2001
    MEITUAN_SIGN_ERROR = 2002
    MEITUAN_RATE_LIMIT = 2003

    # 数据库错误 (3000+)
    DATABASE_ERROR = 3001
    RECORD_NOT_FOUND = 3002
    DUPLICATE_RECORD = 3003

ERROR_MESSAGES = {
    ErrorCode.SUCCESS: "成功",
    ErrorCode.BAD_REQUEST: "请求参数错误",
    ErrorCode.UNAUTHORIZED: "未授权访问",
    ErrorCode.FORBIDDEN: "禁止访问",
    ErrorCode.NOT_FOUND: "资源不存在",
    ErrorCode.VALIDATION_ERROR: "参数验证失败",
    ErrorCode.INTERNAL_ERROR: "服务器内部错误",
    ErrorCode.USER_NOT_FOUND: "用户不存在",
    ErrorCode.USER_ALREADY_EXISTS: "用户已存在",
    ErrorCode.INVALID_LOGIN_CODE: "无效的登录code",
    ErrorCode.TOKEN_EXPIRED: "令牌已过期",
    ErrorCode.MEITUAN_API_ERROR: "美团联盟API调用失败",
    ErrorCode.MEITUAN_SIGN_ERROR: "美团联盟API签名错误",
    ErrorCode.DATABASE_ERROR: "数据库操作失败",
}

def get_error_message(code: int) -> str:
    return ERROR_MESSAGES.get(code, "未知错误")
```

## 部署指南

### 环境要求

- Python 3.9+
- SQLite 3.x
- 推荐使用虚拟环境

### 本地开发部署

```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 创建环境变量文件
cp .env.example .env

# 5. 编辑环境变量
# 配置美团联盟API密钥、微信小程序配置等

# 6. 初始化数据库
python -c "from app.database import engine, Base; Base.metadata.create_all(bind=engine)"

# 7. 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Docker部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir -p data

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - DATABASE_PATH=data/database.db
      - MEITUAN_APP_KEY=${MEITUAN_APP_KEY}
      - MEITUAN_APP_SECRET=${MEITUAN_APP_SECRET}
      - WECHAT_APP_ID=${WECHAT_APP_ID}
      - WECHAT_APP_SECRET=${WECHAT_APP_SECRET}
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped
```

### 生产环境部署

```bash
# 1. 使用生产环境配置
export ENVIRONMENT=production

# 2. 使用Gunicorn启动
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# 3. 使用systemd管理服务
sudo tee /etc/systemd/system/meituan-api.service > /dev/null <<EOF
[Unit]
Description=Meituan Alliance API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/meituan-api
Environment=PATH=/opt/meituan-api/venv/bin
ExecStart=/opt/meituan-api/venv/bin/gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 4. 启动服务
sudo systemctl daemon-reload
sudo systemctl enable meituan-api
sudo systemctl start meituan-api
```

### 数据库备份

```python
# scripts/backup_db.py
import shutil
import os
from datetime import datetime

def backup_database():
    """备份SQLite数据库"""
    db_path = "data/database.db"
    backup_dir = "backups"

    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{backup_dir}/database_backup_{timestamp}.db"

    try:
        shutil.copy2(db_path, backup_path)
        print(f"数据库备份成功: {backup_path}")

        # 清理旧备份（保留最近7天）
        cleanup_old_backups(backup_dir, days=7)

    except Exception as e:
        print(f"数据库备份失败: {e}")

def cleanup_old_backups(backup_dir: str, days: int = 7):
    """清理旧备份文件"""
    import time

    now = time.time()
    cutoff = now - (days * 24 * 60 * 60)

    for filename in os.listdir(backup_dir):
        filepath = os.path.join(backup_dir, filename)
        if os.path.isfile(filepath) and os.path.getmtime(filepath) < cutoff:
            os.remove(filepath)
            print(f"删除旧备份: {filename}")

if __name__ == "__main__":
    backup_database()
```

## 常见问题

### Q1: SQLite数据库锁定问题

**问题**: 在高并发情况下出现数据库锁定错误

**解决方案**:
```python
# 在database.py中配置连接池
from sqlalchemy.pool import StaticPool

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={
        "check_same_thread": False,
        "timeout": 20
    },
    poolclass=StaticPool,
    pool_pre_ping=True,
    echo=settings.DEBUG
)
```

### Q2: 美团联盟API签名验证失败

**问题**: 调用美团联盟API时返回签名错误

**解决方案**:
1. 检查参数排序是否正确
2. 确认字符串拼接格式
3. 验证MD5加密后是否转为大写
4. 检查时间戳格式

```python
# 调试签名生成过程
def debug_sign_generation(params: Dict[str, Any], app_secret: str):
    print("原始参数:", params)

    sorted_keys = sorted(params.keys())
    print("排序后的键:", sorted_keys)

    sign_str = ''
    for key in sorted_keys:
        sign_str += f"{key}{params[key]}"
    print("拼接字符串:", sign_str)

    sign_str = f"{app_secret}{sign_str}{app_secret}"
    print("添加密钥后:", sign_str)

    signature = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    print("最终签名:", signature)

    return signature
```

### Q3: JWT Token过期处理

**问题**: 用户token过期后需要重新登录

**解决方案**:
```python
# 实现token自动刷新机制
@router.middleware("http")
async def token_refresh_middleware(request: Request, call_next):
    response = await call_next(request)

    # 检查是否为401错误
    if response.status_code == 401:
        # 尝试刷新token
        refresh_token = request.headers.get("X-Refresh-Token")
        if refresh_token:
            try:
                new_token = await refresh_access_token(refresh_token)
                response.headers["X-New-Token"] = new_token
            except:
                pass

    return response
```

### Q4: 广告配置热更新

**问题**: 修改广告配置后需要重启服务

**解决方案**:
```python
# 实现文件监控自动重载
import asyncio
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ConfigFileHandler(FileSystemEventHandler):
    def __init__(self, ad_service):
        self.ad_service = ad_service

    def on_modified(self, event):
        if event.src_path.endswith('ads_config.json'):
            self.ad_service.reload_config()
            print("广告配置已自动重载")

# 启动文件监控
observer = Observer()
observer.schedule(ConfigFileHandler(ad_service), "data/", recursive=False)
observer.start()
```

### Q5: 数据库性能优化

**问题**: 随着数据量增长，查询性能下降

**解决方案**:
1. 添加适当的索引
2. 使用分页查询
3. 实现查询缓存
4. 考虑数据归档

```python
# 实现简单的查询缓存
from functools import wraps
import json
import hashlib

def cache_query(expire_time: int = 300):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"query:{func.__name__}:{hashlib.md5(str(args).encode()).hexdigest()}"

            # 尝试从缓存获取
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)

            # 执行查询
            result = await func(*args, **kwargs)

            # 缓存结果
            await redis_client.setex(cache_key, expire_time, json.dumps(result, default=str))

            return result
        return wrapper
    return decorator
```

---

**文档版本**: v2.0.0
**更新时间**: 2024年1月
**技术栈**: Python 3.9+ + FastAPI + SQLite
**维护者**: 开发团队
```
```
```
```
```

#### 用户登录

**接口地址**: `POST /api/user/login`

**请求参数**:
```json
{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token_string",
    "userInfo": {
      "id": 1,
      "openid": "user_openid",
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "created_at": "2024-01-01 00:00:00"
    }
  }
}
```

**实现示例**:
```javascript
/**
 * 用户登录
 * @param {string} code - 微信登录code
 * @param {Object} userInfo - 用户信息
 * @returns {Promise} 登录结果
 */
async function userLogin(code, userInfo) {
  try {
    const response = await wx.request({
      url: 'https://your-domain.com/api/user/login',
      method: 'POST',
      data: {
        code: code,
        userInfo: userInfo
      }
    });

    if (response.data.code === 200) {
      // 保存token
      wx.setStorageSync('token', response.data.data.token);
      wx.setStorageSync('userInfo', response.data.data.userInfo);
    }

    return response.data;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
}
```

#### 获取用户信息

**接口地址**: `GET /api/user/profile`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "nickname": "用户昵称",
    "avatar": "头像URL",
    "phone": "手机号",
    "total_commission": 1250.50,
    "total_orders": 25
  }
}
```

### 2. 收藏管理

#### 添加收藏

**接口地址**: `POST /api/favorites`

**请求参数**:
```json
{
  "poi_id": "商户ID",
  "poi_name": "商户名称",
  "poi_image": "商户图片",
  "category": "分类",
  "avg_price": 50
}
```

**实现示例**:
```javascript
/**
 * 添加收藏
 * @param {Object} poiInfo - 商户信息
 * @returns {Promise} 收藏结果
 */
async function addFavorite(poiInfo) {
  const token = wx.getStorageSync('token');

  try {
    const response = await wx.request({
      url: 'https://your-domain.com/api/favorites',
      method: 'POST',
      header: setAuthHeaders(token),
      data: poiInfo
    });

    return response.data;
  } catch (error) {
    console.error('添加收藏失败:', error);
    throw error;
  }
}
```

#### 获取收藏列表

**接口地址**: `GET /api/favorites`

**查询参数**:
- `page`: 页码
- `limit`: 每页数量

**实现示例**:
```javascript
/**
 * 获取收藏列表
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @returns {Promise} 收藏列表
 */
async function getFavorites(page = 1, limit = 20) {
  const token = wx.getStorageSync('token');

  try {
    const response = await wx.request({
      url: `https://your-domain.com/api/favorites?page=${page}&limit=${limit}`,
      method: 'GET',
      header: setAuthHeaders(token)
    });

    return response.data;
  } catch (error) {
    console.error('获取收藏列表失败:', error);
    throw error;
  }
}
```

### 3. 社区内容管理

#### 发布内容

**接口地址**: `POST /api/community/posts`

**请求参数**:
```json
{
  "title": "标题",
  "content": "内容",
  "images": ["图片URL1", "图片URL2"],
  "poi_id": "关联商户ID",
  "tags": ["标签1", "标签2"]
}
```

**实现示例**:
```javascript
/**
 * 发布社区内容
 * @param {Object} postData - 发布内容
 * @returns {Promise} 发布结果
 */
async function createPost(postData) {
  const token = wx.getStorageSync('token');

  try {
    const response = await wx.request({
      url: 'https://your-domain.com/api/community/posts',
      method: 'POST',
      header: setAuthHeaders(token),
      data: postData
    });

    return response.data;
  } catch (error) {
    console.error('发布内容失败:', error);
    throw error;
  }
}
```

#### 获取社区内容列表

**接口地址**: `GET /api/community/posts`

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `category`: 分类筛选
- `sort`: 排序方式 (latest, hot, recommended)

**实现示例**:
```javascript
/**
 * 获取社区内容列表
 * @param {Object} options - 查询选项
 * @returns {Promise} 内容列表
 */
async function getCommunityPosts(options = {}) {
  const params = new URLSearchParams({
    page: options.page || 1,
    limit: options.limit || 20,
    category: options.category || '',
    sort: options.sort || 'latest'
  });

  try {
    const response = await wx.request({
      url: `https://your-domain.com/api/community/posts?${params}`,
      method: 'GET'
    });

    return response.data;
  } catch (error) {
    console.error('获取社区内容失败:', error);
    throw error;
  }
}
```

### 4. 数据统计

#### 获取用户统计数据

**接口地址**: `GET /api/stats/user`

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "total_commission": 1250.50,
    "month_commission": 350.20,
    "total_orders": 25,
    "month_orders": 8,
    "conversion_rate": 0.15,
    "favorite_count": 12
  }
}
```

**实现示例**:
```javascript
/**
 * 获取用户统计数据
 * @returns {Promise} 统计数据
 */
async function getUserStats() {
  const token = wx.getStorageSync('token');

  try {
    const response = await wx.request({
      url: 'https://your-domain.com/api/stats/user',
      method: 'GET',
      header: setAuthHeaders(token)
    });

    return response.data;
  } catch (error) {
    console.error('获取统计数据失败:', error);
    throw error;
  }
}
```

## 错误处理

### 错误码定义

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | 正常处理 |
| 400 | 请求参数错误 | 检查参数格式 |
| 401 | 未授权 | 重新登录 |
| 403 | 禁止访问 | 检查权限 |
| 404 | 资源不存在 | 提示用户 |
| 500 | 服务器错误 | 稍后重试 |
| 1001 | 签名错误 | 检查签名算法 |
| 1002 | 参数缺失 | 补充必要参数 |
| 1003 | 频率限制 | 降低请求频率 |

### 统一错误处理

```javascript
/**
 * 统一API错误处理
 * @param {Object} error - 错误对象
 * @param {Function} callback - 错误回调
 */
function handleApiError(error, callback) {
  let message = '网络错误，请稍后重试';

  if (error.data && error.data.code) {
    switch (error.data.code) {
      case 401:
        message = '登录已过期，请重新登录';
        // 清除本地token
        wx.removeStorageSync('token');
        wx.removeStorageSync('userInfo');
        // 跳转到登录页
        wx.navigateTo({
          url: '/pages/login/login'
        });
        break;
      case 403:
        message = '没有访问权限';
        break;
      case 404:
        message = '请求的资源不存在';
        break;
      case 1001:
        message = '签名验证失败';
        break;
      case 1002:
        message = '请求参数不完整';
        break;
      case 1003:
        message = '请求过于频繁，请稍后重试';
        break;
      default:
        message = error.data.message || message;
    }
  }

  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });

  if (callback) {
    callback(error);
  }
}
```

## SDK使用指南

### 初始化配置

```javascript
/**
 * API配置类
 */
class ApiConfig {
  constructor() {
    this.baseUrl = 'https://your-domain.com/api';
    this.meituanBaseUrl = 'https://openapi.meituan.com';
    this.appKey = 'your_app_key';
    this.appSecret = 'your_app_secret';
  }

  /**
   * 获取请求头
   * @returns {Object} 请求头对象
   */
  getHeaders() {
    const token = wx.getStorageSync('token');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    };
  }
}

// 全局配置实例
const apiConfig = new ApiConfig();
```

### 请求封装

```javascript
/**
 * 统一请求封装
 * @param {Object} options - 请求选项
 * @returns {Promise} 请求结果
 */
function request(options) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: options.header || apiConfig.getHeaders(),
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}
```

## 常见问题

### Q1: 美团联盟API签名验证失败

**A**: 检查以下几点：
1. 参数排序是否正确
2. 字符串拼接格式是否正确
3. MD5加密后是否转为大写
4. 时间戳格式是否正确

### Q2: 跨域问题

**A**: 在小程序后台配置服务器域名，确保API域名在白名单中。

### Q3: Token过期处理

**A**: 实现自动刷新token机制：

```javascript
/**
 * 自动刷新token
 * @returns {Promise} 新的token
 */
async function refreshToken() {
  const refreshToken = wx.getStorageSync('refreshToken');

  try {
    const response = await request({
      url: `${apiConfig.baseUrl}/auth/refresh`,
      method: 'POST',
      data: { refresh_token: refreshToken }
    });

    wx.setStorageSync('token', response.data.token);
    return response.data.token;
  } catch (error) {
    // 刷新失败，跳转登录页
    wx.navigateTo({
      url: '/pages/login/login'
    });
    throw error;
  }
}
```

### Q4: 请求频率限制

**A**: 实现请求队列和频率控制：

```javascript
/**
 * 请求频率控制器
 */
class RateLimiter {
  constructor(maxRequests = 10, timeWindow = 60000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
    this.requests = [];
  }

  /**
   * 检查是否可以发送请求
   * @returns {boolean} 是否允许请求
   */
  canMakeRequest() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.timeWindow);

    if (this.requests.length < this.maxRequests) {
      this.requests.push(now);
      return true;
    }

    return false;
  }
}

const rateLimiter = new RateLimiter();
```

---

**更新时间**: 2024年1月
**版本**: v1.0.0
**维护者**: 开发团队