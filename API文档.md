# 美团联盟小程序 API 文档

## 目录
- [概述](#概述)
- [认证方式](#认证方式)
- [美团联盟API](#美团联盟api)
- [自建后端API](#自建后端api)
- [错误处理](#错误处理)
- [SDK使用指南](#sdk使用指南)
- [常见问题](#常见问题)

## 概述

本文档详细介绍了美团联盟小程序中使用的所有API接口，包括美团联盟官方API和自建后端API。

### API基础信息
- **美团联盟API域名**: `https://openapi.meituan.com`
- **自建后端API域名**: `https://your-domain.com/api`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证方式

### 美团联盟API认证

美团联盟API使用签名认证方式，需要以下参数：

```javascript
/**
 * 生成美团联盟API签名
 * @param {Object} params - 请求参数
 * @param {string} appSecret - 应用密钥
 * @returns {string} 签名字符串
 */
function generateSign(params, appSecret) {
  // 1. 参数排序
  const sortedKeys = Object.keys(params).sort();
  
  // 2. 拼接字符串
  let signStr = '';
  sortedKeys.forEach(key => {
    signStr += key + params[key];
  });
  
  // 3. 添加密钥
  signStr = appSecret + signStr + appSecret;
  
  // 4. MD5加密并转大写
  return md5(signStr).toUpperCase();
}
```

### 自建API认证

使用JWT Token认证：

```javascript
/**
 * 设置请求头认证信息
 * @param {string} token - JWT Token
 * @returns {Object} 请求头对象
 */
function setAuthHeaders(token) {
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
}
```

## 美团联盟API

### 1. 获取推广物料

#### 接口地址
`POST /api/poi/getpoilist`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| app_key | string | 是 | 应用Key |
| timestamp | string | 是 | 时间戳 |
| sign | string | 是 | 签名 |
| category | int | 否 | 品类ID |
| city_id | int | 否 | 城市ID |
| offset | int | 否 | 偏移量，默认0 |
| limit | int | 否 | 返回数量，默认20 |

#### 请求示例

```javascript
/**
 * 获取推广物料列表
 * @param {Object} options - 查询选项
 * @returns {Promise} API响应
 */
async function getPoiList(options = {}) {
  const params = {
    app_key: 'your_app_key',
    timestamp: Date.now().toString(),
    category: options.category || '',
    city_id: options.cityId || '',
    offset: options.offset || 0,
    limit: options.limit || 20
  };
  
  // 生成签名
  params.sign = generateSign(params, 'your_app_secret');
  
  try {
    const response = await wx.request({
      url: 'https://openapi.meituan.com/api/poi/getpoilist',
      method: 'POST',
      data: params
    });
    
    return response.data;
  } catch (error) {
    console.error('获取推广物料失败:', error);
    throw error;
  }
}
```

#### 响应示例

```json
{
  "status": 0,
  "msg": "success",
  "data": {
    "total": 100,
    "offset": 0,
    "limit": 20,
    "list": [
      {
        "poi_id": "123456",
        "poi_name": "美味餐厅",
        "category_name": "美食",
        "avg_price": 50,
        "pic_url": "https://example.com/pic.jpg",
        "address": "北京市朝阳区",
        "commission_rate": "5%",
        "promotion_url": "https://example.com/promotion"
      }
    ]
  }
}
```

### 2. 生成推广链接

#### 接口地址
`POST /api/generate/link`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| app_key | string | 是 | 应用Key |
| timestamp | string | 是 | 时间戳 |
| sign | string | 是 | 签名 |
| poi_id | string | 是 | 商户ID |
| sid | string | 否 | 推广位ID |

#### 请求示例

```javascript
/**
 * 生成推广链接
 * @param {string} poiId - 商户ID
 * @param {string} sid - 推广位ID
 * @returns {Promise} 推广链接
 */
async function generatePromotionLink(poiId, sid = '') {
  const params = {
    app_key: 'your_app_key',
    timestamp: Date.now().toString(),
    poi_id: poiId,
    sid: sid
  };
  
  params.sign = generateSign(params, 'your_app_secret');
  
  try {
    const response = await wx.request({
      url: 'https://openapi.meituan.com/api/generate/link',
      method: 'POST',
      data: params
    });
    
    return response.data.data.promotion_url;
  } catch (error) {
    console.error('生成推广链接失败:', error);
    throw error;
  }
}
```

### 3. 获取订单数据

#### 接口地址
`POST /api/order/getorderlist`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| app_key | string | 是 | 应用Key |
| timestamp | string | 是 | 时间戳 |
| sign | string | 是 | 签名 |
| start_time | string | 是 | 开始时间 |
| end_time | string | 是 | 结束时间 |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

#### 请求示例

```javascript
/**
 * 获取订单列表
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {number} page - 页码
 * @returns {Promise} 订单数据
 */
async function getOrderList(startTime, endTime, page = 1) {
  const params = {
    app_key: 'your_app_key',
    timestamp: Date.now().toString(),
    start_time: startTime,
    end_time: endTime,
    page: page,
    page_size: 20
  };
  
  params.sign = generateSign(params, 'your_app_secret');
  
  try {
    const response = await wx.request({
      url: 'https://openapi.meituan.com/api/order/getorderlist',
      method: 'POST',
      data: params
    });
    
    return response.data;
  } catch (error) {
    console.error('获取订单数据失败:', error);
    throw error;
  }
}
```

## 自建后端API

### 1. 用户管理

#### 用户登录

**接口地址**: `POST /api/user/login`

**请求参数**:
```json
{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token_string",
    "userInfo": {
      "id": 1,
      "openid": "user_openid",
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "created_at": "2024-01-01 00:00:00"
    }
  }
}
```

**实现示例**:
```javascript
/**
 * 用户登录
 * @param {string} code - 微信登录code
 * @param {Object} userInfo - 用户信息
 * @returns {Promise} 登录结果
 */
async function userLogin(code, userInfo) {
  try {
    const response = await wx.request({
      url: 'https://your-domain.com/api/user/login',
      method: 'POST',
      data: {
        code: code,
        userInfo: userInfo
      }
    });
    
    if (response.data.code === 200) {
      // 保存token
      wx.setStorageSync('token', response.data.data.token);
      wx.setStorageSync('userInfo', response.data.data.userInfo);
    }
    
    return response.data;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
}
```

#### 获取用户信息

**接口地址**: `GET /api/user/profile`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "nickname": "用户昵称",
    "avatar": "头像URL",
    "phone": "手机号",
    "total_commission": 1250.50,
    "total_orders": 25
  }
}
```

### 2. 收藏管理

#### 添加收藏

**接口地址**: `POST /api/favorites`

**请求参数**:
```json
{
  "poi_id": "商户ID",
  "poi_name": "商户名称",
  "poi_image": "商户图片",
  "category": "分类",
  "avg_price": 50
}
```

**实现示例**:
```javascript
/**
 * 添加收藏
 * @param {Object} poiInfo - 商户信息
 * @returns {Promise} 收藏结果
 */
async function addFavorite(poiInfo) {
  const token = wx.getStorageSync('token');
  
  try {
    const response = await wx.request({
      url: 'https://your-domain.com/api/favorites',
      method: 'POST',
      header: setAuthHeaders(token),
      data: poiInfo
    });
    
    return response.data;
  } catch (error) {
    console.error('添加收藏失败:', error);
    throw error;
  }
}
```

#### 获取收藏列表

**接口地址**: `GET /api/favorites`

**查询参数**:
- `page`: 页码
- `limit`: 每页数量

**实现示例**:
```javascript
/**
 * 获取收藏列表
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @returns {Promise} 收藏列表
 */
async function getFavorites(page = 1, limit = 20) {
  const token = wx.getStorageSync('token');
  
  try {
    const response = await wx.request({
      url: `https://your-domain.com/api/favorites?page=${page}&limit=${limit}`,
      method: 'GET',
      header: setAuthHeaders(token)
    });
    
    return response.data;
  } catch (error) {
    console.error('获取收藏列表失败:', error);
    throw error;
  }
}
```

### 3. 社区内容管理

#### 发布内容

**接口地址**: `POST /api/community/posts`

**请求参数**:
```json
{
  "title": "标题",
  "content": "内容",
  "images": ["图片URL1", "图片URL2"],
  "poi_id": "关联商户ID",
  "tags": ["标签1", "标签2"]
}
```

**实现示例**:
```javascript
/**
 * 发布社区内容
 * @param {Object} postData - 发布内容
 * @returns {Promise} 发布结果
 */
async function createPost(postData) {
  const token = wx.getStorageSync('token');
  
  try {
    const response = await wx.request({
      url: 'https://your-domain.com/api/community/posts',
      method: 'POST',
      header: setAuthHeaders(token),
      data: postData
    });
    
    return response.data;
  } catch (error) {
    console.error('发布内容失败:', error);
    throw error;
  }
}
```

#### 获取社区内容列表

**接口地址**: `GET /api/community/posts`

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `category`: 分类筛选
- `sort`: 排序方式 (latest, hot, recommended)

**实现示例**:
```javascript
/**
 * 获取社区内容列表
 * @param {Object} options - 查询选项
 * @returns {Promise} 内容列表
 */
async function getCommunityPosts(options = {}) {
  const params = new URLSearchParams({
    page: options.page || 1,
    limit: options.limit || 20,
    category: options.category || '',
    sort: options.sort || 'latest'
  });
  
  try {
    const response = await wx.request({
      url: `https://your-domain.com/api/community/posts?${params}`,
      method: 'GET'
    });
    
    return response.data;
  } catch (error) {
    console.error('获取社区内容失败:', error);
    throw error;
  }
}
```

### 4. 数据统计

#### 获取用户统计数据

**接口地址**: `GET /api/stats/user`

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "total_commission": 1250.50,
    "month_commission": 350.20,
    "total_orders": 25,
    "month_orders": 8,
    "conversion_rate": 0.15,
    "favorite_count": 12
  }
}
```

**实现示例**:
```javascript
/**
 * 获取用户统计数据
 * @returns {Promise} 统计数据
 */
async function getUserStats() {
  const token = wx.getStorageSync('token');
  
  try {
    const response = await wx.request({
      url: 'https://your-domain.com/api/stats/user',
      method: 'GET',
      header: setAuthHeaders(token)
    });
    
    return response.data;
  } catch (error) {
    console.error('获取统计数据失败:', error);
    throw error;
  }
}
```

## 错误处理

### 错误码定义

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | 正常处理 |
| 400 | 请求参数错误 | 检查参数格式 |
| 401 | 未授权 | 重新登录 |
| 403 | 禁止访问 | 检查权限 |
| 404 | 资源不存在 | 提示用户 |
| 500 | 服务器错误 | 稍后重试 |
| 1001 | 签名错误 | 检查签名算法 |
| 1002 | 参数缺失 | 补充必要参数 |
| 1003 | 频率限制 | 降低请求频率 |

### 统一错误处理

```javascript
/**
 * 统一API错误处理
 * @param {Object} error - 错误对象
 * @param {Function} callback - 错误回调
 */
function handleApiError(error, callback) {
  let message = '网络错误，请稍后重试';
  
  if (error.data && error.data.code) {
    switch (error.data.code) {
      case 401:
        message = '登录已过期，请重新登录';
        // 清除本地token
        wx.removeStorageSync('token');
        wx.removeStorageSync('userInfo');
        // 跳转到登录页
        wx.navigateTo({
          url: '/pages/login/login'
        });
        break;
      case 403:
        message = '没有访问权限';
        break;
      case 404:
        message = '请求的资源不存在';
        break;
      case 1001:
        message = '签名验证失败';
        break;
      case 1002:
        message = '请求参数不完整';
        break;
      case 1003:
        message = '请求过于频繁，请稍后重试';
        break;
      default:
        message = error.data.message || message;
    }
  }
  
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
  
  if (callback) {
    callback(error);
  }
}
```

## SDK使用指南

### 初始化配置

```javascript
/**
 * API配置类
 */
class ApiConfig {
  constructor() {
    this.baseUrl = 'https://your-domain.com/api';
    this.meituanBaseUrl = 'https://openapi.meituan.com';
    this.appKey = 'your_app_key';
    this.appSecret = 'your_app_secret';
  }
  
  /**
   * 获取请求头
   * @returns {Object} 请求头对象
   */
  getHeaders() {
    const token = wx.getStorageSync('token');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    };
  }
}

// 全局配置实例
const apiConfig = new ApiConfig();
```

### 请求封装

```javascript
/**
 * 统一请求封装
 * @param {Object} options - 请求选项
 * @returns {Promise} 请求结果
 */
function request(options) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: options.header || apiConfig.getHeaders(),
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}
```

## 常见问题

### Q1: 美团联盟API签名验证失败

**A**: 检查以下几点：
1. 参数排序是否正确
2. 字符串拼接格式是否正确
3. MD5加密后是否转为大写
4. 时间戳格式是否正确

### Q2: 跨域问题

**A**: 在小程序后台配置服务器域名，确保API域名在白名单中。

### Q3: Token过期处理

**A**: 实现自动刷新token机制：

```javascript
/**
 * 自动刷新token
 * @returns {Promise} 新的token
 */
async function refreshToken() {
  const refreshToken = wx.getStorageSync('refreshToken');
  
  try {
    const response = await request({
      url: `${apiConfig.baseUrl}/auth/refresh`,
      method: 'POST',
      data: { refresh_token: refreshToken }
    });
    
    wx.setStorageSync('token', response.data.token);
    return response.data.token;
  } catch (error) {
    // 刷新失败，跳转登录页
    wx.navigateTo({
      url: '/pages/login/login'
    });
    throw error;
  }
}
```

### Q4: 请求频率限制

**A**: 实现请求队列和频率控制：

```javascript
/**
 * 请求频率控制器
 */
class RateLimiter {
  constructor(maxRequests = 10, timeWindow = 60000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
    this.requests = [];
  }
  
  /**
   * 检查是否可以发送请求
   * @returns {boolean} 是否允许请求
   */
  canMakeRequest() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    
    if (this.requests.length < this.maxRequests) {
      this.requests.push(now);
      return true;
    }
    
    return false;
  }
}

const rateLimiter = new RateLimiter();
```

---

**更新时间**: 2024年1月
**版本**: v1.0.0
**维护者**: 开发团队