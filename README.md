# 美团联盟微信小程序

基于美团联盟API开发的微信小程序，提供商品推广、社区内容、专题文章等功能。

## 项目概述

本项目是一个完整的微信小程序，通过对接美团联盟API实现商品推广功能，同时集成社区内容和专题文章，为用户提供优质的购物和内容消费体验。

## 核心功能

### 1. 首页
- **地理位置展示**：显示用户当前城市
- **搜索功能**：支持商品和店铺搜索
- **品牌展示**：热门品牌快捷入口
- **活动轮播**：推广活动展示
- **分类导航**：商品分类快速筛选
- **商品列表**：推荐商品展示和购买

### 2. 商品列表页
- **分类筛选**：多维度商品筛选
- **排序功能**：价格、销量等排序
- **商品展示**：详细商品信息
- **推广跳转**：跳转美团小程序购买

### 3. 社区页面
- **内容发布**：用户生成内容(UGC)
- **内容展示**：图文混排展示
- **商品嵌入**：内容中嵌入推广商品
- **互动功能**：点赞、评论、分享

### 4. 专题页面
- **文章聚合**：对接公众号优质内容
- **分类浏览**：按主题分类展示
- **外链跳转**：跳转公众号原文

### 5. 个人中心
- **用户信息**：头像、昵称展示
- **资产展示**：优惠券、余额、积分
- **订单管理**：推广订单查询
- **功能服务**：收藏、历史、设置等

## 技术架构

### 前端技术
- **框架**：微信小程序原生开发
- **样式**：WXSS + Flex布局
- **状态管理**：页面级数据管理
- **组件化**：可复用组件设计

### API集成
- **美团联盟API**：商品数据、转链、订单查询
- **微信API**：登录、分享、地理位置、跳转
- **自建后端**：用户系统、内容管理、数据统计

### 核心流程
1. **商品展示**：后端调用美团联盟推广物料API获取商品数据
2. **推广转链**：用户点击商品时，后端调用转链API生成推广链接
3. **跳转购买**：小程序调用`wx.navigateToMiniProgram`跳转美团小程序
4. **订单追踪**：通过联盟订单查询API跟踪推广效果

## 项目结构

```
zhang_mt/
├── app.js                 # 小程序入口文件
├── app.json              # 全局配置
├── app.wxss              # 全局样式
├── sitemap.json          # 搜索优化配置
├── pages/                # 页面目录
│   ├── index/            # 首页
│   │   ├── index.js
│   │   ├── index.json
│   │   ├── index.wxml
│   │   └── index.wxss
│   ├── products/         # 商品列表页
│   │   ├── products.js
│   │   ├── products.json
│   │   ├── products.wxml
│   │   └── products.wxss
│   ├── community/        # 社区页面
│   │   ├── community.js
│   │   ├── community.json
│   │   ├── community.wxml
│   │   └── community.wxss
│   ├── topic/            # 专题页面
│   │   ├── topic.js
│   │   ├── topic.json
│   │   ├── topic.wxml
│   │   └── topic.wxss
│   └── profile/          # 个人中心
│       ├── profile.js
│       ├── profile.json
│       ├── profile.wxml
│       └── profile.wxss
├── images/               # 图片资源
└── utils/                # 工具函数
```

## 开发环境

### 环境要求
- **微信开发者工具**：最新版本
- **Node.js**：14.0+（如需本地开发工具）
- **微信小程序账号**：已认证的小程序账号
- **美团联盟账号**：已申请的联盟推广账号

### 配置说明
1. **小程序配置**：在`app.json`中配置页面路径和tabBar
2. **API配置**：在后端配置美团联盟API密钥
3. **域名配置**：在小程序后台配置服务器域名

## 部署说明

### 小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在小程序后台提交审核
4. 审核通过后发布上线

### 后端部署
1. 部署后端服务到云服务器
2. 配置HTTPS域名和SSL证书
3. 在小程序后台配置服务器域名
4. 配置美团联盟API密钥

## API接口说明

### 美团联盟API
- **推广物料API**：获取商品数据
- **转链API**：生成推广链接
- **订单查询API**：查询推广订单

### 自建API
- **用户系统**：登录、用户信息管理
- **内容管理**：社区内容CRUD
- **数据统计**：用户行为分析

## 功能特色

### 1. 无缝购买体验
- 通过`wx.navigateToMiniProgram`实现无缝跳转
- 用户在美团小程序完成购买后可返回
- 推广订单自动追踪和统计

### 2. 社区内容生态
- 支持用户发布图文内容
- 内容中可嵌入推广商品
- 完整的互动功能（点赞、评论、分享）

### 3. 专题内容聚合
- 对接微信公众号API
- 自动同步优质文章内容
- 支持跳转到公众号原文

### 4. 个性化推荐
- 基于用户地理位置推荐
- 根据浏览历史个性化展示
- 智能商品推荐算法

## 注意事项

### 1. 合规要求
- 遵守微信小程序平台规范
- 遵守美团联盟推广规则
- 确保内容合法合规

### 2. 性能优化
- 图片懒加载和压缩
- 分页加载优化
- 缓存策略实施

### 3. 用户体验
- 响应式设计适配不同设备
- 加载状态和错误处理
- 无障碍访问支持

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现核心功能模块
- 完成美团联盟API集成
- 上线基础社区功能

## 联系方式

如有问题或建议，请联系开发团队。

## 许可证

本项目仅供学习和参考使用。