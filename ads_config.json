{"ads_config": {"version": "1.0.0", "last_updated": "2024-01-15T10:00:00Z", "description": "美团联盟小程序广告配置文件", "ads": [{"id": "banner_home_001", "type": "banner", "position": "home_top", "title": "美团外卖新用户专享", "description": "新用户下单立减15元，满30元可用", "image_url": "https://example.com/images/banner_home_001.jpg", "link_url": "https://example.com/promotion/new-user", "link_type": "internal", "priority": 1, "status": "active", "start_time": "2024-01-01T00:00:00Z", "end_time": "2024-12-31T23:59:59Z", "target_audience": {"user_type": "new", "city": ["北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都"], "age_range": [18, 45], "device_type": "all"}, "display_rules": {"max_daily_impressions": 1000, "max_user_impressions": 3, "frequency_cap": "1/hour", "min_interval": 3600}, "tracking": {"impression_url": "https://analytics.example.com/impression", "click_url": "https://analytics.example.com/click"}}, {"id": "popup_flash_sale_001", "type": "popup", "position": "app_launch", "title": "限时抢购", "description": "精选美食5折起，仅限今日！错过再等一年", "image_url": "https://example.com/images/popup_flash_sale.jpg", "link_url": "https://example.com/flash-sale", "link_type": "external", "priority": 2, "status": "active", "start_time": "2024-01-15T00:00:00Z", "end_time": "2024-01-15T23:59:59Z", "target_audience": {"user_type": "all", "city": ["全国"], "purchase_history": "has_orders", "last_order_days": 7}, "display_rules": {"max_daily_impressions": 500, "max_user_impressions": 1, "frequency_cap": "1/day", "show_delay": 2000}, "tracking": {"impression_url": "https://analytics.example.com/impression", "click_url": "https://analytics.example.com/click"}}, {"id": "native_product_001", "type": "native", "position": "product_list", "title": "为您推荐", "description": "根据您的喜好精选的优质商品", "image_url": "https://example.com/images/native_product.jpg", "link_url": "https://example.com/products/recommended", "link_type": "internal", "priority": 3, "status": "active", "start_time": "2024-01-01T00:00:00Z", "end_time": "2024-12-31T23:59:59Z", "target_audience": {"user_type": "all", "interests": ["美食", "购物", "生活服务"], "behavior": ["browse_products", "add_favorites"]}, "display_rules": {"max_daily_impressions": 2000, "max_user_impressions": 5, "frequency_cap": "1/30min", "position_in_list": [3, 8, 15]}, "tracking": {"impression_url": "https://analytics.example.com/impression", "click_url": "https://analytics.example.com/click"}}, {"id": "banner_community_001", "type": "banner", "position": "community_top", "title": "分享有奖", "description": "分享美食体验，赢取现金红包", "image_url": "https://example.com/images/banner_community.jpg", "link_url": "https://example.com/activity/share-reward", "link_type": "internal", "priority": 4, "status": "active", "start_time": "2024-01-01T00:00:00Z", "end_time": "2024-03-31T23:59:59Z", "target_audience": {"user_type": "active", "social_activity": "high", "content_creation": true}, "display_rules": {"max_daily_impressions": 800, "max_user_impressions": 2, "frequency_cap": "1/2hour"}, "tracking": {"impression_url": "https://analytics.example.com/impression", "click_url": "https://analytics.example.com/click"}}, {"id": "interstitial_weekend_001", "type": "interstitial", "position": "page_transition", "title": "周末特惠", "description": "周末专享优惠，全场8折起", "image_url": "https://example.com/images/interstitial_weekend.jpg", "link_url": "https://example.com/weekend-special", "link_type": "external", "priority": 5, "status": "active", "start_time": "2024-01-01T00:00:00Z", "end_time": "2024-12-31T23:59:59Z", "target_audience": {"user_type": "all", "time_filter": {"days_of_week": [6, 7], "hours": [10, 22]}}, "display_rules": {"max_daily_impressions": 300, "max_user_impressions": 1, "frequency_cap": "1/day", "show_probability": 0.3}, "tracking": {"impression_url": "https://analytics.example.com/impression", "click_url": "https://analytics.example.com/click"}}], "global_settings": {"ad_timeout": 5000, "retry_attempts": 3, "fallback_ads": ["banner_home_001", "native_product_001"], "analytics_enabled": true, "user_consent_required": false, "cache_duration": 300, "preload_enabled": true, "debug_mode": false, "a_b_testing": {"enabled": true, "test_groups": ["A", "B"], "traffic_split": [50, 50]}}, "ad_positions": {"home_top": {"max_ads": 1, "refresh_interval": 30000, "auto_rotate": true}, "app_launch": {"max_ads": 1, "show_delay": 1000, "auto_close": 5000}, "product_list": {"max_ads": 3, "insertion_strategy": "distributed", "min_content_between": 2}, "community_top": {"max_ads": 1, "refresh_interval": 60000}, "page_transition": {"max_ads": 1, "show_probability": 0.2, "cooldown_period": 300000}}, "targeting_rules": {"user_types": {"new": "total_orders == 0", "active": "total_orders > 5 AND last_order_days <= 30", "inactive": "last_order_days > 30", "vip": "total_commission > 1000"}, "behavior_tracking": {"page_views": true, "click_tracking": true, "conversion_tracking": true, "retention_period": 30}}}}