// app.js
App({
  /**
   * 小程序初始化完成时触发
   */
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    this.login()
    
    // 获取用户位置信息
    this.getUserLocation()
  },

  /**
   * 用户登录获取openid
   */
  login() {
    wx.login({
      success: res => {
        console.log('登录成功，code:', res.code)
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        // 这里需要调用后端接口
        this.globalData.userCode = res.code
      },
      fail: err => {
        console.error('登录失败:', err)
      }
    })
  },

  /**
   * 获取用户地理位置
   */
  getUserLocation() {
    wx.getLocation({
      type: 'wgs84',
      success: res => {
        console.log('位置获取成功:', res)
        this.globalData.location = {
          latitude: res.latitude,
          longitude: res.longitude
        }
        // 根据经纬度获取城市信息
        this.getCityInfo(res.latitude, res.longitude)
      },
      fail: err => {
        console.error('位置获取失败:', err)
        // 设置默认城市
        this.globalData.cityName = '广州市'
      }
    })
  },

  /**
   * 根据经纬度获取城市信息
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   */
  getCityInfo(latitude, longitude) {
    // 这里可以调用腾讯地图API或其他地理编码服务
    // 暂时设置默认值
    this.globalData.cityName = '广州市'
  },

  /**
   * 获取用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善会员资料',
        success: res => {
          console.log('获取用户信息成功:', res)
          this.globalData.userInfo = res.userInfo
          resolve(res.userInfo)
        },
        fail: err => {
          console.error('获取用户信息失败:', err)
          reject(err)
        }
      })
    })
  },

  /**
   * 跳转到美团小程序
   * @param {string} path 美团小程序页面路径
   * @param {object} extraData 额外数据
   */
  navigateToMeituan(path = '', extraData = {}) {
    wx.navigateToMiniProgram({
      appId: 'wxde8ac0a21135c07d', // 美团小程序appId
      path: path,
      extraData: extraData,
      envVersion: 'release',
      success: res => {
        console.log('跳转美团小程序成功:', res)
      },
      fail: err => {
        console.error('跳转美团小程序失败:', err)
        wx.showToast({
          title: '跳转失败，请稍后重试',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    userCode: null,
    location: null,
    cityName: '广州市'
  }
})