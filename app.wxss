/**app.wxss**/
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

/* 容器样式 */
.container {
  padding: 0;
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn {
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-primary {
  background-color: #FF6900;
  color: #fff;
  border: none;
}

.btn-primary:active {
  background-color: #e55a00;
}

.btn-secondary {
  background-color: #fff;
  color: #333;
  border: 1rpx solid #ddd;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 文本样式 */
.text-primary {
  color: #FF6900;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-bold {
  font-weight: 600;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.m-0 { margin: 0; }
.m-1 { margin: 20rpx; }
.m-2 { margin: 40rpx; }

.mt-1 { margin-top: 20rpx; }
.mt-2 { margin-top: 40rpx; }
.mb-1 { margin-bottom: 20rpx; }
.mb-2 { margin-bottom: 40rpx; }
.ml-1 { margin-left: 20rpx; }
.ml-2 { margin-left: 40rpx; }
.mr-1 { margin-right: 20rpx; }
.mr-2 { margin-right: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 20rpx; }
.p-2 { padding: 40rpx; }

.pt-1 { padding-top: 20rpx; }
.pt-2 { padding-top: 40rpx; }
.pb-1 { padding-bottom: 20rpx; }
.pb-2 { padding-bottom: 40rpx; }
.pl-1 { padding-left: 20rpx; }
.pl-2 { padding-left: 40rpx; }
.pr-1 { padding-right: 20rpx; }
.pr-2 { padding-right: 40rpx; }

/* 图片样式 */
.image-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 列表样式 */
.list-item {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
}

.list-item:last-child {
  border-bottom: none;
}

/* 搜索框样式 */
.search-box {
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  color: #333;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.tag-active {
  background-color: #FF6900;
  color: #fff;
}

/* 价格样式 */
.price {
  color: #FF6900;
  font-weight: 600;
}

.price-symbol {
  font-size: 24rpx;
}

.price-integer {
  font-size: 36rpx;
}

.price-decimal {
  font-size: 24rpx;
}

/* 原价样式 */
.original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 24rpx;
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}