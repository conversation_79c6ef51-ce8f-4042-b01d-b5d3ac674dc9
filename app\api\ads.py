"""
广告管理API
处理广告展示、点击统计等
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List, Dict, Any
from datetime import datetime
import json
import os
from app.api.auth import get_current_user

router = APIRouter()

# 广告统计数据存储
ad_impressions = []
ad_clicks = []

class AdService:
    def __init__(self, config_path: str = "ads_config.json"):
        self.config_path = config_path
        self.ads_config = self.load_config()
    
    def load_config(self) -> Dict:
        """加载广告配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 返回默认配置
                return {
                    "ads_config": {
                        "ads": [],
                        "global_settings": {}
                    }
                }
        except Exception as e:
            print(f"加载广告配置失败: {e}")
            return {"ads_config": {"ads": [], "global_settings": {}}}
    
    def get_ads_by_position(
        self, 
        position: str, 
        user_info: Optional[Dict] = None,
        limit: int = 10
    ) -> List[Dict]:
        """根据位置获取广告"""
        ads = self.ads_config.get("ads_config", {}).get("ads", [])
        filtered_ads = []
        
        for ad in ads:
            # 检查基本条件
            if not self._is_ad_valid(ad, position):
                continue
            
            # 检查定向条件
            if user_info and not self._check_targeting(ad, user_info):
                continue
            
            filtered_ads.append(ad)
        
        # 按优先级排序
        filtered_ads.sort(key=lambda x: x.get("priority", 999))
        
        return filtered_ads[:limit]
    
    def _is_ad_valid(self, ad: Dict, position: str) -> bool:
        """检查广告是否有效"""
        # 检查状态
        if ad.get("status") != "active":
            return False
        
        # 检查位置
        if ad.get("position") != position:
            return False
        
        # 检查时间（简化处理）
        return True
    
    def _check_targeting(self, ad: Dict, user_info: Dict) -> bool:
        """检查定向条件（简化处理）"""
        return True

# 全局广告服务实例
ad_service = AdService()

@router.get("/")
async def get_ads(
    position: str = Query(..., description="广告位置"),
    limit: int = Query(10, ge=1, le=50),
    current_user: Optional[dict] = Depends(lambda: None)
):
    """
    获取指定位置的广告
    
    Args:
        position: 广告位置 (home_top, home_middle, product_list等)
        limit: 返回数量限制
        current_user: 当前用户信息（可选）
        
    Returns:
        广告列表
    """
    try:
        user_info = None
        
        # 如果用户已登录，获取用户信息用于广告定向
        if current_user:
            user_info = {
                "city": current_user.get("city"),
                "total_orders": current_user.get("total_orders", 0),
                "age": 25,  # 需要根据实际业务计算
            }
        
        ads = ad_service.get_ads_by_position(position, user_info, limit)
        
        return {
            "code": 0,
            "message": "success",
            "data": {
                "ads": ads,
                "position": position,
                "count": len(ads)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取广告失败: {str(e)}")

@router.post("/{ad_id}/impression")
async def record_impression(
    ad_id: str,
    current_user: Optional[dict] = Depends(lambda: None)
):
    """记录广告展示"""
    try:
        user_id = current_user["id"] if current_user else None
        
        impression_record = {
            "ad_id": ad_id,
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "ip_address": "127.0.0.1",  # 在实际项目中应该获取真实IP
            "user_agent": "MiniProgram"
        }
        
        ad_impressions.append(impression_record)
        
        return {
            "code": 0,
            "message": "记录成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"记录广告展示失败: {str(e)}")

@router.post("/{ad_id}/click")
async def record_click(
    ad_id: str,
    current_user: Optional[dict] = Depends(lambda: None)
):
    """记录广告点击"""
    try:
        user_id = current_user["id"] if current_user else None
        
        click_record = {
            "ad_id": ad_id,
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "ip_address": "127.0.0.1",
            "user_agent": "MiniProgram"
        }
        
        ad_clicks.append(click_record)
        
        return {
            "code": 0,
            "message": "记录成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"记录广告点击失败: {str(e)}")

@router.get("/stats")
async def get_ad_stats(
    ad_id: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """获取广告统计数据"""
    try:
        # 筛选数据
        impressions = ad_impressions
        clicks = ad_clicks
        
        if ad_id:
            impressions = [imp for imp in impressions if imp["ad_id"] == ad_id]
            clicks = [click for click in clicks if click["ad_id"] == ad_id]
        
        # 计算统计数据
        stats = {
            "impressions": len(impressions),
            "clicks": len(clicks),
            "ctr": len(clicks) / len(impressions) if len(impressions) > 0 else 0,
            "unique_users": len(set(imp["user_id"] for imp in impressions if imp["user_id"])),
            "date_range": {
                "start": start_date,
                "end": end_date
            }
        }
        
        return {
            "code": 0,
            "message": "success",
            "data": stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取广告统计失败: {str(e)}")

@router.post("/reload-config")
async def reload_ad_config():
    """重新加载广告配置（管理员接口）"""
    try:
        ad_service.ads_config = ad_service.load_config()
        
        return {
            "code": 0,
            "message": "配置重新加载成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重新加载配置失败: {str(e)}")

@router.get("/positions")
async def get_ad_positions():
    """获取广告位列表"""
    positions = [
        {
            "id": "home_top",
            "name": "首页顶部",
            "description": "首页顶部横幅广告位",
            "max_ads": 1
        },
        {
            "id": "home_middle",
            "name": "首页中部",
            "description": "首页中部推荐位",
            "max_ads": 3
        },
        {
            "id": "product_list",
            "name": "商品列表",
            "description": "商品列表页广告位",
            "max_ads": 2
        },
        {
            "id": "community_top",
            "name": "社区顶部",
            "description": "社区页面顶部广告位",
            "max_ads": 1
        },
        {
            "id": "profile_top",
            "name": "个人中心顶部",
            "description": "个人中心页面顶部",
            "max_ads": 1
        }
    ]
    
    return {
        "code": 0,
        "message": "success",
        "data": positions
    }

@router.get("/config")
async def get_ad_config():
    """获取广告配置（管理员接口）"""
    try:
        return {
            "code": 0,
            "message": "success",
            "data": ad_service.ads_config
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取广告配置失败: {str(e)}")

@router.put("/config")
async def update_ad_config(config: Dict[str, Any]):
    """更新广告配置（管理员接口）"""
    try:
        # 保存配置到文件
        with open(ad_service.config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # 重新加载配置
        ad_service.ads_config = ad_service.load_config()
        
        return {
            "code": 0,
            "message": "配置更新成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新广告配置失败: {str(e)}")
