"""
认证相关API
处理用户登录、注册、Token刷新等
"""

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import Optional
import httpx
import time
from datetime import datetime, timedelta
from jose import JWTError, jwt
from passlib.context import CryptContext

# 模拟数据库存储
users_db = {}
user_id_counter = 1

# JWT配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

router = APIRouter()

# 请求模型
class LoginRequest(BaseModel):
    code: str
    nickName: Optional[str] = None
    avatarUrl: Optional[str] = None
    gender: Optional[int] = 0
    city: Optional[str] = None
    province: Optional[str] = None
    country: Optional[str] = None

class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    user_info: dict

# 工具函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据"
            )
        return user_id
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据"
        )

async def get_openid_by_code(code: str) -> str:
    """通过微信code获取openid（模拟实现）"""
    # 在实际项目中，这里应该调用微信API
    # 这里为了演示，直接返回一个模拟的openid
    return f"mock_openid_{code}_{int(time.time())}"

def create_or_update_user(user_info: LoginRequest, openid: str):
    """创建或更新用户信息"""
    global user_id_counter
    
    # 查找现有用户
    existing_user = None
    for user in users_db.values():
        if user.get("openid") == openid:
            existing_user = user
            break
    
    if existing_user:
        # 更新现有用户信息
        existing_user.update({
            "nickname": user_info.nickName or existing_user.get("nickname"),
            "avatar": user_info.avatarUrl or existing_user.get("avatar"),
            "gender": user_info.gender if user_info.gender is not None else existing_user.get("gender"),
            "city": user_info.city or existing_user.get("city"),
            "province": user_info.province or existing_user.get("province"),
            "country": user_info.country or existing_user.get("country"),
            "last_login_time": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        })
        return existing_user
    else:
        # 创建新用户
        new_user = {
            "id": user_id_counter,
            "openid": openid,
            "nickname": user_info.nickName,
            "avatar": user_info.avatarUrl,
            "gender": user_info.gender or 0,
            "city": user_info.city,
            "province": user_info.province,
            "country": user_info.country,
            "status": True,
            "total_commission": 0.0,
            "total_orders": 0,
            "last_login_time": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        users_db[user_id_counter] = new_user
        user_id_counter += 1
        return new_user

@router.post("/login", response_model=TokenResponse)
async def login(user_info: LoginRequest):
    """
    微信小程序登录
    
    Args:
        user_info: 包含微信code和用户信息的请求体
        
    Returns:
        包含token和用户信息的响应
    """
    try:
        # 通过code获取openid
        openid = await get_openid_by_code(user_info.code)
        if not openid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的登录code"
            )
        
        # 创建或更新用户
        user = create_or_update_user(user_info, openid)
        
        # 生成token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user["id"])},
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user_info": {
                "id": user["id"],
                "nickname": user["nickname"],
                "avatar": user["avatar"],
                "total_commission": user["total_commission"],
                "total_orders": user["total_orders"]
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )

@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(authorization: str = Depends(lambda: None)):
    """刷新访问令牌"""
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少认证信息"
        )
    
    token = authorization.split(" ")[1]
    user_id = verify_token(token)
    
    user = users_db.get(int(user_id))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 生成新的token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user["id"])},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_info": {
            "id": user["id"],
            "nickname": user["nickname"],
            "avatar": user["avatar"],
            "total_commission": user["total_commission"],
            "total_orders": user["total_orders"]
        }
    }

@router.get("/verify")
async def verify_user_token(authorization: str = Depends(lambda: None)):
    """验证用户token"""
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少认证信息"
        )
    
    token = authorization.split(" ")[1]
    user_id = verify_token(token)
    
    user = users_db.get(int(user_id))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return {
        "code": 0,
        "message": "token有效",
        "data": {
            "user_id": user["id"],
            "valid": True
        }
    }

# 依赖函数：获取当前用户
def get_current_user(authorization: str = Depends(lambda: None)):
    """获取当前登录用户"""
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录"
        )
    
    token = authorization.split(" ")[1]
    user_id = verify_token(token)
    
    user = users_db.get(int(user_id))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user
