"""
收藏管理API
处理用户收藏的商品和内容
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from app.api.auth import get_current_user

router = APIRouter()

# 模拟收藏数据存储
favorites_db = {}
favorite_id_counter = 1

# 请求模型
class AddFavoriteRequest(BaseModel):
    target_type: str  # product, content
    target_id: str
    target_data: Dict[Any, Any]

@router.get("/")
async def get_favorites(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    target_type: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """获取收藏列表"""
    try:
        user_id = current_user["id"]
        
        # 筛选当前用户的收藏
        user_favorites = []
        for fav in favorites_db.values():
            if fav["user_id"] == user_id:
                if not target_type or fav["target_type"] == target_type:
                    user_favorites.append(fav)
        
        # 按创建时间倒序排列
        user_favorites.sort(key=lambda x: x["created_at"], reverse=True)
        
        # 分页
        total = len(user_favorites)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        favorites = user_favorites[start_idx:end_idx]
        
        return {
            "code": 0,
            "message": "success",
            "data": {
                "list": favorites,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "pages": (total + limit - 1) // limit
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取收藏列表失败: {str(e)}")

@router.post("/")
async def add_favorite(
    request: AddFavoriteRequest,
    current_user: dict = Depends(get_current_user)
):
    """添加收藏"""
    try:
        global favorite_id_counter
        user_id = current_user["id"]
        
        # 检查是否已收藏
        for fav in favorites_db.values():
            if (fav["user_id"] == user_id and 
                fav["target_type"] == request.target_type and 
                fav["target_id"] == request.target_id):
                raise HTTPException(status_code=400, detail="已经收藏过了")
        
        # 创建收藏记录
        favorite = {
            "id": favorite_id_counter,
            "user_id": user_id,
            "target_type": request.target_type,
            "target_id": request.target_id,
            "target_data": request.target_data,
            "created_at": datetime.now().isoformat()
        }
        
        favorites_db[favorite_id_counter] = favorite
        favorite_id_counter += 1
        
        return {
            "code": 0,
            "message": "收藏成功",
            "data": {
                "id": favorite["id"]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加收藏失败: {str(e)}")

@router.delete("/{favorite_id}")
async def remove_favorite(
    favorite_id: int,
    current_user: dict = Depends(get_current_user)
):
    """取消收藏"""
    try:
        user_id = current_user["id"]
        
        # 查找收藏记录
        favorite = favorites_db.get(favorite_id)
        if not favorite:
            raise HTTPException(status_code=404, detail="收藏不存在")
        
        # 检查权限
        if favorite["user_id"] != user_id:
            raise HTTPException(status_code=403, detail="无权限操作")
        
        # 删除收藏
        del favorites_db[favorite_id]
        
        return {
            "code": 0,
            "message": "取消收藏成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消收藏失败: {str(e)}")

@router.post("/check")
async def check_favorite_status(
    target_type: str,
    target_id: str,
    current_user: dict = Depends(get_current_user)
):
    """检查收藏状态"""
    try:
        user_id = current_user["id"]
        
        # 查找是否已收藏
        for fav in favorites_db.values():
            if (fav["user_id"] == user_id and 
                fav["target_type"] == target_type and 
                fav["target_id"] == target_id):
                return {
                    "code": 0,
                    "message": "success",
                    "data": {
                        "is_favorited": True,
                        "favorite_id": fav["id"]
                    }
                }
        
        return {
            "code": 0,
            "message": "success",
            "data": {
                "is_favorited": False,
                "favorite_id": None
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查收藏状态失败: {str(e)}")

@router.get("/stats")
async def get_favorite_stats(current_user: dict = Depends(get_current_user)):
    """获取收藏统计"""
    try:
        user_id = current_user["id"]
        
        # 统计各类型收藏数量
        stats = {
            "total": 0,
            "product": 0,
            "content": 0
        }
        
        for fav in favorites_db.values():
            if fav["user_id"] == user_id:
                stats["total"] += 1
                if fav["target_type"] in stats:
                    stats[fav["target_type"]] += 1
        
        return {
            "code": 0,
            "message": "success",
            "data": stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取收藏统计失败: {str(e)}")

@router.delete("/batch")
async def batch_remove_favorites(
    favorite_ids: list[int],
    current_user: dict = Depends(get_current_user)
):
    """批量取消收藏"""
    try:
        user_id = current_user["id"]
        removed_count = 0
        
        for favorite_id in favorite_ids:
            favorite = favorites_db.get(favorite_id)
            if favorite and favorite["user_id"] == user_id:
                del favorites_db[favorite_id]
                removed_count += 1
        
        return {
            "code": 0,
            "message": f"成功取消{removed_count}个收藏",
            "data": {
                "removed_count": removed_count
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量取消收藏失败: {str(e)}")

# 初始化一些模拟收藏数据
def init_mock_favorites():
    """初始化模拟收藏数据"""
    global favorite_id_counter
    
    mock_favorites = [
        {
            "id": 1,
            "user_id": 1,
            "target_type": "product",
            "target_id": "prod_001",
            "target_data": {
                "name": "肯德基 香辣鸡腿堡套餐",
                "image": "https://example.com/images/kfc_burger.jpg",
                "price": 29.9,
                "original_price": 39.9
            },
            "created_at": "2024-01-10T10:00:00"
        },
        {
            "id": 2,
            "user_id": 1,
            "target_type": "product", 
            "target_id": "prod_003",
            "target_data": {
                "name": "星巴克 拿铁咖啡",
                "image": "https://example.com/images/starbucks_latte.jpg",
                "price": 28.0,
                "original_price": 35.0
            },
            "created_at": "2024-01-09T15:30:00"
        }
    ]
    
    for fav in mock_favorites:
        favorites_db[fav["id"]] = fav
        if fav["id"] >= favorite_id_counter:
            favorite_id_counter = fav["id"] + 1

# 初始化模拟数据
init_mock_favorites()
