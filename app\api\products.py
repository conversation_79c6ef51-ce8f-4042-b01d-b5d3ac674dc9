"""
商品管理API
处理商品列表、详情、推广链接生成等
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
import random
from app.api.auth import get_current_user

router = APIRouter()

# 模拟商品数据
mock_products = [
    {
        "id": "prod_001",
        "name": "肯德基 香辣鸡腿堡套餐",
        "image": "https://example.com/images/kfc_burger.jpg",
        "price": 29.9,
        "original_price": 39.9,
        "sales": 12000,
        "rating": 4.8,
        "tags": ["限时特价", "人气爆款"],
        "category": "food",
        "brand": "肯德基",
        "description": "香辣鸡腿堡+薯条+可乐，经典搭配",
        "commission_rate": 0.05,
        "stock": 999,
        "location": "全国门店"
    },
    {
        "id": "prod_002", 
        "name": "麦当劳 巨无霸套餐",
        "image": "https://example.com/images/mcd_bigmac.jpg",
        "price": 32.9,
        "original_price": 42.9,
        "sales": 8956,
        "rating": 4.7,
        "tags": ["经典口味"],
        "category": "food",
        "brand": "麦当劳",
        "description": "经典巨无霸汉堡套餐",
        "commission_rate": 0.04,
        "stock": 888,
        "location": "全国门店"
    },
    {
        "id": "prod_003",
        "name": "星巴克 拿铁咖啡",
        "image": "https://example.com/images/starbucks_latte.jpg", 
        "price": 28.0,
        "original_price": 35.0,
        "sales": 5432,
        "rating": 4.9,
        "tags": ["精选咖啡", "限时优惠"],
        "category": "food",
        "brand": "星巴克",
        "description": "香浓拿铁咖啡，精选咖啡豆",
        "commission_rate": 0.06,
        "stock": 666,
        "location": "全国门店"
    },
    {
        "id": "prod_004",
        "name": "必胜客 至尊披萨",
        "image": "https://example.com/images/pizzahut_supreme.jpg",
        "price": 68.0,
        "original_price": 88.0,
        "sales": 3210,
        "rating": 4.6,
        "tags": ["大份量", "家庭分享"],
        "category": "food", 
        "brand": "必胜客",
        "description": "至尊披萨，丰富配料",
        "commission_rate": 0.08,
        "stock": 555,
        "location": "全国门店"
    },
    {
        "id": "prod_005",
        "name": "海底捞 火锅套餐",
        "image": "https://example.com/images/haidilao_hotpot.jpg",
        "price": 128.0,
        "original_price": 158.0,
        "sales": 2156,
        "rating": 4.9,
        "tags": ["火锅", "聚餐首选"],
        "category": "food",
        "brand": "海底捞",
        "description": "经典火锅套餐，2-3人份",
        "commission_rate": 0.10,
        "stock": 333,
        "location": "全国门店"
    }
]

# 请求模型
class PromotionLinkRequest(BaseModel):
    sid: Optional[str] = None

@router.get("/list")
async def get_products(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = None,
    keyword: Optional[str] = None,
    city_id: Optional[int] = None,
    sort_by: Optional[str] = Query("recommend", regex="^(recommend|price_asc|price_desc|sales)$")
):
    """
    获取商品列表
    
    Args:
        page: 页码
        limit: 每页数量
        category: 分类筛选
        keyword: 搜索关键词
        city_id: 城市ID
        sort_by: 排序方式 (recommend|price_asc|price_desc|sales)
        
    Returns:
        商品列表数据
    """
    try:
        # 筛选商品
        filtered_products = mock_products.copy()
        
        # 分类筛选
        if category and category != "recommend":
            filtered_products = [p for p in filtered_products if p["category"] == category]
        
        # 关键词搜索
        if keyword:
            keyword = keyword.lower()
            filtered_products = [
                p for p in filtered_products 
                if keyword in p["name"].lower() or keyword in p["brand"].lower()
            ]
        
        # 排序
        if sort_by == "price_asc":
            filtered_products.sort(key=lambda x: x["price"])
        elif sort_by == "price_desc":
            filtered_products.sort(key=lambda x: x["price"], reverse=True)
        elif sort_by == "sales":
            filtered_products.sort(key=lambda x: x["sales"], reverse=True)
        else:  # recommend
            # 推荐排序：随机打乱
            random.shuffle(filtered_products)
        
        # 分页
        total = len(filtered_products)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        products = filtered_products[start_idx:end_idx]
        
        return {
            "code": 0,
            "message": "success",
            "data": {
                "list": products,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "pages": (total + limit - 1) // limit
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取商品列表失败: {str(e)}")

@router.get("/{product_id}")
async def get_product_detail(product_id: str):
    """获取商品详情"""
    try:
        # 查找商品
        product = None
        for p in mock_products:
            if p["id"] == product_id:
                product = p.copy()
                break
        
        if not product:
            raise HTTPException(status_code=404, detail="商品不存在")
        
        # 添加详细信息
        product.update({
            "detail_images": [
                f"https://example.com/images/{product_id}_detail1.jpg",
                f"https://example.com/images/{product_id}_detail2.jpg",
                f"https://example.com/images/{product_id}_detail3.jpg"
            ],
            "specifications": {
                "保质期": "当日制作",
                "配送": "门店自取/外卖配送",
                "服务": "7天无理由退款"
            },
            "reviews": [
                {
                    "user": "用户***1",
                    "rating": 5,
                    "content": "味道很好，分量足够",
                    "time": "2024-01-10"
                },
                {
                    "user": "用户***2", 
                    "rating": 4,
                    "content": "性价比不错，推荐",
                    "time": "2024-01-09"
                }
            ]
        })
        
        return {
            "code": 0,
            "message": "success",
            "data": product
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取商品详情失败: {str(e)}")

@router.post("/{product_id}/promotion-link")
async def generate_promotion_link(
    product_id: str,
    request: PromotionLinkRequest,
    current_user: dict = Depends(get_current_user)
):
    """生成推广链接"""
    try:
        # 查找商品
        product = None
        for p in mock_products:
            if p["id"] == product_id:
                product = p
                break
        
        if not product:
            raise HTTPException(status_code=404, detail="商品不存在")
        
        # 生成推广链接（模拟）
        # 在实际项目中，这里应该调用美团联盟API生成真实的推广链接
        promotion_url = f"https://union.meituan.com/promotion?product_id={product_id}&user_id={current_user['id']}&sid={request.sid or ''}"
        
        # 记录推广行为（在实际项目中应该保存到数据库）
        promotion_record = {
            "user_id": current_user["id"],
            "product_id": product_id,
            "product_name": product["name"],
            "promotion_url": promotion_url,
            "created_at": datetime.now().isoformat()
        }
        
        return {
            "code": 0,
            "message": "推广链接生成成功",
            "data": {
                "promotion_url": promotion_url,
                "product_info": {
                    "id": product["id"],
                    "name": product["name"],
                    "price": product["price"],
                    "commission_rate": product["commission_rate"]
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成推广链接失败: {str(e)}")

@router.get("/categories/list")
async def get_categories():
    """获取商品分类列表"""
    categories = [
        {
            "id": "recommend",
            "name": "为你推荐",
            "icon": "recommend",
            "sort": 1
        },
        {
            "id": "food",
            "name": "美食",
            "icon": "food",
            "sort": 2
        },
        {
            "id": "takeout",
            "name": "外卖",
            "icon": "takeout", 
            "sort": 3
        },
        {
            "id": "hotel",
            "name": "酒店",
            "icon": "hotel",
            "sort": 4
        },
        {
            "id": "travel",
            "name": "旅游",
            "icon": "travel",
            "sort": 5
        }
    ]
    
    return {
        "code": 0,
        "message": "success",
        "data": categories
    }

@router.get("/brands/list")
async def get_brands():
    """获取品牌列表"""
    brands = [
        {
            "id": "kfc",
            "name": "肯德基",
            "logo": "https://example.com/logos/kfc.png",
            "product_count": 156
        },
        {
            "id": "mcd",
            "name": "麦当劳", 
            "logo": "https://example.com/logos/mcd.png",
            "product_count": 128
        },
        {
            "id": "starbucks",
            "name": "星巴克",
            "logo": "https://example.com/logos/starbucks.png",
            "product_count": 89
        },
        {
            "id": "pizzahut",
            "name": "必胜客",
            "logo": "https://example.com/logos/pizzahut.png",
            "product_count": 67
        },
        {
            "id": "haidilao",
            "name": "海底捞",
            "logo": "https://example.com/logos/haidilao.png",
            "product_count": 45
        }
    ]
    
    return {
        "code": 0,
        "message": "success",
        "data": brands
    }
