"""
用户管理API
处理用户信息查询、更新、统计等
"""

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from app.api.auth import get_current_user, users_db

router = APIRouter()

# 请求模型
class UserUpdateRequest(BaseModel):
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    phone: Optional[str] = None
    gender: Optional[int] = None
    city: Optional[str] = None
    province: Optional[str] = None
    country: Optional[str] = None

# 响应模型
class UserProfileResponse(BaseModel):
    id: int
    nickname: Optional[str]
    avatar: Optional[str]
    phone: Optional[str]
    gender: int
    city: Optional[str]
    province: Optional[str]
    country: Optional[str]
    status: bool
    total_commission: float
    total_orders: int
    created_at: str

@router.get("/profile")
async def get_user_profile(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "code": 0,
        "message": "success",
        "data": {
            "id": current_user["id"],
            "nickname": current_user.get("nickname"),
            "avatar": current_user.get("avatar"),
            "phone": current_user.get("phone"),
            "gender": current_user.get("gender", 0),
            "city": current_user.get("city"),
            "province": current_user.get("province"),
            "country": current_user.get("country"),
            "status": current_user.get("status", True),
            "total_commission": current_user.get("total_commission", 0.0),
            "total_orders": current_user.get("total_orders", 0),
            "created_at": current_user.get("created_at")
        }
    }

@router.put("/profile")
async def update_user_profile(
    user_update: UserUpdateRequest,
    current_user: dict = Depends(get_current_user)
):
    """更新用户信息"""
    user_id = current_user["id"]
    user = users_db.get(user_id)
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 更新用户信息
    update_data = user_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if value is not None:
            user[field] = value
    
    user["updated_at"] = datetime.now().isoformat()
    
    return {
        "code": 0,
        "message": "更新成功",
        "data": {
            "id": user["id"],
            "nickname": user.get("nickname"),
            "avatar": user.get("avatar"),
            "phone": user.get("phone"),
            "gender": user.get("gender", 0),
            "city": user.get("city"),
            "province": user.get("province"),
            "country": user.get("country"),
            "status": user.get("status", True),
            "total_commission": user.get("total_commission", 0.0),
            "total_orders": user.get("total_orders", 0),
            "updated_at": user.get("updated_at")
        }
    }

@router.get("/stats")
async def get_user_stats(current_user: dict = Depends(get_current_user)):
    """获取用户统计数据"""
    user_id = current_user["id"]
    
    # 模拟统计数据计算
    # 在实际项目中，这里应该查询数据库计算真实统计数据
    stats = {
        "total_commission": current_user.get("total_commission", 0.0),
        "total_orders": current_user.get("total_orders", 0),
        "month_commission": round(current_user.get("total_commission", 0.0) * 0.3, 2),
        "month_orders": max(0, current_user.get("total_orders", 0) - 5),
        "conversion_rate": 0.15,  # 15%转化率
        "favorite_count": 8,  # 收藏数量
        "browse_count": 156,  # 浏览次数
        "share_count": 23,  # 分享次数
        "level": "普通用户",
        "next_level": "银牌用户",
        "level_progress": 0.6  # 升级进度60%
    }
    
    return {
        "code": 0,
        "message": "success",
        "data": stats
    }

@router.get("/commission-history")
async def get_commission_history(
    page: int = 1,
    limit: int = 20,
    current_user: dict = Depends(get_current_user)
):
    """获取佣金历史记录"""
    # 模拟佣金历史数据
    # 在实际项目中，这里应该查询数据库获取真实的佣金记录
    mock_records = []
    for i in range(min(limit, 10)):  # 最多返回10条模拟数据
        record = {
            "id": (page - 1) * limit + i + 1,
            "order_id": f"MT{datetime.now().strftime('%Y%m%d')}{1000 + i}",
            "product_name": f"美团商品 {i + 1}",
            "order_amount": round(50.0 + i * 10.5, 2),
            "commission_rate": 0.05,  # 5%佣金率
            "commission_amount": round((50.0 + i * 10.5) * 0.05, 2),
            "status": "已结算" if i % 3 == 0 else "待结算",
            "order_time": datetime.now().isoformat(),
            "settle_time": datetime.now().isoformat() if i % 3 == 0 else None
        }
        mock_records.append(record)
    
    return {
        "code": 0,
        "message": "success",
        "data": {
            "list": mock_records,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": 50,  # 模拟总数
                "pages": 3
            }
        }
    }

@router.get("/browse-history")
async def get_browse_history(
    page: int = 1,
    limit: int = 20,
    current_user: dict = Depends(get_current_user)
):
    """获取浏览历史"""
    # 模拟浏览历史数据
    mock_history = []
    for i in range(min(limit, 8)):
        history = {
            "id": (page - 1) * limit + i + 1,
            "product_id": f"prod_{1000 + i}",
            "product_name": f"热门商品 {i + 1}",
            "product_image": f"https://example.com/images/product{i + 1}.jpg",
            "price": round(30.0 + i * 5.5, 2),
            "original_price": round(40.0 + i * 7.5, 2),
            "browse_time": datetime.now().isoformat(),
            "category": "美食" if i % 2 == 0 else "外卖"
        }
        mock_history.append(history)
    
    return {
        "code": 0,
        "message": "success",
        "data": {
            "list": mock_history,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": 30,
                "pages": 2
            }
        }
    }

@router.delete("/browse-history")
async def clear_browse_history(current_user: dict = Depends(get_current_user)):
    """清空浏览历史"""
    # 在实际项目中，这里应该删除数据库中的浏览记录
    return {
        "code": 0,
        "message": "浏览历史已清空"
    }

@router.post("/feedback")
async def submit_feedback(
    content: str,
    contact: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """提交用户反馈"""
    # 在实际项目中，这里应该保存反馈到数据库
    feedback_data = {
        "id": int(datetime.now().timestamp()),
        "user_id": current_user["id"],
        "content": content,
        "contact": contact,
        "status": "待处理",
        "created_at": datetime.now().isoformat()
    }
    
    return {
        "code": 0,
        "message": "反馈提交成功，我们会尽快处理",
        "data": {
            "feedback_id": feedback_data["id"]
        }
    }
