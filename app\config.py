"""
应用配置文件
使用pydantic-settings管理配置
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "美团联盟小程序API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 数据库配置
    DATABASE_PATH: str = "data/database.db"
    DATABASE_URL: Optional[str] = None
    
    # JWT配置
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 美团联盟API配置
    MEITUAN_APP_KEY: str = ""
    MEITUAN_APP_SECRET: str = ""
    MEITUAN_BASE_URL: str = "https://openapi.meituan.com"
    
    # 微信小程序配置
    WECHAT_APP_ID: str = ""
    WECHAT_APP_SECRET: str = ""
    WECHAT_API_BASE_URL: str = "https://api.weixin.qq.com"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "data/logs/app.log"
    
    # 广告配置
    ADS_CONFIG_PATH: str = "ads_config.json"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # CORS配置
    ALLOWED_ORIGINS: list = ["*"]
    
    # 文件上传配置
    UPLOAD_DIR: str = "data/uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 缓存配置
    CACHE_EXPIRE_TIME: int = 300  # 5分钟
    
    # 限流配置
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # 60秒
    
    @property
    def database_url(self) -> str:
        """获取数据库URL"""
        if self.DATABASE_URL:
            return self.DATABASE_URL
        return f"sqlite:///{self.DATABASE_PATH}"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 确保必要的目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            "data",
            "data/logs",
            "data/uploads",
            os.path.dirname(self.DATABASE_PATH) if "/" in self.DATABASE_PATH else "data"
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# 创建全局配置实例
settings = Settings()

# 开发环境配置
class DevelopmentSettings(Settings):
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"

# 生产环境配置
class ProductionSettings(Settings):
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    ALLOWED_ORIGINS: list = ["https://your-domain.com"]

# 测试环境配置
class TestingSettings(Settings):
    DEBUG: bool = True
    DATABASE_PATH: str = "data/test_database.db"
    LOG_LEVEL: str = "DEBUG"

def get_settings() -> Settings:
    """根据环境变量获取配置"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()

# 根据环境获取配置
settings = get_settings()
