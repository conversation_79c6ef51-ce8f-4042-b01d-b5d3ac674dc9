"""
美团联盟小程序后端API服务
基于FastAPI + SQLite构建
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 创建FastAPI应用实例
app = FastAPI(
    title="美团联盟小程序API",
    description="基于Python + SQLite的后端API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 跨域中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    print(f"{request.method} {request.url} - {response.status_code} - {process_time:.4f}s")
    
    return response

# 根路径
@app.get("/")
async def root():
    """API根路径"""
    return {
        "message": "美团联盟小程序API服务",
        "version": "1.0.0",
        "status": "running",
        "timestamp": int(time.time())
    }

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": int(time.time())
    }

# API信息
@app.get("/api/info")
async def api_info():
    """API信息接口"""
    return {
        "name": "美团联盟小程序API",
        "version": "1.0.0",
        "description": "基于Python + SQLite的后端API服务",
        "tech_stack": {
            "framework": "FastAPI",
            "database": "SQLite",
            "orm": "SQLAlchemy",
            "auth": "JWT"
        },
        "endpoints": {
            "auth": "/api/auth",
            "users": "/api/users",
            "products": "/api/products",
            "content": "/api/content",
            "favorites": "/api/favorites",
            "ads": "/api/ads"
        }
    }

# 示例API路由
@app.get("/api/test")
async def test_api():
    """测试API接口"""
    return {
        "code": 0,
        "message": "API测试成功",
        "data": {
            "server_time": int(time.time()),
            "database": "SQLite",
            "status": "connected"
        }
    }

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "服务器内部错误",
            "detail": str(exc) if app.debug else "Internal server error",
            "timestamp": int(time.time())
        }
    )

# HTTP异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.detail,
            "timestamp": int(time.time())
        }
    )

# 这里可以注册其他路由
# from app.api import auth, users, products, content, favorites, ads
# app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
# app.include_router(users.router, prefix="/api/users", tags=["用户"])
# app.include_router(products.router, prefix="/api/products", tags=["商品"])
# app.include_router(content.router, prefix="/api/content", tags=["内容"])
# app.include_router(favorites.router, prefix="/api/favorites", tags=["收藏"])
# app.include_router(ads.router, prefix="/api/ads", tags=["广告"])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
