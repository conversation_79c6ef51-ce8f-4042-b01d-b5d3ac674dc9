# 微信小程序核心功能分析（基于美团联盟）

**开发平台**：微信小程序

**核心可行性**：通过美团联盟API获取推广物料，并在小程序内生成推广链接，最终利用 `wx.navigateToMiniProgram` API跳转至美团官方小程序完成交易闭环，此核心链路完全可行。


## 一、首页

**定位**：作为用户首屏入口，承担“流量聚合+活动转化”核心功能，融合社区与电商属性，强化用户留存与转化。

### 头部区域
*   **场景化信息**：左上角显示用户地理位置（如：广州市），增强页面代入感。
*   **搜索功能**：页面顶部提供关键词搜索框，引导用户主动搜索商品或店铺。
*   **活动引流**：
    *   提供“分享”等按钮，通过社交裂变促进拉新。
*   **品牌曝光**：以按钮形式集中展示合作品牌（如：肯德基、麦当劳、星巴克），强化品牌认知，并作为分类/品牌页的快捷入口。

### 中间区域
*   **核心广告位**：通过“新用户专享 下单返现金”等活动位，直接触达用户，促进转化。
*   **分类导航**：设置“为你推荐”、“美食”、“外卖商品券”、“休闲娱乐”等标签，帮助用户根据场景快速筛选，提升浏览效率。
*   **商品列表**：
    *   以信息流形式展示“今日必抢”或推荐商品。
    *   内容包括商品图、名称、价格，并用“抢”等强引导词按钮刺激用户购买欲。
    *   根据用户浏览习惯推荐个性化商品。

### 底部导航栏
*   **核心入口**：固定展示四大核心入口：【首页】、【社区】、【专题】、【我的】，保障全局操作的一致性和流畅性。

### API 实现策略（基于美团联盟 & 微信小程序）
*   **地理位置获取**：在小程序中调用 `wx.getLocation` API获取用户经纬度，用于请求区域化推广物料。
*   **分享功能**：通过小程序的 `onShareAppMessage` 事件，自定义分享内容（标题、图片），实现社交裂变。
*   **搜索与商品列表**：
    *   后端服务调用美团联盟的 **推广物料API** 获取商品数据。
    *   用户点击商品时，后端服务调用联盟的 **转链API** 生成推广链接（包含小程序路径 `path` 和 `appId`）。
    *   小程序前端调用 `wx.navigateToMiniProgram`，传入从后端获取的推广参数，跳转到美团官方小程序完成购买。
*   **活动/品牌/分类**：信息来源于联盟 **推广物料API**，由后端进行数据聚合。活动位可配置为跳转到指定的推广页面。
*   **商品信息**：通过 **推广物料API** 获得。

---

## 二、商品列表页（分类/品牌二级页）

**定位**：承接首页的分类或品牌点击，以列表形式清晰展示商品，聚焦于用户的浏览和购买决策。

### 页面布局
*   **顶部导航**：包含“全部新品”、“美食餐饮”、“外卖”等细分品类，方便用户进一步筛选。
*   **商品列表**：
    *   纵向展示商品列表，信息结构清晰。
    *   每个商品项包含：图片、名称、价格、已售数量等信息。
    *   右侧统一设置红色的“抢”按钮，视觉突出，方便用户快速下单。

### 底部导航栏
*   **核心入口**：与其它页面保持一致，固定展示【首页】、【社区】、【专题】、【我的】四大核心入口。

### API 实现策略（基于美团联盟 & 微信小程序）
*   **小程序用户系统**：通过 `wx.login` 获取用户 `code`，发送至自建后端换取 `openid`，以此建立小程序自身的用户体系。可进一步引导用户授权 `wx.getUserProfile` 获取头像、昵称，用于小程序内部展示。
*   **美团相关功能**：
    *   **用户信息**：**无法获取美团账户信息**。小程序用户系统与美团账户系统独立。
    *   **资产权益**：**无法实现**。无法获取用户在美团的资产。
    *   **推广订单管理**：可调用联盟 **订单查询API**，查询通过本小程序产生的推广订单，用于后台数据分析或展示给推广员。
*   **自建服务**：
    *   **收藏/浏览记录**：**需要自建**。基于小程序自身的用户系统，在自建后端记录用户的收藏和浏览行为。
    *   **卡券/客服/地址管理**：**均无法实现**，这些是美团的平台功能。

### API 实现策略（基于美团联盟 & 微信小程序）
*   **UGC内容**：**完全自建**。社区功能（内容发布、展示、互动）需要自建后端服务，并利用小程序的用户系统（通过 `wx.login` 实现）来管理用户身份。
*   **内容内嵌商品**：
    1.  后端通过联盟 **推广物料API** 获取商品，供用户在发布内容时选择。
    2.  后端调用 **转链API** 生成带小程序路径的推广链接。
    3.  小程序前端渲染内容，当用户点击商品时，调用 `wx.navigateToMiniProgram` 跳转至美团小程序。


### API 实现策略（基于美团联盟 & 微信小程序）
*   **细分品类**：后端从联盟 **推广物料API** 的返回数据中解析、聚合出分类信息。
*   **商品列表**：后端调用 **推广物料API**，支持前端按分类、关键词分页拉取数据。
*   **下单操作**：用户点击“抢”按钮后，小程序获取商品信息，请求后端生成带小程序路径的推广链接，然后调用 `wx.navigateToMiniProgram` 跳转到美团小程序完成购买。

---

## 三、内容聚合页

**定位**：通过对接微信公众号优质内容，构建轻量级内容消费场景。

### 核心功能
*   **文章调用**：
    *   定期同步指定微信公众号的精选文章（需公众号运营方授权）
    *   文章展示包含封面图、标题和摘要
*   **跳转逻辑**：用户点击文章后，直接跳转到该公众号的原始文章页面
*   **分类筛选**：保留"推荐"、"美食"、"旅行"基础分类标签

**技术实现**：
*   通过微信公众平台API获取授权公众号文章列表
*   使用 `wx.navigateToMiniProgram` 跳转到公众号文章页

## 四、我的（个人中心）

**定位**：聚合用户的个人信息、资产、订单与服务，强化用户的归属感与长期留存。

### 头部区域
*   **用户信息**：展示用户头像和昵称。
*   **资产权益**：集中展示用户的“红包”、“代金券”、“余额”等资产信息，强化优惠感知，刺激消费。
*   优惠券：`pages/coupon/index`
*   余额：`pages/wallet/index`  
*   积分：`pages/points/mall`

### 中间区域
*   **订单管理**：提供“订单中心”等入口，方便用户查看和管理自己的订单。
*   **核心服务**：提供“卡券”等核心服务功能入口。

### 底部导航栏
*   **核心入口**：与其它页面保持一致，固定展示【首页】、【社区】、【专题】、【我的】四大核心入口。
