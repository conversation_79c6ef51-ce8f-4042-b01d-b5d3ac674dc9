#!/usr/bin/env python3
"""
美团联盟小程序部署脚本
自动化部署和环境检查
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python版本过低，需要Python 3.9+")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        "requirements.txt",
        "app/main.py",
        "app/config.py",
        ".env.example",
        "ads_config.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件存在")
    return True

def create_directories():
    """创建必要目录"""
    directories = [
        "data",
        "data/logs",
        "data/uploads",
        "logs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def setup_environment():
    """设置环境变量"""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ 创建.env文件（从.env.example复制）")
            print("⚠️  请编辑.env文件配置相关参数")
        else:
            print("❌ .env.example文件不存在")
            return False
    else:
        print("✅ .env文件已存在")
    
    return True

def install_dependencies():
    """安装Python依赖"""
    try:
        print("📦 安装Python依赖...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def init_database():
    """初始化数据库"""
    try:
        if os.path.exists("scripts/init_db.py"):
            print("🗄️ 初始化数据库...")
            subprocess.run([sys.executable, "scripts/init_db.py"], check=True)
            print("✅ 数据库初始化完成")
        else:
            print("⚠️  数据库初始化脚本不存在，跳过")
        return True
    except subprocess.CalledProcessError:
        print("❌ 数据库初始化失败")
        return False

def check_config():
    """检查配置文件"""
    try:
        # 检查广告配置
        with open("ads_config.json", "r", encoding="utf-8") as f:
            ads_config = json.load(f)
            if "ads_config" in ads_config:
                print("✅ 广告配置文件格式正确")
            else:
                print("⚠️  广告配置文件格式可能有问题")
        
        return True
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False

def start_server(mode="development"):
    """启动服务器"""
    try:
        if mode == "development":
            print("🚀 启动开发服务器...")
            cmd = [sys.executable, "-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
        else:
            print("🚀 启动生产服务器...")
            cmd = [sys.executable, "-m", "gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n⏹️  服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
美团联盟小程序部署脚本

用法:
    python deploy.py [命令]

命令:
    check       - 检查环境和依赖
    setup       - 设置环境（创建目录、配置文件等）
    install     - 安装依赖
    init        - 初始化数据库
    dev         - 启动开发服务器
    prod        - 启动生产服务器
    all         - 执行完整部署流程
    help        - 显示此帮助信息

示例:
    python deploy.py check      # 检查环境
    python deploy.py setup      # 设置环境
    python deploy.py dev        # 启动开发服务器
    python deploy.py all        # 完整部署
    """
    print(help_text)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == "help":
        show_help()
    
    elif command == "check":
        print("🔍 检查环境...")
        success = True
        success &= check_python_version()
        success &= check_files()
        
        if success:
            print("\n✅ 环境检查通过")
        else:
            print("\n❌ 环境检查失败")
            sys.exit(1)
    
    elif command == "setup":
        print("⚙️  设置环境...")
        create_directories()
        setup_environment()
        print("\n✅ 环境设置完成")
    
    elif command == "install":
        print("📦 安装依赖...")
        if install_dependencies():
            print("\n✅ 依赖安装完成")
        else:
            print("\n❌ 依赖安装失败")
            sys.exit(1)
    
    elif command == "init":
        print("🗄️ 初始化数据库...")
        if init_database():
            print("\n✅ 数据库初始化完成")
        else:
            print("\n❌ 数据库初始化失败")
            sys.exit(1)
    
    elif command == "dev":
        start_server("development")
    
    elif command == "prod":
        start_server("production")
    
    elif command == "all":
        print("🚀 开始完整部署流程...")
        
        # 检查环境
        print("\n1️⃣ 检查环境...")
        if not (check_python_version() and check_files()):
            print("❌ 环境检查失败")
            sys.exit(1)
        
        # 设置环境
        print("\n2️⃣ 设置环境...")
        create_directories()
        if not setup_environment():
            print("❌ 环境设置失败")
            sys.exit(1)
        
        # 安装依赖
        print("\n3️⃣ 安装依赖...")
        if not install_dependencies():
            print("❌ 依赖安装失败")
            sys.exit(1)
        
        # 初始化数据库
        print("\n4️⃣ 初始化数据库...")
        if not init_database():
            print("❌ 数据库初始化失败")
            sys.exit(1)
        
        # 检查配置
        print("\n5️⃣ 检查配置...")
        if not check_config():
            print("❌ 配置检查失败")
            sys.exit(1)
        
        print("\n🎉 部署完成！")
        print("\n下一步:")
        print("1. 编辑 .env 文件配置API密钥")
        print("2. 运行 'python deploy.py dev' 启动开发服务器")
        print("3. 访问 http://localhost:8000/docs 查看API文档")
    
    else:
        print(f"❌ 未知命令: {command}")
        show_help()
        sys.exit(1)

if __name__ == "__main__":
    main()
