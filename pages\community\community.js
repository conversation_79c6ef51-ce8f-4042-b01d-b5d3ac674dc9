// pages/community/community.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentCategory: 'recommend',
    loading: false,
    hasMore: true,
    loadMoreText: '上拉加载更多',
    
    // 分类数据
    categories: [
      { id: 'recommend', name: '推荐' },
      { id: 'food', name: '美食' },
      { id: 'travel', name: '旅行' },
      { id: 'lifestyle', name: '生活' },
      { id: 'shopping', name: '购物' }
    ],
    
    // 文章数据
    articles: [
      {
        id: 1,
        title: '探店｜这家肯德基新品太好吃了！',
        summary: '今天去试了肯德基的新品香辣鸡腿堡，真的超级好吃！鸡腿很嫩，酱料也很香，强烈推荐大家去试试～',
        author: {
          id: 1,
          name: '美食探店小达人',
          avatar: '/images/avatars/avatar1.jpg',
          isFollowed: false
        },
        publishTime: '2小时前',
        images: [
          '/images/articles/article1-1.jpg',
          '/images/articles/article1-2.jpg',
          '/images/articles/article1-3.jpg'
        ],
        product: {
          id: 1,
          name: '肯德基 香辣鸡腿堡套餐',
          image: '/images/products/product1.jpg',
          price: '29.9',
          originalPrice: '39.9'
        },
        tags: ['美食推荐', '肯德基', '新品'],
        likeCount: 128,
        commentCount: 32,
        isLiked: false
      },
      {
        id: 2,
        title: '星巴克限定咖啡测评',
        summary: '最近星巴克出了几款限定咖啡，作为咖啡爱好者必须要去试试！今天测评了三款，给大家分享一下感受～',
        author: {
          id: 2,
          name: '咖啡控小姐姐',
          avatar: '/images/avatars/avatar2.jpg',
          isFollowed: true
        },
        publishTime: '5小时前',
        images: [
          '/images/articles/article2-1.jpg',
          '/images/articles/article2-2.jpg'
        ],
        product: {
          id: 3,
          name: '星巴克 拿铁咖啡',
          image: '/images/products/product3.jpg',
          price: '28.0',
          originalPrice: '35.0'
        },
        tags: ['咖啡', '星巴克', '测评'],
        likeCount: 89,
        commentCount: 15,
        isLiked: true
      },
      {
        id: 3,
        title: '周末和朋友聚餐的好去处',
        summary: '推荐一家超棒的餐厅，环境很好，菜品也很不错，价格也很实惠，和朋友聚餐的首选！',
        author: {
          id: 3,
          name: '生活记录者',
          avatar: '/images/avatars/avatar3.jpg',
          isFollowed: false
        },
        publishTime: '1天前',
        images: [
          '/images/articles/article3-1.jpg'
        ],
        tags: ['聚餐', '推荐', '生活'],
        likeCount: 56,
        commentCount: 8,
        isLiked: false
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadArticles()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreArticles()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '发现精彩内容',
      path: '/pages/community/community',
      imageUrl: '/images/share-community.jpg'
    }
  },

  /**
   * 刷新数据
   */
  refreshData() {
    wx.showNavigationBarLoading()
    
    setTimeout(() => {
      this.loadArticles()
      wx.hideNavigationBarLoading()
      wx.stopPullDownRefresh()
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    }, 1000)
  },

  /**
   * 加载文章数据
   */
  loadArticles() {
    this.setData({
      loading: true
    })
    
    // 这里应该调用后端API获取文章数据
    // 可以根据分类和用户偏好获取内容
    setTimeout(() => {
      this.setData({
        loading: false
      })
    }, 1000)
  },

  /**
   * 加载更多文章
   */
  loadMoreArticles() {
    if (!this.data.hasMore || this.data.loading) return
    
    this.setData({
      loading: true,
      loadMoreText: '加载中...'
    })
    
    // 模拟加载更多数据
    setTimeout(() => {
      const newArticles = [
        {
          id: Date.now(),
          title: '新发现的美食好去处',
          summary: '今天发现了一家很棒的餐厅，分享给大家～',
          author: {
            id: 4,
            name: '美食发现家',
            avatar: '/images/avatars/avatar4.jpg',
            isFollowed: false
          },
          publishTime: '刚刚',
          images: ['/images/articles/article4-1.jpg'],
          tags: ['美食', '发现'],
          likeCount: 12,
          commentCount: 3,
          isLiked: false
        }
      ]
      
      this.setData({
        articles: [...this.data.articles, ...newArticles],
        loading: false,
        loadMoreText: '上拉加载更多'
      })
    }, 1000)
  },

  /**
   * 分类点击事件
   * @param {Object} e 事件对象
   */
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category
    
    this.setData({
      currentCategory: category.id
    })
    
    this.loadArticlesByCategory(category.id)
  },

  /**
   * 根据分类加载文章
   * @param {string} categoryId 分类ID
   */
  loadArticlesByCategory(categoryId) {
    console.log('根据分类加载文章:', categoryId)
    this.loadArticles()
  },

  /**
   * 文章点击事件
   * @param {Object} e 事件对象
   */
  onArticleTap(e) {
    const article = e.currentTarget.dataset.article
    
    wx.navigateTo({
      url: `/pages/article-detail/article-detail?articleId=${article.id}`
    })
  },

  /**
   * 关注按钮点击事件
   * @param {Object} e 事件对象
   */
  onFollowTap(e) {
    const author = e.currentTarget.dataset.author
    const articles = this.data.articles.map(article => {
      if (article.author.id === author.id) {
        article.author.isFollowed = !article.author.isFollowed
      }
      return article
    })
    
    this.setData({ articles })
    
    wx.showToast({
      title: author.isFollowed ? '取消关注' : '关注成功',
      icon: 'success',
      duration: 1500
    })
  },

  /**
   * 图片点击事件
   * @param {Object} e 事件对象
   */
  onImageTap(e) {
    const { images, current } = e.currentTarget.dataset
    
    wx.previewImage({
      urls: images,
      current: current
    })
  },

  /**
   * 商品点击事件
   * @param {Object} e 事件对象
   */
  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    
    // 跳转到美团小程序
    this.navigateToMeituanProduct(product)
  },

  /**
   * 点赞按钮点击事件
   * @param {Object} e 事件对象
   */
  onLikeTap(e) {
    const article = e.currentTarget.dataset.article
    const articles = this.data.articles.map(item => {
      if (item.id === article.id) {
        item.isLiked = !item.isLiked
        item.likeCount += item.isLiked ? 1 : -1
      }
      return item
    })
    
    this.setData({ articles })
  },

  /**
   * 评论按钮点击事件
   * @param {Object} e 事件对象
   */
  onCommentTap(e) {
    const article = e.currentTarget.dataset.article
    
    wx.navigateTo({
      url: `/pages/article-detail/article-detail?articleId=${article.id}&showComment=true`
    })
  },

  /**
   * 分享按钮点击事件
   * @param {Object} e 事件对象
   */
  onShareTap(e) {
    const article = e.currentTarget.dataset.article
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  /**
   * 发布按钮点击事件
   */
  onPublishTap() {
    // 检查用户登录状态
    if (!app.globalData.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再发布内容',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }
    
    wx.navigateTo({
      url: '/pages/publish/publish'
    })
  },

  /**
   * 跳转到美团小程序商品页
   * @param {Object} product 商品信息
   */
  navigateToMeituanProduct(product) {
    wx.showLoading({
      title: '正在跳转...'
    })
    
    // 这里应该调用后端API生成推广链接
    setTimeout(() => {
      wx.hideLoading()
      
      app.navigateToMeituan('', {
        productId: product.id,
        source: 'community'
      })
    }, 1000)
  }
})