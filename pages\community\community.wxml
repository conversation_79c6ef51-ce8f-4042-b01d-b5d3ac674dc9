<!--pages/community/community.wxml-->
<view class="container">
  <!-- 顶部分类导航 -->
  <view class="category-nav">
    <scroll-view class="nav-scroll" scroll-x="true" show-scrollbar="false">
      <view class="nav-list">
        <view class="nav-item {{currentCategory === item.id ? 'active' : ''}}" 
              wx:for="{{categories}}" 
              wx:key="id" 
              bindtap="onCategoryTap" 
              data-category="{{item}}">
          <text class="nav-text">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 内容列表 -->
  <view class="content-list">
    <view class="content-item" wx:for="{{articles}}" wx:key="id" bindtap="onArticleTap" data-article="{{item}}">
      <view class="article-header">
        <view class="author-info">
          <image class="author-avatar" src="{{item.author.avatar}}" mode="aspectFill"></image>
          <view class="author-details">
            <text class="author-name">{{item.author.name}}</text>
            <text class="publish-time">{{item.publishTime}}</text>
          </view>
        </view>
        <view class="article-actions">
          <button class="follow-btn {{item.author.isFollowed ? 'followed' : ''}}" 
                  bindtap="onFollowTap" 
                  data-author="{{item.author}}" 
                  catchtap="true">
            {{item.author.isFollowed ? '已关注' : '关注'}}
          </button>
        </view>
      </view>
      
      <view class="article-content">
        <text class="article-title">{{item.title}}</text>
        <text class="article-summary">{{item.summary}}</text>
        
        <!-- 文章图片 -->
        <view class="article-images" wx:if="{{item.images && item.images.length > 0}}">
          <image class="article-image" 
                 wx:for="{{item.images}}" 
                 wx:key="*this" 
                 wx:for-item="image"
                 src="{{image}}" 
                 mode="aspectFill"
                 bindtap="onImageTap" 
                 data-images="{{item.images}}" 
                 data-current="{{image}}"
                 catchtap="true"></image>
        </view>
        
        <!-- 内嵌商品 -->
        <view class="embedded-product" wx:if="{{item.product}}" bindtap="onProductTap" data-product="{{item.product}}" catchtap="true">
          <image class="product-image" src="{{item.product.image}}" mode="aspectFill"></image>
          <view class="product-info">
            <text class="product-name">{{item.product.name}}</text>
            <view class="product-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.product.price}}</text>
              <text class="original-price" wx:if="{{item.product.originalPrice}}">¥{{item.product.originalPrice}}</text>
            </view>
          </view>
          <view class="product-action">
            <button class="buy-btn">购买</button>
          </view>
        </view>
      </view>
      
      <view class="article-footer">
        <view class="article-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <text class="article-tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
        </view>
        
        <view class="article-stats">
          <view class="stat-item" bindtap="onLikeTap" data-article="{{item}}" catchtap="true">
            <image class="stat-icon" src="{{item.isLiked ? '/images/heart-filled.png' : '/images/heart.png'}}"></image>
            <text class="stat-text">{{item.likeCount}}</text>
          </view>
          <view class="stat-item" bindtap="onCommentTap" data-article="{{item}}" catchtap="true">
            <image class="stat-icon" src="/images/comment.png"></image>
            <text class="stat-text">{{item.commentCount}}</text>
          </view>
          <view class="stat-item" bindtap="onShareTap" data-article="{{item}}" catchtap="true">
            <image class="stat-icon" src="/images/share.png"></image>
            <text class="stat-text">分享</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{articles.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-community.png"></image>
    <text class="empty-text">暂无内容</text>
    <text class="empty-desc">快来发布第一篇内容吧</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !loading}}">
    <text class="load-more-text">{{loadMoreText}}</text>
  </view>

  <!-- 发布按钮 -->
  <view class="publish-btn" bindtap="onPublishTap">
    <image class="publish-icon" src="/images/edit.png"></image>
  </view>
</view>