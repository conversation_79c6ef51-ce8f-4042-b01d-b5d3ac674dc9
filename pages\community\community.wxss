/* pages/community/community.wxss */

/* 分类导航 */
.category-nav {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 24rpx 0;
}

.nav-scroll {
  white-space: nowrap;
}

.nav-list {
  display: flex;
  padding: 0 32rpx;
}

.nav-item {
  padding: 16rpx 32rpx;
  margin-right: 24rpx;
  border-radius: 50rpx;
  background-color: #f5f5f5;
  white-space: nowrap;
}

.nav-item.active {
  background-color: #FF6900;
}

.nav-text {
  font-size: 28rpx;
  color: #666;
}

.nav-item.active .nav-text {
  color: #fff;
}

/* 内容列表 */
.content-list {
  padding: 0 32rpx;
}

.content-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-top: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 文章头部 */
.article-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.article-actions {
  display: flex;
  align-items: center;
}

.follow-btn {
  background-color: #FF6900;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  line-height: 1;
}

.follow-btn.followed {
  background-color: #f5f5f5;
  color: #666;
}

.follow-btn:active {
  opacity: 0.8;
}

/* 文章内容 */
.article-content {
  margin-bottom: 24rpx;
}

.article-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}

.article-summary {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

/* 文章图片 */
.article-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  gap: 12rpx;
}

.article-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

/* 内嵌商品 */
.embedded-product {
  display: flex;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  border: 1rpx solid #f0f0f0;
}

.embedded-product .product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.embedded-product .product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.embedded-product .product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.embedded-product .product-price {
  display: flex;
  align-items: baseline;
}

.embedded-product .price-symbol {
  font-size: 20rpx;
  color: #FF6900;
  font-weight: 600;
}

.embedded-product .price-value {
  font-size: 28rpx;
  color: #FF6900;
  font-weight: 600;
  margin-right: 12rpx;
}

.embedded-product .original-price {
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
}

.embedded-product .product-action {
  display: flex;
  align-items: flex-end;
  margin-left: 16rpx;
}

.embedded-product .buy-btn {
  background-color: #FF6900;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 1;
}

.embedded-product .buy-btn:active {
  background-color: #e55a00;
}

/* 文章底部 */
.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.article-tag {
  font-size: 20rpx;
  color: #FF6900;
  background-color: #FFF2E8;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.article-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-left: 32rpx;
}

.stat-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.load-more-text {
  font-size: 28rpx;
  color: #999;
}

/* 发布按钮 */
.publish-btn {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #FF6900;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 0, 0.3);
  z-index: 100;
}

.publish-btn:active {
  transform: scale(0.95);
}

.publish-icon {
  width: 48rpx;
  height: 48rpx;
}