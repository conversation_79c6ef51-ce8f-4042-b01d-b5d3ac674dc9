// pages/index/index.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    cityName: '广州市',
    currentCategory: 'recommend',
    hasMore: true,
    loadingText: '加载中...',
    
    // 品牌数据
    brands: [
      { id: 1, name: '肯德基', logo: '/images/brands/kfc.png' },
      { id: 2, name: '麦当劳', logo: '/images/brands/mcd.png' },
      { id: 3, name: '星巴克', logo: '/images/brands/starbucks.png' },
      { id: 4, name: '必胜客', logo: '/images/brands/pizzahut.png' },
      { id: 5, name: '汉堡王', logo: '/images/brands/bk.png' }
    ],
    
    // 轮播图数据
    banners: [
      { id: 1, image: '/images/banners/banner1.jpg', title: '新用户专享 下单返现金', url: '' },
      { id: 2, image: '/images/banners/banner2.jpg', title: '限时优惠 满减活动', url: '' },
      { id: 3, image: '/images/banners/banner3.jpg', title: '品牌联合 超值套餐', url: '' }
    ],
    
    // 分类数据
    categories: [
      { id: 'recommend', name: '为你推荐' },
      { id: 'food', name: '美食' },
      { id: 'takeout', name: '外卖商品券' },
      { id: 'entertainment', name: '休闲娱乐' },
      { id: 'shopping', name: '购物' }
    ],
    
    // 商品数据
    products: [
      {
        id: 1,
        name: '肯德基 香辣鸡腿堡套餐',
        image: '/images/products/product1.jpg',
        price: '29.9',
        originalPrice: '39.9',
        sales: '1.2万',
        tags: ['限时特价', '人气爆款']
      },
      {
        id: 2,
        name: '麦当劳 巨无霸套餐',
        image: '/images/products/product2.jpg',
        price: '32.5',
        originalPrice: '42.5',
        sales: '8956',
        tags: ['新品上市', '买一送一']
      },
      {
        id: 3,
        name: '星巴克 拿铁咖啡',
        image: '/images/products/product3.jpg',
        price: '28.0',
        originalPrice: '35.0',
        sales: '5432',
        tags: ['精选咖啡', '限时优惠']
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新城市信息
    this.setData({
      cityName: app.globalData.cityName || '广州市'
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreProducts()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '美团联盟 - 优质商品推荐',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    this.loadProducts()
  },

  /**
   * 刷新数据
   */
  refreshData() {
    wx.showNavigationBarLoading()
    
    // 模拟刷新数据
    setTimeout(() => {
      this.loadProducts()
      wx.hideNavigationBarLoading()
      wx.stopPullDownRefresh()
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    }, 1000)
  },

  /**
   * 加载商品数据
   */
  loadProducts() {
    // 这里应该调用美团联盟API获取商品数据
    // 暂时使用模拟数据
    console.log('加载商品数据...')
  },

  /**
   * 加载更多商品
   */
  loadMoreProducts() {
    if (!this.data.hasMore) return
    
    this.setData({
      loadingText: '加载中...'
    })
    
    // 模拟加载更多数据
    setTimeout(() => {
      const newProducts = [
        {
          id: Date.now(),
          name: '必胜客 至尊披萨',
          image: '/images/products/product4.jpg',
          price: '68.0',
          originalPrice: '88.0',
          sales: '3210',
          tags: ['大份量', '家庭分享']
        }
      ]
      
      this.setData({
        products: [...this.data.products, ...newProducts],
        loadingText: '加载完成'
      })
    }, 1000)
  },

  /**
   * 搜索框点击事件
   */
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  /**
   * 品牌点击事件
   * @param {Object} e 事件对象
   */
  onBrandTap(e) {
    const brand = e.currentTarget.dataset.brand
    console.log('点击品牌:', brand)
    
    wx.navigateTo({
      url: `/pages/products/products?brandId=${brand.id}&brandName=${brand.name}`
    })
  },

  /**
   * 轮播图点击事件
   * @param {Object} e 事件对象
   */
  onBannerTap(e) {
    const banner = e.currentTarget.dataset.banner
    console.log('点击轮播图:', banner)
    
    if (banner.url) {
      wx.navigateTo({
        url: banner.url
      })
    }
  },

  /**
   * 分类点击事件
   * @param {Object} e 事件对象
   */
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category
    
    this.setData({
      currentCategory: category.id
    })
    
    // 根据分类加载商品
    this.loadProductsByCategory(category.id)
  },

  /**
   * 根据分类加载商品
   * @param {string} categoryId 分类ID
   */
  loadProductsByCategory(categoryId) {
    console.log('加载分类商品:', categoryId)
    // 这里应该调用API根据分类获取商品
  },

  /**
   * 商品点击事件
   * @param {Object} e 事件对象
   */
  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    console.log('点击商品:', product)
    
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?productId=${product.id}`
    })
  },

  /**
   * 抢购按钮点击事件
   * @param {Object} e 事件对象
   */
  onGrabTap(e) {
    const product = e.currentTarget.dataset.product
    console.log('点击抢购:', product)
    
    // 生成推广链接并跳转到美团小程序
    this.navigateToMeituanProduct(product)
  },

  /**
   * 跳转到美团小程序商品页
   * @param {Object} product 商品信息
   */
  navigateToMeituanProduct(product) {
    wx.showLoading({
      title: '正在跳转...'
    })
    
    // 这里应该调用后端API生成推广链接
    // 暂时模拟跳转
    setTimeout(() => {
      wx.hideLoading()
      
      // 调用app.js中的跳转方法
      app.navigateToMeituan('', {
        productId: product.id,
        source: 'miniprogram'
      })
    }, 1000)
  }
})