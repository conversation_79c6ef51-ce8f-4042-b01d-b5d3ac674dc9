// pages/index/index.js
const app = getApp();
const { businessAPI, adAPI } = require('../../utils/api');
const { PAGE_CONFIG, AD_POSITIONS, formatNumber } = require('../../utils/config');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    cityName: '定位中...',
    currentCategory: 'recommend',
    hasMore: true,
    loading: false,
    page: 1,

    // 广告数据
    topAds: [],

    // 分类数据
    categories: [
      { id: 'recommend', name: '为你推荐' },
      { id: 'food', name: '美食' },
      { id: 'takeout', name: '外卖' },
      { id: 'hotel', name: '酒店' },
      { id: 'travel', name: '旅游' }
    ],

    // 商品数据
    products: [],

    // 搜索关键词
    searchKeyword: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    if (this.data.products.length === 0) {
      this.loadProducts(true);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadProducts(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadProducts(false);
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    // 获取用户位置
    this.getUserLocation();

    // 加载顶部广告
    this.loadTopAds();

    // 加载商品数据
    this.loadProducts(true);
  },

  /**
   * 获取用户位置
   */
  getUserLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        // 根据经纬度获取城市名称（这里简化处理）
        this.setData({
          cityName: '当前城市'
        });
      },
      fail: () => {
        this.setData({
          cityName: '广州市'
        });
      }
    });
  },

  /**
   * 加载顶部广告
   */
  async loadTopAds() {
    try {
      const ads = await businessAPI.loadAds(AD_POSITIONS.HOME_TOP);
      this.setData({
        topAds: ads
      });
    } catch (error) {
      console.error('加载广告失败:', error);
    }
  },

  /**
   * 加载商品数据
   */
  async loadProducts(refresh = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const page = refresh ? 1 : this.data.page + 1;
      const params = {
        page: page,
        limit: PAGE_CONFIG.index.productPageSize,
        category: this.data.currentCategory === 'recommend' ? null : this.data.currentCategory
      };

      const result = await businessAPI.getProductList(params, false);

      const newProducts = result.data.list || [];
      const products = refresh ? newProducts : [...this.data.products, ...newProducts];

      this.setData({
        products: products,
        page: page,
        hasMore: newProducts.length === PAGE_CONFIG.index.productPageSize,
        loading: false
      });

      if (refresh) {
        wx.stopPullDownRefresh();
      }

    } catch (error) {
      this.setData({ loading: false });
      if (refresh) {
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 分类切换
   */
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category;
    if (category !== this.data.currentCategory) {
      this.setData({
        currentCategory: category,
        products: [],
        page: 0
      });
      this.loadProducts(true);
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索确认
   */
  onSearchConfirm() {
    if (this.data.searchKeyword.trim()) {
      wx.navigateTo({
        url: `/pages/products/products?keyword=${this.data.searchKeyword}`
      });
    }
  },

  /**
   * 商品点击
   */
  onProductTap(e) {
    const product = e.currentTarget.dataset.product;
    businessAPI.promoteProduct(product.id, product);
  },

  /**
   * 广告点击
   */
  onAdTap(e) {
    const ad = e.currentTarget.dataset.ad;
    adAPI.recordClick(ad.id);

    if (ad.link_url) {
      if (ad.link_type === 'internal') {
        wx.navigateTo({
          url: ad.link_url
        });
      } else {
        wx.navigateTo({
          url: `/pages/webview/webview?url=${encodeURIComponent(ad.link_url)}`
        });
      }
    }
  },

  /**
   * 收藏商品
   */
  onFavoriteProduct(e) {
    const product = e.currentTarget.dataset.product;
    businessAPI.favoriteProduct(product.id, product);
  }
});