<!--pages/index/index.wxml-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="location-info">
      <image class="location-icon" src="/images/location.png"></image>
      <text class="location-text">{{cityName}}</text>
    </view>
    <view class="header-actions">
      <button class="share-btn" open-type="share">
        <image class="share-icon" src="/images/share.png"></image>
      </button>
    </view>
  </view>

  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-box" bindtap="onSearchTap">
      <image class="search-icon" src="/images/search.png"></image>
      <text class="search-placeholder">搜索商品或店铺</text>
    </view>
  </view>

  <!-- 品牌展示区域 -->
  <view class="brands-section">
    <scroll-view class="brands-scroll" scroll-x="true" show-scrollbar="false">
      <view class="brands-list">
        <view class="brand-item" wx:for="{{brands}}" wx:key="id" bindtap="onBrandTap" data-brand="{{item}}">
          <image class="brand-logo" src="{{item.logo}}" mode="aspectFit"></image>
          <text class="brand-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 活动广告位 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-banner="{{item}}"></image>
      </swiper-item>
    </swiper>
  </view>

  <!-- 分类导航 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
      <view class="category-list">
        <view class="category-item {{currentCategory === item.id ? 'active' : ''}}" 
              wx:for="{{categories}}" 
              wx:key="id" 
              bindtap="onCategoryTap" 
              data-category="{{item}}">
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 商品列表 -->
  <view class="products-section">
    <view class="section-title">
      <text class="title-text">今日必抢</text>
      <view class="title-decoration"></view>
    </view>
    
    <view class="products-list">
      <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-tags">
            <text class="product-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
          </view>
          <view class="product-price-row">
            <view class="price-info">
              <text class="price-symbol">¥</text>
              <text class="price-integer">{{item.price}}</text>
              <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
            </view>
            <view class="sales-info">
              <text class="sales-text">已售{{item.sales}}</text>
            </view>
          </view>
        </view>
        <view class="product-action">
          <button class="grab-btn" bindtap="onGrabTap" data-product="{{item}}" catchtap="true">
            抢
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{hasMore}}">
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>