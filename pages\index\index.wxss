/* pages/index/index.wxss */

/* 头部区域 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;
  background-color: #fff;
}

.location-info {
  display: flex;
  align-items: center;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
}

.share-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.share-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 搜索框 */
.search-container {
  padding: 20rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 32rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-placeholder {
  font-size: 28rpx;
  color: #999;
}

/* 品牌展示区域 */
.brands-section {
  background-color: #fff;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.brands-scroll {
  white-space: nowrap;
}

.brands-list {
  display: flex;
  padding: 0 32rpx;
}

.brand-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 48rpx;
  min-width: 120rpx;
}

.brand-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 12rpx;
}

.brand-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 活动广告位 */
.banner-section {
  margin: 20rpx 32rpx;
}

.banner-swiper {
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 分类导航 */
.category-section {
  background-color: #fff;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 32rpx;
}

.category-item {
  padding: 16rpx 32rpx;
  margin-right: 24rpx;
  border-radius: 50rpx;
  background-color: #f5f5f5;
  white-space: nowrap;
}

.category-item.active {
  background-color: #FF6900;
}

.category-name {
  font-size: 28rpx;
  color: #666;
}

.category-item.active .category-name {
  color: #fff;
}

/* 商品列表 */
.products-section {
  padding: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.title-decoration {
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #FF6900 0%, #FFB366 100%);
  border-radius: 3rpx;
}

.products-list {
  display: flex;
  flex-direction: column;
}

.product-item {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.product-tag {
  font-size: 20rpx;
  color: #FF6900;
  background-color: #FFF2E8;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #FF6900;
  font-weight: 600;
}

.price-integer {
  font-size: 36rpx;
  color: #FF6900;
  font-weight: 600;
  margin-right: 12rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.sales-info {
  display: flex;
  align-items: center;
}

.sales-text {
  font-size: 24rpx;
  color: #999;
}

.product-action {
  display: flex;
  align-items: flex-end;
  margin-left: 16rpx;
}

.grab-btn {
  background-color: #FF6900;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1;
}

.grab-btn:active {
  background-color: #e55a00;
}

/* 加载更多 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}