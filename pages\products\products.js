// pages/products/products.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentNav: 'all',
    sortText: '综合排序',
    loading: false,
    hasMore: true,
    loadMoreText: '上拉加载更多',
    showFilter: false,
    priceRange: '',
    selectedBrands: [],
    
    // 导航列表
    navList: [
      { id: 'all', name: '全部新品' },
      { id: 'food', name: '美食餐饮' },
      { id: 'takeout', name: '外卖' },
      { id: 'entertainment', name: '休闲娱乐' },
      { id: 'shopping', name: '购物消费' }
    ],
    
    // 价格区间
    priceRanges: [
      { value: '', label: '不限' },
      { value: '0-30', label: '30元以下' },
      { value: '30-60', label: '30-60元' },
      { value: '60-100', label: '60-100元' },
      { value: '100-200', label: '100-200元' },
      { value: '200+', label: '200元以上' }
    ],
    
    // 品牌列表
    brandList: [
      { id: 1, name: '肯德基' },
      { id: 2, name: '麦当劳' },
      { id: 3, name: '星巴克' },
      { id: 4, name: '必胜客' },
      { id: 5, name: '汉堡王' },
      { id: 6, name: '德克士' }
    ],
    
    // 商品列表
    products: [
      {
        id: 1,
        name: '肯德基 香辣鸡腿堡套餐',
        description: '香辣鸡腿堡+薯条+可乐，经典搭配',
        image: '/images/products/product1.jpg',
        price: '29.9',
        originalPrice: '39.9',
        sales: '1.2万',
        tags: ['限时特价', '人气爆款']
      },
      {
        id: 2,
        name: '麦当劳 巨无霸套餐',
        description: '经典巨无霸汉堡+薯条+饮料',
        image: '/images/products/product2.jpg',
        price: '32.5',
        originalPrice: '42.5',
        sales: '8956',
        tags: ['新品上市', '买一送一']
      },
      {
        id: 3,
        name: '星巴克 拿铁咖啡',
        description: '精选咖啡豆，香醇浓郁',
        image: '/images/products/product3.jpg',
        price: '28.0',
        originalPrice: '35.0',
        sales: '5432',
        tags: ['精选咖啡', '限时优惠']
      },
      {
        id: 4,
        name: '必胜客 至尊披萨',
        description: '多种配料，丰富口感',
        image: '/images/products/product4.jpg',
        price: '68.0',
        originalPrice: '88.0',
        sales: '3210',
        tags: ['大份量', '家庭分享']
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取页面参数
    const { brandId, brandName, categoryId } = options
    
    if (brandName) {
      wx.setNavigationBarTitle({
        title: brandName
      })
    }
    
    this.setData({
      brandId,
      categoryId
    })
    
    this.loadProducts()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreProducts()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '精选商品推荐',
      path: '/pages/products/products',
      imageUrl: '/images/share-products.jpg'
    }
  },

  /**
   * 刷新数据
   */
  refreshData() {
    wx.showNavigationBarLoading()
    
    setTimeout(() => {
      this.loadProducts()
      wx.hideNavigationBarLoading()
      wx.stopPullDownRefresh()
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    }, 1000)
  },

  /**
   * 加载商品数据
   */
  loadProducts() {
    this.setData({
      loading: true
    })
    
    // 这里应该调用美团联盟API获取商品数据
    // 根据当前的筛选条件获取数据
    setTimeout(() => {
      this.setData({
        loading: false
      })
    }, 1000)
  },

  /**
   * 加载更多商品
   */
  loadMoreProducts() {
    if (!this.data.hasMore || this.data.loading) return
    
    this.setData({
      loading: true,
      loadMoreText: '加载中...'
    })
    
    // 模拟加载更多数据
    setTimeout(() => {
      const newProducts = [
        {
          id: Date.now(),
          name: '德克士 脆皮炸鸡',
          description: '外酥内嫩，香辣可口',
          image: '/images/products/product5.jpg',
          price: '25.0',
          originalPrice: '32.0',
          sales: '2156',
          tags: ['新品推荐', '香辣美味']
        }
      ]
      
      this.setData({
        products: [...this.data.products, ...newProducts],
        loading: false,
        loadMoreText: '上拉加载更多'
      })
    }, 1000)
  },

  /**
   * 导航点击事件
   * @param {Object} e 事件对象
   */
  onNavTap(e) {
    const nav = e.currentTarget.dataset.nav
    
    this.setData({
      currentNav: nav.id
    })
    
    this.loadProductsByNav(nav.id)
  },

  /**
   * 根据导航加载商品
   * @param {string} navId 导航ID
   */
  loadProductsByNav(navId) {
    console.log('根据导航加载商品:', navId)
    this.loadProducts()
  },

  /**
   * 排序点击事件
   */
  onSortTap() {
    const sortOptions = ['综合排序', '销量优先', '价格从低到高', '价格从高到低']
    
    wx.showActionSheet({
      itemList: sortOptions,
      success: (res) => {
        this.setData({
          sortText: sortOptions[res.tapIndex]
        })
        this.loadProducts()
      }
    })
  },

  /**
   * 价格点击事件
   */
  onPriceTap() {
    // 可以实现价格排序逻辑
    console.log('价格排序')
  },

  /**
   * 筛选点击事件
   */
  onFilterTap() {
    this.setData({
      showFilter: true
    })
  },

  /**
   * 关闭筛选弹窗
   */
  onFilterClose() {
    this.setData({
      showFilter: false
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 重置筛选条件
   */
  onFilterReset() {
    this.setData({
      priceRange: '',
      selectedBrands: []
    })
  },

  /**
   * 价格区间选择
   * @param {Object} e 事件对象
   */
  onPriceRangeTap(e) {
    const range = e.currentTarget.dataset.range
    
    this.setData({
      priceRange: range.value
    })
  },

  /**
   * 品牌选择
   * @param {Object} e 事件对象
   */
  onBrandSelect(e) {
    const brand = e.currentTarget.dataset.brand
    const selectedBrands = [...this.data.selectedBrands]
    const index = selectedBrands.indexOf(brand.id)
    
    if (index > -1) {
      selectedBrands.splice(index, 1)
    } else {
      selectedBrands.push(brand.id)
    }
    
    this.setData({
      selectedBrands
    })
  },

  /**
   * 确认筛选
   */
  onFilterConfirm() {
    this.setData({
      showFilter: false
    })
    
    // 根据筛选条件重新加载商品
    this.loadProducts()
  },

  /**
   * 商品点击事件
   * @param {Object} e 事件对象
   */
  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?productId=${product.id}`
    })
  },

  /**
   * 抢购按钮点击事件
   * @param {Object} e 事件对象
   */
  onGrabTap(e) {
    const product = e.currentTarget.dataset.product
    
    // 跳转到美团小程序
    this.navigateToMeituanProduct(product)
  },

  /**
   * 跳转到美团小程序商品页
   * @param {Object} product 商品信息
   */
  navigateToMeituanProduct(product) {
    wx.showLoading({
      title: '正在跳转...'
    })
    
    // 这里应该调用后端API生成推广链接
    setTimeout(() => {
      wx.hideLoading()
      
      app.navigateToMeituan('', {
        productId: product.id,
        source: 'products_page'
      })
    }, 1000)
  }
})