<!--pages/products/products.wxml-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="top-nav">
    <scroll-view class="nav-scroll" scroll-x="true" show-scrollbar="false">
      <view class="nav-list">
        <view class="nav-item {{currentNav === item.id ? 'active' : ''}}" 
              wx:for="{{navList}}" 
              wx:key="id" 
              bindtap="onNavTap" 
              data-nav="{{item}}">
          <text class="nav-text">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 筛选条件 -->
  <view class="filter-bar">
    <view class="filter-item" bindtap="onSortTap">
      <text class="filter-text">{{sortText}}</text>
      <image class="filter-icon" src="/images/arrow-down.png"></image>
    </view>
    <view class="filter-item" bindtap="onPriceTap">
      <text class="filter-text">价格</text>
      <image class="filter-icon" src="/images/arrow-down.png"></image>
    </view>
    <view class="filter-item" bindtap="onFilterTap">
      <text class="filter-text">筛选</text>
      <image class="filter-icon" src="/images/filter.png"></image>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="products-list">
    <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
      <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
      <view class="product-info">
        <text class="product-name">{{item.name}}</text>
        <view class="product-desc">
          <text class="desc-text">{{item.description}}</text>
        </view>
        <view class="product-tags">
          <text class="product-tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
        </view>
        <view class="product-bottom">
          <view class="price-section">
            <view class="current-price">
              <text class="price-symbol">¥</text>
              <text class="price-integer">{{item.price}}</text>
            </view>
            <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
          </view>
          <view class="sales-section">
            <text class="sales-text">已售{{item.sales}}</text>
          </view>
        </view>
      </view>
      <view class="product-action">
        <button class="grab-btn" bindtap="onGrabTap" data-product="{{item}}" catchtap="true">
          抢
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{products.length === 0 && !loading}}">
    <image class="empty-image" src="/images/empty-products.png"></image>
    <text class="empty-text">暂无商品</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !loading}}">
    <text class="load-more-text">{{loadMoreText}}</text>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-popup {{showFilter ? 'show' : ''}}" bindtap="onFilterClose">
  <view class="filter-content" catchtap="stopPropagation">
    <view class="filter-header">
      <text class="filter-title">筛选条件</text>
      <text class="filter-reset" bindtap="onFilterReset">重置</text>
    </view>
    
    <view class="filter-section">
      <text class="section-title">价格区间</text>
      <view class="price-range">
        <view class="price-item {{priceRange === item.value ? 'active' : ''}}" 
              wx:for="{{priceRanges}}" 
              wx:key="value" 
              bindtap="onPriceRangeTap" 
              data-range="{{item}}">
          <text class="price-text">{{item.label}}</text>
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <text class="section-title">品牌</text>
      <view class="brand-list">
        <view class="brand-item {{selectedBrands.indexOf(item.id) !== -1 ? 'active' : ''}}" 
              wx:for="{{brandList}}" 
              wx:key="id" 
              bindtap="onBrandSelect" 
              data-brand="{{item}}">
          <text class="brand-text">{{item.name}}</text>
        </view>
      </view>
    </view>
    
    <view class="filter-footer">
      <button class="confirm-btn" bindtap="onFilterConfirm">确定</button>
    </view>
  </view>
</view>