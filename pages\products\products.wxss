/* pages/products/products.wxss */

/* 顶部导航 */
.top-nav {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 24rpx 0;
}

.nav-scroll {
  white-space: nowrap;
}

.nav-list {
  display: flex;
  padding: 0 32rpx;
}

.nav-item {
  padding: 16rpx 32rpx;
  margin-right: 24rpx;
  border-radius: 50rpx;
  background-color: #f5f5f5;
  white-space: nowrap;
}

.nav-item.active {
  background-color: #FF6900;
}

.nav-text {
  font-size: 28rpx;
  color: #666;
}

.nav-item.active .nav-text {
  color: #fff;
}

/* 筛选条件 */
.filter-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-right: 48rpx;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
}

.filter-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 商品列表 */
.products-list {
  padding: 0 32rpx;
}

.product-item {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-desc {
  margin-bottom: 12rpx;
}

.desc-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.product-tag {
  font-size: 20rpx;
  color: #FF6900;
  background-color: #FFF2E8;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.price-section {
  display: flex;
  align-items: baseline;
}

.current-price {
  display: flex;
  align-items: baseline;
  margin-right: 12rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #FF6900;
  font-weight: 600;
}

.price-integer {
  font-size: 36rpx;
  color: #FF6900;
  font-weight: 600;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.sales-section {
  display: flex;
  align-items: center;
}

.sales-text {
  font-size: 24rpx;
  color: #999;
}

.product-action {
  display: flex;
  align-items: flex-end;
  margin-left: 16rpx;
}

.grab-btn {
  background-color: #FF6900;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1;
}

.grab-btn:active {
  background-color: #e55a00;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.load-more-text {
  font-size: 28rpx;
  color: #999;
}

/* 筛选弹窗 */
.filter-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-popup.show {
  opacity: 1;
  visibility: visible;
}

.filter-content {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 600rpx;
  background-color: #fff;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.filter-popup.show .filter-content {
  transform: translateX(0);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.filter-reset {
  font-size: 28rpx;
  color: #FF6900;
}

.filter-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.price-range {
  display: flex;
  flex-wrap: wrap;
}

.price-item {
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
}

.price-item.active {
  border-color: #FF6900;
  background-color: #FFF2E8;
}

.price-text {
  font-size: 24rpx;
  color: #333;
}

.price-item.active .price-text {
  color: #FF6900;
}

.brand-list {
  display: flex;
  flex-wrap: wrap;
}

.brand-item {
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
}

.brand-item.active {
  border-color: #FF6900;
  background-color: #FFF2E8;
}

.brand-text {
  font-size: 24rpx;
  color: #333;
}

.brand-item.active .brand-text {
  color: #FF6900;
}

.filter-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
  width: 100%;
  background-color: #FF6900;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.confirm-btn:active {
  background-color: #e55a00;
}