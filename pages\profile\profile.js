// pages/profile/profile.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      avatarUrl: '',
      nickName: '',
      id: '',
      isVip: false
    },
    assets: {
      couponCount: 0,
      balance: '0.00',
      points: 0,
      redPackCount: 0
    },
    orderCounts: {
      pending: 0,
      paid: 0,
      used: 0,
      refund: 0
    },
    promotionStats: {
      inviteCount: 0,
      earnings: '0.00'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserInfo();
    this.loadUserAssets();
    this.loadOrderCounts();
    this.loadPromotionStats();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    this.loadUserInfo();
    this.loadUserAssets();
    this.loadOrderCounts();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const app = getApp();
    if (app.globalData.userInfo && app.globalData.userInfo.nickName) {
      this.setData({
        userInfo: {
          ...app.globalData.userInfo,
          id: app.globalData.openid || '未获取',
          isVip: false // 根据实际业务逻辑判断
        }
      });
    } else {
      // 尝试从本地存储获取
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.nickName) {
        this.setData({
          userInfo: {
            ...userInfo,
            id: app.globalData.openid || '未获取',
            isVip: false
          }
        });
      }
    }
  },

  /**
   * 加载用户资产信息
   */
  loadUserAssets() {
    // TODO: 调用后端API获取用户资产信息
    // 模拟数据
    const mockAssets = {
      couponCount: 3,
      balance: '128.50',
      points: 2580,
      redPackCount: 1
    };
    
    this.setData({
      assets: mockAssets
    });
    
    // 实际API调用示例
    /*
    wx.request({
      url: 'https://your-api.com/user/assets',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
      },
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            assets: res.data.data
          });
        }
      },
      fail: (err) => {
        console.error('获取用户资产失败:', err);
      }
    });
    */
  },

  /**
   * 加载订单统计
   */
  loadOrderCounts() {
    // TODO: 调用后端API获取订单统计
    // 模拟数据
    const mockOrderCounts = {
      pending: 1,
      paid: 2,
      used: 15,
      refund: 0
    };
    
    this.setData({
      orderCounts: mockOrderCounts
    });
  },

  /**
   * 加载推广统计
   */
  loadPromotionStats() {
    // TODO: 调用后端API获取推广统计
    // 模拟数据
    const mockPromotionStats = {
      inviteCount: 5,
      earnings: '68.50'
    };
    
    this.setData({
      promotionStats: mockPromotionStats
    });
  },

  /**
   * 点击头像
   */
  onAvatarTap() {
    if (!this.data.userInfo.nickName) {
      this.onLoginTap();
      return;
    }
    
    // 已登录用户可以更换头像
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        // TODO: 上传头像到服务器
        this.uploadAvatar(tempFilePath);
      }
    });
  },

  /**
   * 上传头像
   */
  uploadAvatar(filePath) {
    wx.showLoading({
      title: '上传中...'
    });
    
    // TODO: 实际上传逻辑
    setTimeout(() => {
      wx.hideLoading();
      this.setData({
        'userInfo.avatarUrl': filePath
      });
      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 点击登录
   */
  onLoginTap() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = res.userInfo;
        const app = getApp();
        
        // 保存用户信息
        app.globalData.userInfo = userInfo;
        wx.setStorageSync('userInfo', userInfo);
        
        this.setData({
          userInfo: {
            ...userInfo,
            id: app.globalData.openid || '未获取',
            isVip: false
          }
        });
        
        // 重新加载用户数据
        this.loadUserAssets();
        this.loadOrderCounts();
        this.loadPromotionStats();
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.log('用户拒绝授权', err);
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 查看全部资产
   */
  onViewAllAssets() {
    wx.navigateTo({
      url: '/pages/assets/assets'
    });
  },

  /**
   * 点击资产项
   */
  onAssetTap(e) {
    const type = e.currentTarget.dataset.type;
    
    switch (type) {
      case 'coupon':
        wx.navigateTo({
          url: '/pages/coupon/coupon'
        });
        break;
      case 'wallet':
        wx.navigateTo({
          url: '/pages/wallet/wallet'
        });
        break;
      case 'points':
        wx.navigateTo({
          url: '/pages/points/points'
        });
        break;
      case 'redpack':
        wx.navigateTo({
          url: '/pages/redpack/redpack'
        });
        break;
    }
  },

  /**
   * 订单中心
   */
  onOrderCenterTap() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  },

  /**
   * 点击订单类型
   */
  onOrderTypeTap(e) {
    const type = e.currentTarget.dataset.type;
    wx.navigateTo({
      url: `/pages/orders/orders?type=${type}`
    });
  },

  /**
   * 点击服务项
   */
  onServiceTap(e) {
    const service = e.currentTarget.dataset.service;
    
    switch (service) {
      case 'favorite':
        wx.navigateTo({
          url: '/pages/favorite/favorite'
        });
        break;
      case 'history':
        wx.navigateTo({
          url: '/pages/history/history'
        });
        break;
      case 'address':
        wx.navigateTo({
          url: '/pages/address/address'
        });
        break;
      case 'customer':
        // 联系客服
        wx.makePhoneCall({
          phoneNumber: '************'
        });
        break;
      case 'feedback':
        wx.navigateTo({
          url: '/pages/feedback/feedback'
        });
        break;
      case 'about':
        wx.navigateTo({
          url: '/pages/about/about'
        });
        break;
      case 'settings':
        wx.navigateTo({
          url: '/pages/settings/settings'
        });
        break;
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const app = getApp();
    return {
      title: '美团联盟 - 优质商品，超值优惠',
      path: '/pages/index/index',
      imageUrl: '/images/share-bg.jpg'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '美团联盟 - 优质商品，超值优惠',
      imageUrl: '/images/share-bg.jpg'
    };
  }
});