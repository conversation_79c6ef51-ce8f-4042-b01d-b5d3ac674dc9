<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info">
      <view class="avatar-container" bindtap="onAvatarTap">
        <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="avatar-edit" wx:if="{{!userInfo.nickName}}">
          <image class="edit-icon" src="/images/camera.png"></image>
        </view>
      </view>
      <view class="user-details">
        <view class="user-name-row">
          <text class="user-name" wx:if="{{userInfo.nickName}}">{{userInfo.nickName}}</text>
          <button class="login-btn" wx:else bindtap="onLoginTap">点击登录</button>
          <image class="vip-icon" src="/images/vip.png" wx:if="{{userInfo.isVip}}"></image>
        </view>
        <text class="user-id" wx:if="{{userInfo.nickName}}">ID: {{userInfo.id || '未设置'}}</text>
      </view>
    </view>
  </view>

  <!-- 资产权益区域 -->
  <view class="assets-section">
    <view class="assets-header">
      <text class="section-title">我的资产</text>
      <text class="view-all" bindtap="onViewAllAssets">查看全部 ></text>
    </view>
    <view class="assets-grid">
      <view class="asset-item" bindtap="onAssetTap" data-type="coupon">
        <view class="asset-value">{{assets.couponCount}}</view>
        <text class="asset-label">优惠券</text>
      </view>
      <view class="asset-item" bindtap="onAssetTap" data-type="wallet">
        <view class="asset-value">¥{{assets.balance}}</view>
        <text class="asset-label">余额</text>
      </view>
      <view class="asset-item" bindtap="onAssetTap" data-type="points">
        <view class="asset-value">{{assets.points}}</view>
        <text class="asset-label">积分</text>
      </view>
      <view class="asset-item" bindtap="onAssetTap" data-type="redpack">
        <view class="asset-value">{{assets.redPackCount}}</view>
        <text class="asset-label">红包</text>
      </view>
    </view>
  </view>

  <!-- 订单管理区域 -->
  <view class="order-section">
    <view class="section-header" bindtap="onOrderCenterTap">
      <text class="section-title">我的订单</text>
      <view class="header-right">
        <text class="view-all">查看全部</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
    <view class="order-types">
      <view class="order-type-item" bindtap="onOrderTypeTap" data-type="pending">
        <image class="type-icon" src="/images/order-pending.png"></image>
        <text class="type-label">待付款</text>
        <view class="type-badge" wx:if="{{orderCounts.pending > 0}}">{{orderCounts.pending}}</view>
      </view>
      <view class="order-type-item" bindtap="onOrderTypeTap" data-type="paid">
        <image class="type-icon" src="/images/order-paid.png"></image>
        <text class="type-label">待使用</text>
        <view class="type-badge" wx:if="{{orderCounts.paid > 0}}">{{orderCounts.paid}}</view>
      </view>
      <view class="order-type-item" bindtap="onOrderTypeTap" data-type="used">
        <image class="type-icon" src="/images/order-used.png"></image>
        <text class="type-label">已使用</text>
      </view>
      <view class="order-type-item" bindtap="onOrderTypeTap" data-type="refund">
        <image class="type-icon" src="/images/order-refund.png"></image>
        <text class="type-label">退款/售后</text>
        <view class="type-badge" wx:if="{{orderCounts.refund > 0}}">{{orderCounts.refund}}</view>
      </view>
    </view>
  </view>

  <!-- 功能服务区域 -->
  <view class="services-section">
    <view class="service-group">
      <view class="service-item" bindtap="onServiceTap" data-service="favorite">
        <image class="service-icon" src="/images/favorite.png"></image>
        <text class="service-label">我的收藏</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
      <view class="service-item" bindtap="onServiceTap" data-service="history">
        <image class="service-icon" src="/images/history.png"></image>
        <text class="service-label">浏览记录</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
      <view class="service-item" bindtap="onServiceTap" data-service="address">
        <image class="service-icon" src="/images/address.png"></image>
        <text class="service-label">收货地址</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
    
    <view class="service-group">
      <view class="service-item" bindtap="onServiceTap" data-service="customer">
        <image class="service-icon" src="/images/customer.png"></image>
        <text class="service-label">客服中心</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
      <view class="service-item" bindtap="onServiceTap" data-service="feedback">
        <image class="service-icon" src="/images/feedback.png"></image>
        <text class="service-label">意见反馈</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
      <view class="service-item" bindtap="onServiceTap" data-service="about">
        <image class="service-icon" src="/images/about.png"></image>
        <text class="service-label">关于我们</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
  </view>

  <!-- 推广信息区域 -->
  <view class="promotion-section" wx:if="{{userInfo.nickName}}">
    <view class="promotion-card">
      <view class="promotion-header">
        <text class="promotion-title">邀请好友，共享优惠</text>
        <button class="share-btn" open-type="share">
          <image class="share-icon" src="/images/share-white.png"></image>
          <text class="share-text">立即分享</text>
        </button>
      </view>
      <view class="promotion-stats">
        <view class="stat-item">
          <text class="stat-value">{{promotionStats.inviteCount}}</text>
          <text class="stat-label">已邀请</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">¥{{promotionStats.earnings}}</text>
          <text class="stat-label">累计收益</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 设置区域 -->
  <view class="settings-section">
    <view class="service-item" bindtap="onServiceTap" data-service="settings">
      <image class="service-icon" src="/images/settings.png"></image>
      <text class="service-label">设置</text>
      <image class="arrow-icon" src="/images/arrow-right.png"></image>
    </view>
  </view>
</view>