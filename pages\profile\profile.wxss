/* pages/profile/profile.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f00 100%);
  padding: 40rpx 30rpx 30rpx;
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar-container {
  position: relative;
  margin-right: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  background-color: #fff;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  width: 20rpx;
  height: 20rpx;
}

.user-details {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-right: 20rpx;
}

.login-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 40rpx;
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  line-height: 1;
}

.vip-icon {
  width: 60rpx;
  height: 24rpx;
}

.user-id {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 资产权益区域 */
.assets-section {
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.assets-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.view-all {
  font-size: 26rpx;
  color: #ff6b35;
}

.assets-grid {
  display: flex;
  justify-content: space-between;
}

.asset-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
}

.asset-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.asset-label {
  font-size: 24rpx;
  color: #666;
}

/* 订单管理区域 */
.order-section {
  background-color: #fff;
  margin: 0 30rpx 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-right {
  display: flex;
  align-items: center;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

.order-types {
  display: flex;
  justify-content: space-between;
}

.order-type-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 20rpx 10rpx;
}

.type-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 15rpx;
}

.type-label {
  font-size: 24rpx;
  color: #666;
}

.type-badge {
  position: absolute;
  top: 10rpx;
  right: 20rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
  line-height: 1;
}

/* 功能服务区域 */
.services-section {
  margin: 0 30rpx 20rpx;
}

.service-group {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.service-item:last-child {
  border-bottom: none;
}

.service-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 30rpx;
}

.service-label {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

/* 推广信息区域 */
.promotion-section {
  margin: 0 30rpx 20rpx;
}

.promotion-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  color: #fff;
}

.promotion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.promotion-title {
  font-size: 32rpx;
  font-weight: bold;
}

.share-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 24rpx;
  line-height: 1;
}

.share-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.share-text {
  color: #fff;
}

.promotion-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 设置区域 */
.settings-section {
  margin: 0 30rpx;
}

.settings-section .service-group {
  margin-bottom: 0;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .assets-grid {
    flex-wrap: wrap;
  }
  
  .asset-item {
    width: 50%;
    margin-bottom: 20rpx;
  }
  
  .order-types {
    flex-wrap: wrap;
  }
  
  .order-type-item {
    width: 25%;
  }
}

/* 安全区域适配 */
.container {
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}