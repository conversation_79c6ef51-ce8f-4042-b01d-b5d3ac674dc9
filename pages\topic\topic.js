// pages/topic/topic.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentCategory: 'recommend',
    categories: [
      { id: 'recommend', name: '推荐' },
      { id: 'food', name: '美食' },
      { id: 'travel', name: '旅行' },
      { id: 'lifestyle', name: '生活' },
      { id: 'shopping', name: '购物' },
      { id: 'entertainment', name: '娱乐' }
    ],
    articles: [],
    loading: false,
    loadingMore: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadArticles(true);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时可以刷新数据
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadArticles(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMoreArticles();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '美团联盟专题 - 精彩内容等你发现',
      path: '/pages/topic/topic',
      imageUrl: '/images/share-topic.jpg'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '美团联盟专题 - 精彩内容等你发现',
      imageUrl: '/images/share-topic.jpg'
    };
  },

  /**
   * 加载文章列表
   */
  loadArticles(refresh = false) {
    if (refresh) {
      this.setData({
        loading: true,
        articles: []
      });
    }

    // TODO: 调用后端API获取文章数据
    // 这里使用模拟数据
    setTimeout(() => {
      const mockArticles = this.generateMockArticles();
      
      this.setData({
        articles: refresh ? mockArticles : [...this.data.articles, ...mockArticles],
        loading: false,
        hasMore: mockArticles.length === this.data.pageSize
      });

      if (refresh) {
        wx.stopPullDownRefresh();
      }
    }, 1000);

    // 实际API调用示例
    /*
    wx.request({
      url: 'https://your-api.com/articles',
      method: 'GET',
      data: {
        category: this.data.currentCategory,
        page: this.data.page,
        pageSize: this.data.pageSize
      },
      success: (res) => {
        if (res.data.code === 200) {
          const articles = res.data.data.articles || [];
          this.setData({
            articles: refresh ? articles : [...this.data.articles, ...articles],
            loading: false,
            hasMore: articles.length === this.data.pageSize
          });
        }
      },
      fail: (err) => {
        console.error('获取文章失败:', err);
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      },
      complete: () => {
        if (refresh) {
          wx.stopPullDownRefresh();
        }
      }
    });
    */
  },

  /**
   * 加载更多文章
   */
  loadMoreArticles() {
    this.setData({
      loadingMore: true,
      page: this.data.page + 1
    });

    // TODO: 调用后端API获取更多文章
    setTimeout(() => {
      const mockArticles = this.generateMockArticles();
      
      this.setData({
        articles: [...this.data.articles, ...mockArticles],
        loadingMore: false,
        hasMore: mockArticles.length === this.data.pageSize
      });
    }, 1000);
  },

  /**
   * 生成模拟文章数据
   */
  generateMockArticles() {
    const titles = [
      '美团外卖新用户专享优惠，首单立减20元',
      '探店攻略：这些网红餐厅值得一试',
      '周末出游指南：精选本地热门景点',
      '美食达人推荐：不可错过的特色小吃',
      '生活小贴士：如何用美团省钱购物',
      '旅行日记：一场说走就走的周边游',
      '美团优选：本周最值得买的商品清单',
      '餐厅测评：性价比超高的连锁品牌',
      '购物攻略：双11必买清单大公开',
      '本地生活：发现身边的美好'
    ];

    const summaries = [
      '新用户注册美团外卖即可享受首单立减20元优惠，还有更多惊喜等你发现...',
      '为大家精选了几家最近很火的网红餐厅，从环境到口味都有详细介绍...',
      '周末不知道去哪里？这份本地热门景点攻略帮你解决选择困难症...',
      '作为一名资深美食达人，今天要为大家推荐几款不可错过的特色小吃...',
      '教你如何巧用美团各种优惠活动，让你的生活更省钱更便利...',
      '一次意外的周边游经历，发现了很多平时忽略的美丽风景...',
      '本周美团优选商品推荐，从日用品到美食应有尽有，性价比超高...',
      '实地探访了几家连锁餐厅，为大家带来最真实的用餐体验分享...',
      '双11购物节即将到来，提前为大家整理了最值得买的商品清单...',
      '用心发现身边的美好，分享本地生活的点点滴滴...'
    ];

    const authors = [
      { name: '美食探店王', avatar: '/images/avatar1.jpg' },
      { name: '生活达人', avatar: '/images/avatar2.jpg' },
      { name: '旅行小助手', avatar: '/images/avatar3.jpg' },
      { name: '购物专家', avatar: '/images/avatar4.jpg' },
      { name: '本地向导', avatar: '/images/avatar5.jpg' }
    ];

    const tags = ['热门', '推荐', '新品', '优惠', '攻略'];
    const images = [
      '/images/article1.jpg',
      '/images/article2.jpg',
      '/images/article3.jpg',
      '/images/article4.jpg',
      '/images/article5.jpg'
    ];

    const articles = [];
    const count = Math.min(this.data.pageSize, titles.length);
    
    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * titles.length);
      const author = authors[Math.floor(Math.random() * authors.length)];
      
      articles.push({
        id: Date.now() + i,
        title: titles[randomIndex],
        summary: summaries[randomIndex],
        coverImage: images[Math.floor(Math.random() * images.length)],
        authorName: author.name,
        authorAvatar: author.avatar,
        publishTime: this.formatTime(new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)),
        readCount: Math.floor(Math.random() * 10000) + 100,
        tag: Math.random() > 0.5 ? tags[Math.floor(Math.random() * tags.length)] : '',
        category: this.data.currentCategory,
        // 模拟公众号文章链接
        articleUrl: 'https://mp.weixin.qq.com/s/example',
        // 公众号信息
        officialAccount: {
          appId: 'wx1234567890',
          path: 'pages/article/article?id=' + (Date.now() + i)
        }
      });
    }

    return articles;
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return minutes + '分钟前';
    } else if (hours < 24) {
      return hours + '小时前';
    } else if (days < 7) {
      return days + '天前';
    } else {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  },

  /**
   * 点击分类
   */
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id;
    if (categoryId === this.data.currentCategory) {
      return;
    }

    this.setData({
      currentCategory: categoryId,
      page: 1,
      hasMore: true
    });

    this.loadArticles(true);
  },

  /**
   * 点击文章
   */
  onArticleTap(e) {
    const article = e.currentTarget.dataset.article;
    
    // 根据文章类型进行不同的跳转处理
    if (article.officialAccount && article.officialAccount.appId) {
      // 跳转到公众号文章
      wx.navigateToMiniProgram({
        appId: article.officialAccount.appId,
        path: article.officialAccount.path,
        success: () => {
          console.log('跳转公众号文章成功');
          // 记录阅读行为
          this.recordReadBehavior(article.id);
        },
        fail: (err) => {
          console.error('跳转公众号文章失败:', err);
          // 降级处理：使用webview打开
          if (article.articleUrl) {
            wx.navigateTo({
              url: `/pages/webview/webview?url=${encodeURIComponent(article.articleUrl)}&title=${encodeURIComponent(article.title)}`
            });
          } else {
            wx.showToast({
              title: '文章暂时无法打开',
              icon: 'none'
            });
          }
        }
      });
    } else if (article.articleUrl) {
      // 使用webview打开外部链接
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(article.articleUrl)}&title=${encodeURIComponent(article.title)}`
      });
    } else {
      // 跳转到内部文章详情页
      wx.navigateTo({
        url: `/pages/article/article?id=${article.id}`
      });
    }
  },

  /**
   * 记录阅读行为
   */
  recordReadBehavior(articleId) {
    // TODO: 调用后端API记录用户阅读行为
    console.log('记录阅读行为:', articleId);
    
    /*
    wx.request({
      url: 'https://your-api.com/user/read-behavior',
      method: 'POST',
      data: {
        articleId: articleId,
        readTime: new Date().toISOString()
      },
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
      },
      success: (res) => {
        console.log('记录阅读行为成功');
      },
      fail: (err) => {
        console.error('记录阅读行为失败:', err);
      }
    });
    */
  }
});