<!--pages/topic/topic.wxml-->
<view class="container">
  <!-- 顶部分类导航 -->
  <view class="category-nav">
    <scroll-view class="nav-scroll" scroll-x="true" show-scrollbar="false">
      <view class="nav-list">
        <view 
          class="nav-item {{currentCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          bindtap="onCategoryTap"
          data-id="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 内容列表 -->
  <view class="content-list">
    <!-- 文章列表 -->
    <view 
      class="article-item"
      wx:for="{{articles}}"
      wx:key="id"
      bindtap="onArticleTap"
      data-article="{{item}}"
    >
      <!-- 文章封面 -->
      <view class="article-cover">
        <image class="cover-image" src="{{item.coverImage}}" mode="aspectFill"></image>
        <view class="article-tag" wx:if="{{item.tag}}">{{item.tag}}</view>
      </view>
      
      <!-- 文章信息 -->
      <view class="article-info">
        <view class="article-title">{{item.title}}</view>
        <view class="article-summary">{{item.summary}}</view>
        
        <!-- 文章底部信息 -->
        <view class="article-footer">
          <view class="author-info">
            <image class="author-avatar" src="{{item.authorAvatar}}" mode="aspectFill"></image>
            <text class="author-name">{{item.authorName}}</text>
          </view>
          <view class="article-meta">
            <text class="publish-time">{{item.publishTime}}</text>
            <view class="meta-stats">
              <text class="read-count">{{item.readCount}}阅读</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{articles.length === 0 && !loading}}">
      <image class="empty-icon" src="/images/empty-article.png"></image>
      <text class="empty-text">暂无相关内容</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-item" wx:for="{{[1,2,3]}}" wx:key="*this">
        <view class="loading-cover"></view>
        <view class="loading-content">
          <view class="loading-title"></view>
          <view class="loading-summary"></view>
          <view class="loading-footer">
            <view class="loading-avatar"></view>
            <view class="loading-meta"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && articles.length > 0}}">
      <text class="load-more-text">{{loadingMore ? '加载中...' : '上拉加载更多'}}</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && articles.length > 0}}">
      <text class="no-more-text">没有更多内容了</text>
    </view>
  </view>
</view>