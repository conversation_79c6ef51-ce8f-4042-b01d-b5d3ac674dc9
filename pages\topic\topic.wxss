/* pages/topic/topic.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 分类导航 */
.category-nav {
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-scroll {
  white-space: nowrap;
}

.nav-list {
  display: flex;
  padding: 0 30rpx;
}

.nav-item {
  flex-shrink: 0;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f8f8f8;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.nav-item.active {
  color: #fff;
  background-color: #ff6b35;
  font-weight: bold;
}

.nav-item:last-child {
  margin-right: 30rpx;
}

/* 内容列表 */
.content-list {
  padding: 20rpx 30rpx;
}

/* 文章项 */
.article-item {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.article-item:active {
  transform: scale(0.98);
}

/* 文章封面 */
.article-cover {
  position: relative;
  width: 100%;
  height: 360rpx;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.article-tag {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f00 100%);
  color: #fff;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
}

/* 文章信息 */
.article-info {
  padding: 30rpx;
}

.article-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.article-summary {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 30rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

/* 文章底部 */
.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

.author-name {
  font-size: 24rpx;
  color: #666;
}

.article-meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.publish-time {
  margin-right: 20rpx;
}

.meta-stats {
  display: flex;
  align-items: center;
}

.read-count {
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  padding: 20rpx 0;
}

.loading-item {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.loading-cover {
  width: 100%;
  height: 360rpx;
  background-color: #f0f0f0;
}

.loading-content {
  padding: 30rpx;
}

.loading-title {
  width: 80%;
  height: 32rpx;
  background-color: #f0f0f0;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.loading-summary {
  width: 100%;
  height: 80rpx;
  background-color: #f0f0f0;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.loading-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-avatar {
  width: 40rpx;
  height: 40rpx;
  background-color: #f0f0f0;
  border-radius: 20rpx;
}

.loading-meta {
  width: 200rpx;
  height: 24rpx;
  background-color: #f0f0f0;
  border-radius: 12rpx;
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .article-cover {
    height: 300rpx;
  }
  
  .article-info {
    padding: 24rpx;
  }
  
  .article-title {
    font-size: 30rpx;
  }
  
  .article-summary {
    font-size: 24rpx;
  }
}

/* 安全区域适配 */
.container {
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}