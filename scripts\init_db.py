#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建SQLite数据库和初始化表结构
"""

import os
import sys
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_database_tables():
    """创建数据库表"""
    
    # 确保数据目录存在
    data_dir = "data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    db_path = os.path.join(data_dir, "database.db")
    
    # 连接数据库（如果不存在会自动创建）
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 创建用户表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                openid VARCHAR(100) UNIQUE NOT NULL,
                unionid VARCHAR(100),
                nickname VARCHAR(100),
                avatar VARCHAR(500),
                phone VARCHAR(20),
                gender INTEGER DEFAULT 0,
                city VARCHAR(50),
                province VARCHAR(50),
                country VARCHAR(50),
                status BOOLEAN DEFAULT 1,
                total_commission REAL DEFAULT 0.0,
                total_orders INTEGER DEFAULT 0,
                last_login_time DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建内容表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS contents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                type VARCHAR(20) NOT NULL,
                title VARCHAR(200) NOT NULL,
                content TEXT,
                images JSON,
                video_url VARCHAR(500),
                tags JSON,
                category_id INTEGER,
                view_count INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                comment_count INTEGER DEFAULT 0,
                share_count INTEGER DEFAULT 0,
                status INTEGER DEFAULT 1,
                is_featured BOOLEAN DEFAULT 0,
                published_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)
        
        # 创建收藏表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS favorites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                target_type VARCHAR(20) NOT NULL,
                target_id VARCHAR(50) NOT NULL,
                target_data JSON,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                UNIQUE(user_id, target_type, target_id)
            )
        """)
        
        # 创建统计表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                event_type VARCHAR(50) NOT NULL,
                target_type VARCHAR(20),
                target_id VARCHAR(50),
                extra_data JSON,
                ip_address VARCHAR(45),
                user_agent VARCHAR(500),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)
        
        # 创建广告展示记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ad_impressions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ad_id VARCHAR(50) NOT NULL,
                user_id INTEGER,
                position VARCHAR(50),
                impression_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent VARCHAR(500),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)
        
        # 创建广告点击记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ad_clicks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ad_id VARCHAR(50) NOT NULL,
                user_id INTEGER,
                position VARCHAR(50),
                click_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent VARCHAR(500),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)
        
        print("✓ 数据库表创建完成")
        
    except Exception as e:
        print(f"✗ 创建数据库表失败: {e}")
        return False
    
    finally:
        cursor.close()
        conn.close()
    
    return True

def create_indexes():
    """创建数据库索引"""
    
    db_path = "data/database.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 用户表索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_openid ON users(openid)",
            "CREATE INDEX IF NOT EXISTS idx_users_unionid ON users(unionid)",
            "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
            "CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)",
            
            # 内容表索引
            "CREATE INDEX IF NOT EXISTS idx_contents_user_id ON contents(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_contents_type ON contents(type)",
            "CREATE INDEX IF NOT EXISTS idx_contents_status ON contents(status)",
            "CREATE INDEX IF NOT EXISTS idx_contents_published_at ON contents(published_at)",
            "CREATE INDEX IF NOT EXISTS idx_contents_is_featured ON contents(is_featured)",
            
            # 收藏表索引
            "CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_favorites_target_type ON favorites(target_type)",
            "CREATE INDEX IF NOT EXISTS idx_favorites_created_at ON favorites(created_at)",
            
            # 统计表索引
            "CREATE INDEX IF NOT EXISTS idx_statistics_user_id ON statistics(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_statistics_event_type ON statistics(event_type)",
            "CREATE INDEX IF NOT EXISTS idx_statistics_created_at ON statistics(created_at)",
            
            # 广告表索引
            "CREATE INDEX IF NOT EXISTS idx_ad_impressions_ad_id ON ad_impressions(ad_id)",
            "CREATE INDEX IF NOT EXISTS idx_ad_impressions_user_id ON ad_impressions(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_ad_impressions_time ON ad_impressions(impression_time)",
            "CREATE INDEX IF NOT EXISTS idx_ad_clicks_ad_id ON ad_clicks(ad_id)",
            "CREATE INDEX IF NOT EXISTS idx_ad_clicks_user_id ON ad_clicks(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_ad_clicks_time ON ad_clicks(click_time)",
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("✓ 数据库索引创建完成")
        
    except Exception as e:
        print(f"✗ 创建数据库索引失败: {e}")
        return False
    
    finally:
        cursor.close()
        conn.close()
    
    return True

def insert_sample_data():
    """插入示例数据"""
    
    db_path = "data/database.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 插入示例用户
        cursor.execute("""
            INSERT OR IGNORE INTO users (
                openid, nickname, avatar, city, province, country
            ) VALUES (
                'sample_openid_001', 
                '示例用户', 
                'https://example.com/avatar.jpg',
                '北京',
                '北京市',
                '中国'
            )
        """)
        
        # 插入示例内容
        cursor.execute("""
            INSERT OR IGNORE INTO contents (
                user_id, type, title, content, status, is_featured, published_at
            ) VALUES (
                1,
                'article',
                '欢迎使用美团联盟小程序',
                '这是一个示例文章内容，介绍如何使用美团联盟小程序进行推广。',
                1,
                1,
                CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        print("✓ 示例数据插入完成")
        
    except Exception as e:
        print(f"✗ 插入示例数据失败: {e}")
        return False
    
    finally:
        cursor.close()
        conn.close()
    
    return True

def main():
    """主函数"""
    print("开始初始化数据库...")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    # 创建数据库表
    if not create_database_tables():
        sys.exit(1)
    
    # 创建索引
    if not create_indexes():
        sys.exit(1)
    
    # 插入示例数据
    if not insert_sample_data():
        sys.exit(1)
    
    print("-" * 50)
    print("✓ 数据库初始化完成！")
    print("数据库文件位置: data/database.db")
    print("现在可以启动后端服务了：")
    print("  uvicorn app.main:app --reload")

if __name__ == "__main__":
    main()
