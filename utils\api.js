/**
 * API服务封装
 * 统一管理所有API调用
 */

const { request, showError, showLoading, hideLoading } = require('./config');

/**
 * 用户认证相关API
 */
const authAPI = {
  // 微信登录
  login(code, userInfo) {
    return request({
      url: '/api/auth/login',
      method: 'POST',
      data: { code, ...userInfo }
    });
  },
  
  // 刷新Token
  refreshToken() {
    return request({
      url: '/api/auth/refresh',
      method: 'POST'
    });
  }
};

/**
 * 用户管理相关API
 */
const userAPI = {
  // 获取用户信息
  getProfile() {
    return request({
      url: '/api/users/profile',
      method: 'GET'
    });
  },
  
  // 更新用户信息
  updateProfile(data) {
    return request({
      url: '/api/users/profile',
      method: 'PUT',
      data
    });
  },
  
  // 获取用户统计
  getStats() {
    return request({
      url: '/api/users/stats',
      method: 'GET'
    });
  }
};

/**
 * 商品相关API
 */
const productAPI = {
  // 获取商品列表
  getList(params = {}) {
    return request({
      url: '/api/products/list',
      method: 'GET',
      data: params
    });
  },
  
  // 获取商品详情
  getDetail(productId) {
    return request({
      url: `/api/products/${productId}`,
      method: 'GET'
    });
  },
  
  // 生成推广链接
  generatePromotionLink(productId, sid = '') {
    return request({
      url: `/api/products/${productId}/promotion-link`,
      method: 'POST',
      data: { sid }
    });
  }
};

/**
 * 收藏相关API
 */
const favoriteAPI = {
  // 获取收藏列表
  getList(params = {}) {
    return request({
      url: '/api/favorites/',
      method: 'GET',
      data: params
    });
  },
  
  // 添加收藏
  add(targetType, targetId, targetData) {
    return request({
      url: '/api/favorites/',
      method: 'POST',
      data: { target_type: targetType, target_id: targetId, target_data: targetData }
    });
  },
  
  // 取消收藏
  remove(favoriteId) {
    return request({
      url: `/api/favorites/${favoriteId}`,
      method: 'DELETE'
    });
  }
};

/**
 * 内容相关API
 */
const contentAPI = {
  // 获取内容列表
  getList(params = {}) {
    return request({
      url: '/api/content/list',
      method: 'GET',
      data: params
    });
  },
  
  // 获取内容详情
  getDetail(contentId) {
    return request({
      url: `/api/content/${contentId}`,
      method: 'GET'
    });
  },
  
  // 创建内容
  create(data) {
    return request({
      url: '/api/content/',
      method: 'POST',
      data
    });
  },
  
  // 点赞内容
  like(contentId) {
    return request({
      url: `/api/content/${contentId}/like`,
      method: 'POST'
    });
  }
};

/**
 * 广告相关API
 */
const adAPI = {
  // 获取广告列表
  getAds(position, limit = 10) {
    return request({
      url: '/api/ads/',
      method: 'GET',
      data: { position, limit }
    });
  },
  
  // 记录广告展示
  recordImpression(adId) {
    return request({
      url: `/api/ads/${adId}/impression`,
      method: 'POST'
    });
  },
  
  // 记录广告点击
  recordClick(adId) {
    return request({
      url: `/api/ads/${adId}/click`,
      method: 'POST'
    });
  }
};

/**
 * 封装的业务方法
 */
const businessAPI = {
  // 用户登录流程
  async userLogin() {
    try {
      showLoading('登录中...');
      
      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        });
      });
      
      if (!loginRes.code) {
        throw new Error('获取登录code失败');
      }
      
      // 获取用户信息
      const userInfoRes = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: resolve,
          fail: reject
        });
      });
      
      // 调用后端登录接口
      const result = await authAPI.login(loginRes.code, userInfoRes.userInfo);
      
      // 保存用户信息和token
      wx.setStorageSync('user_token', result.data.access_token);
      wx.setStorageSync('user_info', result.data.user_info);
      
      hideLoading();
      return result;
      
    } catch (error) {
      hideLoading();
      showError(error.message || '登录失败');
      throw error;
    }
  },
  
  // 获取商品列表（带加载状态）
  async getProductList(params = {}, showLoadingUI = true) {
    try {
      if (showLoadingUI) {
        showLoading('加载中...');
      }
      
      const result = await productAPI.getList(params);
      
      if (showLoadingUI) {
        hideLoading();
      }
      
      return result;
      
    } catch (error) {
      if (showLoadingUI) {
        hideLoading();
      }
      showError(error.message || '获取商品列表失败');
      throw error;
    }
  },
  
  // 生成推广链接并跳转
  async promoteProduct(productId, productInfo) {
    try {
      showLoading('生成推广链接...');
      
      // 生成推广链接
      const result = await productAPI.generatePromotionLink(productId);
      
      hideLoading();
      
      // 跳转到美团小程序
      const { navigateToMeituan } = require('./config');
      navigateToMeituan({
        path: result.data.promotion_url,
        extraData: {
          product_id: productId,
          product_name: productInfo.name
        }
      });
      
    } catch (error) {
      hideLoading();
      showError(error.message || '生成推广链接失败');
    }
  },
  
  // 收藏商品
  async favoriteProduct(productId, productData) {
    try {
      await favoriteAPI.add('product', productId, productData);
      wx.showToast({
        title: '收藏成功',
        icon: 'success'
      });
    } catch (error) {
      showError(error.message || '收藏失败');
    }
  },
  
  // 加载广告
  async loadAds(position) {
    try {
      const result = await adAPI.getAds(position);
      
      // 记录广告展示
      if (result.data.ads && result.data.ads.length > 0) {
        result.data.ads.forEach(ad => {
          adAPI.recordImpression(ad.id).catch(console.error);
        });
      }
      
      return result.data.ads || [];
      
    } catch (error) {
      console.error('加载广告失败:', error);
      return [];
    }
  }
};

module.exports = {
  authAPI,
  userAPI,
  productAPI,
  favoriteAPI,
  contentAPI,
  adAPI,
  businessAPI
};
