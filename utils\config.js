/**
 * 小程序统一配置文件
 * 集中管理API地址、小程序配置等
 */

// 环境配置
const ENV = 'development'; // development | production

// API配置
const API_CONFIG = {
  development: {
    baseUrl: 'http://localhost:8000',
    timeout: 10000
  },
  production: {
    baseUrl: 'https://your-api-domain.com',
    timeout: 15000
  }
};

// 美团联盟配置
const MEITUAN_CONFIG = {
  appKey: 'your_meituan_app_key',
  miniProgramAppId: 'wxde8ac0a21135c07d' // 美团小程序AppID
};

// 微信小程序配置
const WECHAT_CONFIG = {
  appId: 'your_wechat_app_id'
};

// 页面配置
const PAGE_CONFIG = {
  // 首页配置
  index: {
    bannerAutoPlay: true,
    bannerInterval: 3000,
    productPageSize: 20
  },
  // 商品页配置
  products: {
    pageSize: 20,
    categories: [
      { id: 1, name: '美食', icon: 'food' },
      { id: 2, name: '外卖', icon: 'takeout' },
      { id: 3, name: '酒店', icon: 'hotel' },
      { id: 4, name: '旅游', icon: 'travel' }
    ]
  },
  // 社区配置
  community: {
    pageSize: 10,
    maxImageCount: 9,
    maxContentLength: 1000
  }
};

// 广告位配置
const AD_POSITIONS = {
  HOME_TOP: 'home_top',
  HOME_MIDDLE: 'home_middle',
  PRODUCT_LIST: 'product_list',
  COMMUNITY_TOP: 'community_top',
  PROFILE_TOP: 'profile_top'
};

// 存储键名配置
const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_INFO: 'user_info',
  LOCATION: 'user_location',
  SEARCH_HISTORY: 'search_history',
  BROWSE_HISTORY: 'browse_history'
};

// 错误码配置
const ERROR_CODES = {
  SUCCESS: 0,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  NETWORK_ERROR: -1,
  LOGIN_REQUIRED: 1001,
  INVALID_PARAMS: 1002
};

// 获取当前环境的API配置
function getApiConfig() {
  return API_CONFIG[ENV];
}

// 获取完整的API地址
function getApiUrl(path) {
  const config = getApiConfig();
  return `${config.baseUrl}${path}`;
}

// 获取请求头
function getRequestHeaders() {
  const token = wx.getStorageSync(STORAGE_KEYS.USER_TOKEN);
  const headers = {
    'Content-Type': 'application/json'
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

// 统一请求方法
function request(options) {
  const config = getApiConfig();
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: getApiUrl(options.url),
      method: options.method || 'GET',
      data: options.data || {},
      header: getRequestHeaders(),
      timeout: config.timeout,
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === ERROR_CODES.SUCCESS) {
            resolve(res.data);
          } else {
            reject(res.data);
          }
        } else if (res.statusCode === 401) {
          // Token过期，清除本地存储并跳转登录
          wx.removeStorageSync(STORAGE_KEYS.USER_TOKEN);
          wx.removeStorageSync(STORAGE_KEYS.USER_INFO);
          reject({ code: ERROR_CODES.UNAUTHORIZED, message: '登录已过期' });
        } else {
          reject({ code: res.statusCode, message: '网络请求失败' });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        reject({ code: ERROR_CODES.NETWORK_ERROR, message: '网络连接失败' });
      }
    });
  });
}

// 显示错误提示
function showError(error) {
  const message = error.message || '操作失败';
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
}

// 显示成功提示
function showSuccess(message = '操作成功') {
  wx.showToast({
    title: message,
    icon: 'success',
    duration: 1500
  });
}

// 显示加载中
function showLoading(title = '加载中...') {
  wx.showLoading({
    title: title,
    mask: true
  });
}

// 隐藏加载中
function hideLoading() {
  wx.hideLoading();
}

// 检查登录状态
function checkLogin() {
  const token = wx.getStorageSync(STORAGE_KEYS.USER_TOKEN);
  return !!token;
}

// 跳转到美团小程序
function navigateToMeituan(params = {}) {
  wx.navigateToMiniProgram({
    appId: MEITUAN_CONFIG.miniProgramAppId,
    path: params.path || '',
    extraData: params.extraData || {},
    envVersion: 'release',
    success: () => {
      console.log('跳转美团小程序成功');
    },
    fail: (err) => {
      console.error('跳转美团小程序失败:', err);
      showError('跳转失败，请稍后重试');
    }
  });
}

// 格式化时间
function formatTime(timestamp) {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    return date.toLocaleDateString();
  }
}

// 格式化数字
function formatNumber(num) {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  } else {
    return num.toString();
  }
}

// 导出配置和方法
module.exports = {
  // 配置
  ENV,
  MEITUAN_CONFIG,
  WECHAT_CONFIG,
  PAGE_CONFIG,
  AD_POSITIONS,
  STORAGE_KEYS,
  ERROR_CODES,
  
  // 方法
  getApiConfig,
  getApiUrl,
  getRequestHeaders,
  request,
  showError,
  showSuccess,
  showLoading,
  hideLoading,
  checkLogin,
  navigateToMeituan,
  formatTime,
  formatNumber
};
