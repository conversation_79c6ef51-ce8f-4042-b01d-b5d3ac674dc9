# 安全指南

## 概述

本文档详细说明了美团联盟小程序项目的安全策略、防护措施和最佳实践，确保应用程序和用户数据的安全性。

## 安全架构

### 整体安全策略

```
┌─────────────────────────────────────────────────────────────┐
│                        安全防护层次                          │
├─────────────────────────────────────────────────────────────┤
│ 1. 网络安全层    │ HTTPS, WAF, DDoS防护, 防火墙           │
│ 2. 应用安全层    │ 认证授权, 输入验证, 会话管理           │
│ 3. 数据安全层    │ 加密存储, 访问控制, 数据脱敏           │
│ 4. 基础设施安全  │ 服务器加固, 容器安全, 监控审计         │
└─────────────────────────────────────────────────────────────┘
```

### 威胁模型

**主要威胁类型**:
1. **注入攻击**: SQL注入, NoSQL注入, 命令注入
2. **跨站脚本**: XSS攻击, CSRF攻击
3. **认证绕过**: 弱密码, 会话劫持, 权限提升
4. **数据泄露**: 敏感信息暴露, 数据库泄露
5. **拒绝服务**: DDoS攻击, 资源耗尽
6. **业务逻辑**: 越权访问, 业务流程绕过

## 认证与授权

### JWT认证实现

**JWT配置**:
```javascript
// src/utils/jwt.js
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

/**
 * JWT工具类
 * 提供安全的Token生成和验证
 */
class JWTUtil {
  constructor() {
    this.secret = process.env.JWT_SECRET || this.generateSecret();
    this.issuer = process.env.JWT_ISSUER || 'meituan-alliance';
    this.audience = process.env.JWT_AUDIENCE || 'meituan-alliance-users';
    
    // Token配置
    this.accessTokenExpiry = '15m';  // 访问令牌15分钟
    this.refreshTokenExpiry = '7d';  // 刷新令牌7天
  }
  
  /**
   * 生成安全的密钥
   */
  generateSecret() {
    const secret = crypto.randomBytes(64).toString('hex');
    console.warn('警告: 使用随机生成的JWT密钥，请在生产环境中设置JWT_SECRET环境变量');
    return secret;
  }
  
  /**
   * 生成访问令牌
   */
  generateAccessToken(payload) {
    const tokenPayload = {
      ...payload,
      type: 'access',
      iat: Math.floor(Date.now() / 1000),
      jti: crypto.randomUUID() // JWT ID
    };
    
    return jwt.sign(tokenPayload, this.secret, {
      expiresIn: this.accessTokenExpiry,
      issuer: this.issuer,
      audience: this.audience,
      algorithm: 'HS256'
    });
  }
  
  /**
   * 生成刷新令牌
   */
  generateRefreshToken(payload) {
    const tokenPayload = {
      userId: payload.userId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      jti: crypto.randomUUID()
    };
    
    return jwt.sign(tokenPayload, this.secret, {
      expiresIn: this.refreshTokenExpiry,
      issuer: this.issuer,
      audience: this.audience,
      algorithm: 'HS256'
    });
  }
  
  /**
   * 验证令牌
   */
  verifyToken(token, type = 'access') {
    try {
      const decoded = jwt.verify(token, this.secret, {
        issuer: this.issuer,
        audience: this.audience,
        algorithms: ['HS256']
      });
      
      // 验证令牌类型
      if (decoded.type !== type) {
        throw new Error('令牌类型不匹配');
      }
      
      return decoded;
      
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('令牌已过期');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('无效的令牌');
      } else {
        throw error;
      }
    }
  }
  
  /**
   * 刷新访问令牌
   */
  async refreshAccessToken(refreshToken) {
    try {
      const decoded = this.verifyToken(refreshToken, 'refresh');
      
      // 检查刷新令牌是否在黑名单中
      const isBlacklisted = await this.isTokenBlacklisted(decoded.jti);
      if (isBlacklisted) {
        throw new Error('刷新令牌已失效');
      }
      
      // 生成新的访问令牌
      const newAccessToken = this.generateAccessToken({
        userId: decoded.userId
      });
      
      return newAccessToken;
      
    } catch (error) {
      throw new Error('刷新令牌失败: ' + error.message);
    }
  }
  
  /**
   * 将令牌加入黑名单
   */
  async blacklistToken(token) {
    try {
      const decoded = jwt.decode(token);
      if (decoded && decoded.jti) {
        const redisClient = require('./redis');
        const expiry = decoded.exp - Math.floor(Date.now() / 1000);
        
        if (expiry > 0) {
          await redisClient.setex(`blacklist:${decoded.jti}`, expiry, '1');
        }
      }
    } catch (error) {
      console.error('令牌黑名单添加失败:', error);
    }
  }
  
  /**
   * 检查令牌是否在黑名单中
   */
  async isTokenBlacklisted(jti) {
    try {
      const redisClient = require('./redis');
      const result = await redisClient.get(`blacklist:${jti}`);
      return result === '1';
    } catch (error) {
      console.error('黑名单检查失败:', error);
      return false;
    }
  }
}

module.exports = new JWTUtil();
```

### 认证中间件

```javascript
// src/middleware/auth.js
const jwtUtil = require('../utils/jwt');
const logger = require('../utils/logger');

/**
 * 认证中间件
 * 验证JWT令牌并设置用户信息
 */
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        code: 401,
        message: '缺少认证令牌'
      });
    }
    
    const token = authHeader.substring(7);
    
    // 验证令牌
    const decoded = jwtUtil.verifyToken(token);
    
    // 检查令牌是否在黑名单中
    const isBlacklisted = await jwtUtil.isTokenBlacklisted(decoded.jti);
    if (isBlacklisted) {
      return res.status(401).json({
        code: 401,
        message: '令牌已失效'
      });
    }
    
    // 设置用户信息
    req.user = {
      id: decoded.userId,
      jti: decoded.jti
    };
    
    // 记录访问日志
    logger.info('用户认证成功', {
      userId: decoded.userId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url
    });
    
    next();
    
  } catch (error) {
    logger.warn('认证失败', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url
    });
    
    return res.status(401).json({
      code: 401,
      message: '认证失败: ' + error.message
    });
  }
};

/**
 * 可选认证中间件
 * 如果有令牌则验证，没有则跳过
 */
const optionalAuth = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authenticate(req, res, next);
  }
  
  next();
};

/**
 * 权限检查中间件
 */
const authorize = (requiredPermissions = []) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          message: '需要认证'
        });
      }
      
      // 获取用户权限
      const userPermissions = await getUserPermissions(req.user.id);
      
      // 检查权限
      const hasPermission = requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      );
      
      if (!hasPermission) {
        logger.warn('权限不足', {
          userId: req.user.id,
          requiredPermissions,
          userPermissions,
          url: req.url
        });
        
        return res.status(403).json({
          code: 403,
          message: '权限不足'
        });
      }
      
      next();
      
    } catch (error) {
      logger.error('权限检查失败:', error);
      
      return res.status(500).json({
        code: 500,
        message: '权限检查失败'
      });
    }
  };
};

/**
 * 获取用户权限
 */
async function getUserPermissions(userId) {
  // 这里应该从数据库获取用户权限
  // 简化示例，实际应用中需要实现完整的权限系统
  return ['read', 'write'];
}

module.exports = {
  authenticate,
  optionalAuth,
  authorize
};
```

## 输入验证与过滤

### 输入验证中间件

```javascript
// src/middleware/validation.js
const Joi = require('joi');
const DOMPurify = require('isomorphic-dompurify');
const logger = require('../utils/logger');

/**
 * 输入验证中间件
 */
const validate = (schema, source = 'body') => {
  return (req, res, next) => {
    const data = req[source];
    
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true
    });
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      logger.warn('输入验证失败', {
        errors,
        data,
        ip: req.ip,
        url: req.url
      });
      
      return res.status(400).json({
        code: 400,
        message: '输入验证失败',
        errors
      });
    }
    
    // 更新请求数据
    req[source] = value;
    next();
  };
};

/**
 * XSS防护中间件
 */
const sanitizeInput = (req, res, next) => {
  try {
    // 清理请求体中的HTML
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }
    
    // 清理查询参数
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }
    
    next();
    
  } catch (error) {
    logger.error('输入清理失败:', error);
    next();
  }
};

/**
 * 递归清理对象中的HTML
 */
function sanitizeObject(obj) {
  if (typeof obj === 'string') {
    return DOMPurify.sanitize(obj, { ALLOWED_TAGS: [] });
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}

/**
 * SQL注入防护
 */
const preventSQLInjection = (req, res, next) => {
  const sqlPatterns = [
    /('|(\-\-)|(;)|(\||\|)|(\*|\*))/i,
    /(union|select|insert|delete|update|drop|create|alter|exec|execute)/i,
    /(script|javascript|vbscript|onload|onerror|onclick)/i
  ];
  
  const checkValue = (value) => {
    if (typeof value === 'string') {
      return sqlPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };
  
  const checkObject = (obj) => {
    if (typeof obj === 'string') {
      return checkValue(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.some(item => checkObject(item));
    }
    
    if (obj && typeof obj === 'object') {
      return Object.values(obj).some(value => checkObject(value));
    }
    
    return false;
  };
  
  // 检查请求体
  if (req.body && checkObject(req.body)) {
    logger.warn('检测到SQL注入尝试', {
      body: req.body,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url
    });
    
    return res.status(400).json({
      code: 400,
      message: '请求包含非法字符'
    });
  }
  
  // 检查查询参数
  if (req.query && checkObject(req.query)) {
    logger.warn('检测到SQL注入尝试', {
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url
    });
    
    return res.status(400).json({
      code: 400,
      message: '请求包含非法字符'
    });
  }
  
  next();
};

// 常用验证模式
const schemas = {
  // 用户注册
  userRegister: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required(),
    nickname: Joi.string().min(2).max(50).optional()
  }),
  
  // 用户登录
  userLogin: Joi.object({
    username: Joi.string().required(),
    password: Joi.string().required()
  }),
  
  // 商品搜索
  productSearch: Joi.object({
    keyword: Joi.string().max(100).optional(),
    category: Joi.string().max(50).optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }),
  
  // 社区帖子
  communityPost: Joi.object({
    title: Joi.string().min(5).max(200).required(),
    content: Joi.string().min(10).max(5000).required(),
    tags: Joi.array().items(Joi.string().max(20)).max(5).optional(),
    images: Joi.array().items(Joi.string().uri()).max(9).optional()
  })
};

module.exports = {
  validate,
  sanitizeInput,
  preventSQLInjection,
  schemas
};
```

## 数据安全

### 数据加密

```javascript
// src/utils/encryption.js
const crypto = require('crypto');
const bcrypt = require('bcrypt');

/**
 * 数据加密工具类
 */
class EncryptionUtil {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
    this.saltRounds = 12;
    
    // 从环境变量获取主密钥
    this.masterKey = this.getMasterKey();
  }
  
  /**
   * 获取主密钥
   */
  getMasterKey() {
    const key = process.env.ENCRYPTION_KEY;
    
    if (!key) {
      throw new Error('未设置加密密钥，请设置ENCRYPTION_KEY环境变量');
    }
    
    if (key.length !== 64) {
      throw new Error('加密密钥长度必须为64个字符（32字节的十六进制）');
    }
    
    return Buffer.from(key, 'hex');
  }
  
  /**
   * 加密敏感数据
   */
  encrypt(plaintext) {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipher(this.algorithm, this.masterKey, iv);
      
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      // 组合IV、标签和密文
      const result = iv.toString('hex') + tag.toString('hex') + encrypted;
      
      return result;
      
    } catch (error) {
      throw new Error('数据加密失败: ' + error.message);
    }
  }
  
  /**
   * 解密敏感数据
   */
  decrypt(encryptedData) {
    try {
      // 提取IV、标签和密文
      const iv = Buffer.from(encryptedData.slice(0, this.ivLength * 2), 'hex');
      const tag = Buffer.from(encryptedData.slice(this.ivLength * 2, (this.ivLength + this.tagLength) * 2), 'hex');
      const encrypted = encryptedData.slice((this.ivLength + this.tagLength) * 2);
      
      const decipher = crypto.createDecipher(this.algorithm, this.masterKey, iv);
      decipher.setAuthTag(tag);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
      
    } catch (error) {
      throw new Error('数据解密失败: ' + error.message);
    }
  }
  
  /**
   * 哈希密码
   */
  async hashPassword(password) {
    try {
      const hash = await bcrypt.hash(password, this.saltRounds);
      return hash;
    } catch (error) {
      throw new Error('密码哈希失败: ' + error.message);
    }
  }
  
  /**
   * 验证密码
   */
  async verifyPassword(password, hash) {
    try {
      const isValid = await bcrypt.compare(password, hash);
      return isValid;
    } catch (error) {
      throw new Error('密码验证失败: ' + error.message);
    }
  }
  
  /**
   * 生成安全的随机字符串
   */
  generateSecureRandom(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }
  
  /**
   * 生成HMAC签名
   */
  generateHMAC(data, secret) {
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(data);
    return hmac.digest('hex');
  }
  
  /**
   * 验证HMAC签名
   */
  verifyHMAC(data, signature, secret) {
    const expectedSignature = this.generateHMAC(data, secret);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }
}

module.exports = new EncryptionUtil();
```

### 数据脱敏

```javascript
// src/utils/dataMasking.js
/**
 * 数据脱敏工具
 * 用于保护敏感信息
 */
class DataMasking {
  /**
   * 手机号脱敏
   */
  maskPhone(phone) {
    if (!phone || phone.length < 7) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }
  
  /**
   * 邮箱脱敏
   */
  maskEmail(email) {
    if (!email || !email.includes('@')) return email;
    const [username, domain] = email.split('@');
    const maskedUsername = username.length > 2 
      ? username.substring(0, 2) + '*'.repeat(username.length - 2)
      : username;
    return `${maskedUsername}@${domain}`;
  }
  
  /**
   * 身份证号脱敏
   */
  maskIdCard(idCard) {
    if (!idCard || idCard.length < 8) return idCard;
    return idCard.replace(/(\d{4})\d{10}(\d{4})/, '$1**********$2');
  }
  
  /**
   * 银行卡号脱敏
   */
  maskBankCard(cardNumber) {
    if (!cardNumber || cardNumber.length < 8) return cardNumber;
    return cardNumber.replace(/(\d{4})\d+(\d{4})/, '$1****$2');
  }
  
  /**
   * 姓名脱敏
   */
  maskName(name) {
    if (!name || name.length < 2) return name;
    if (name.length === 2) {
      return name.charAt(0) + '*';
    }
    return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1);
  }
  
  /**
   * IP地址脱敏
   */
  maskIP(ip) {
    if (!ip) return ip;
    return ip.replace(/(\d+\.\d+\.\d+\.)\d+/, '$1***');
  }
  
  /**
   * 通用对象脱敏
   */
  maskObject(obj, fields = []) {
    if (!obj || typeof obj !== 'object') return obj;
    
    const masked = { ...obj };
    
    fields.forEach(field => {
      if (masked[field]) {
        switch (field) {
          case 'phone':
          case 'mobile':
            masked[field] = this.maskPhone(masked[field]);
            break;
          case 'email':
            masked[field] = this.maskEmail(masked[field]);
            break;
          case 'idCard':
          case 'idNumber':
            masked[field] = this.maskIdCard(masked[field]);
            break;
          case 'bankCard':
          case 'cardNumber':
            masked[field] = this.maskBankCard(masked[field]);
            break;
          case 'name':
          case 'realName':
            masked[field] = this.maskName(masked[field]);
            break;
          case 'ip':
            masked[field] = this.maskIP(masked[field]);
            break;
          default:
            // 通用脱敏：显示前2位和后2位
            const value = masked[field].toString();
            if (value.length > 4) {
              masked[field] = value.substring(0, 2) + '*'.repeat(value.length - 4) + value.substring(value.length - 2);
            }
        }
      }
    });
    
    return masked;
  }
}

module.exports = new DataMasking();
```

## 网络安全

### HTTPS配置

```javascript
// src/config/https.js
const fs = require('fs');
const https = require('https');
const express = require('express');

/**
 * HTTPS服务器配置
 */
function createHTTPSServer(app) {
  const options = {
    key: fs.readFileSync(process.env.SSL_KEY_PATH || './ssl/private.key'),
    cert: fs.readFileSync(process.env.SSL_CERT_PATH || './ssl/certificate.crt'),
    
    // 安全配置
    secureProtocol: 'TLSv1_2_method',
    ciphers: [
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES128-SHA256',
      'ECDHE-RSA-AES256-SHA384'
    ].join(':'),
    honorCipherOrder: true
  };
  
  return https.createServer(options, app);
}

/**
 * 安全头中间件
 */
function securityHeaders() {
  return (req, res, next) => {
    // 强制HTTPS
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    
    // 防止点击劫持
    res.setHeader('X-Frame-Options', 'DENY');
    
    // 防止MIME类型嗅探
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // XSS保护
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // 内容安全策略
    res.setHeader('Content-Security-Policy', [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self'",
      "connect-src 'self'",
      "frame-ancestors 'none'"
    ].join('; '));
    
    // 引用者策略
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // 权限策略
    res.setHeader('Permissions-Policy', [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()'
    ].join(', '));
    
    next();
  };
}

module.exports = {
  createHTTPSServer,
  securityHeaders
};
```

### 限流防护

```javascript
// src/middleware/rateLimit.js
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const redis = require('../utils/redis');
const logger = require('../utils/logger');

/**
 * 创建限流中间件
 */
function createRateLimit(options = {}) {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 最大请求数
    message: {
      code: 429,
      message: '请求过于频繁，请稍后再试'
    },
    standardHeaders: true,
    legacyHeaders: false,
    store: new RedisStore({
      sendCommand: (...args) => redis.call(...args)
    }),
    keyGenerator: (req) => {
      // 使用IP和用户ID作为限流键
      return req.user ? `${req.ip}:${req.user.id}` : req.ip;
    },
    onLimitReached: (req, res, options) => {
      logger.warn('触发限流', {
        ip: req.ip,
        userId: req.user?.id,
        url: req.url,
        userAgent: req.get('User-Agent')
      });
    }
  };
  
  return rateLimit({ ...defaultOptions, ...options });
}

// 不同类型的限流配置
const rateLimits = {
  // 通用API限流
  general: createRateLimit({
    windowMs: 15 * 60 * 1000,
    max: 1000
  }),
  
  // 登录限流
  login: createRateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: {
      code: 429,
      message: '登录尝试过于频繁，请15分钟后再试'
    }
  }),
  
  // 注册限流
  register: createRateLimit({
    windowMs: 60 * 60 * 1000,
    max: 3,
    message: {
      code: 429,
      message: '注册过于频繁，请1小时后再试'
    }
  }),
  
  // 发送验证码限流
  sms: createRateLimit({
    windowMs: 60 * 1000,
    max: 1,
    message: {
      code: 429,
      message: '验证码发送过于频繁，请1分钟后再试'
    }
  }),
  
  // 搜索限流
  search: createRateLimit({
    windowMs: 60 * 1000,
    max: 30
  }),
  
  // 上传限流
  upload: createRateLimit({
    windowMs: 60 * 1000,
    max: 10
  })
};

module.exports = rateLimits;
```

## 安全监控

### 安全事件监控

```javascript
// src/utils/securityMonitor.js
const logger = require('./logger');
const redis = require('./redis');

/**
 * 安全监控工具
 */
class SecurityMonitor {
  constructor() {
    this.alertThresholds = {
      failedLogins: 10,        // 失败登录次数
      suspiciousIPs: 5,        // 可疑IP请求次数
      sqlInjectionAttempts: 3, // SQL注入尝试次数
      xssAttempts: 3          // XSS攻击尝试次数
    };
  }
  
  /**
   * 记录安全事件
   */
  async logSecurityEvent(type, details) {
    const event = {
      type,
      timestamp: new Date().toISOString(),
      ...details
    };
    
    // 记录到日志
    logger.warn('安全事件', event);
    
    // 存储到Redis
    await redis.lpush('security:events', JSON.stringify(event));
    await redis.ltrim('security:events', 0, 999); // 保留最近1000条
    
    // 检查是否需要告警
    await this.checkAlerts(type, details);
  }
  
  /**
   * 记录失败登录
   */
  async recordFailedLogin(ip, username) {
    const key = `failed_login:${ip}`;
    const count = await redis.incr(key);
    await redis.expire(key, 3600); // 1小时过期
    
    await this.logSecurityEvent('failed_login', {
      ip,
      username,
      count
    });
    
    // 检查是否需要封禁IP
    if (count >= this.alertThresholds.failedLogins) {
      await this.blockIP(ip, 'too_many_failed_logins');
    }
  }
  
  /**
   * 记录可疑请求
   */
  async recordSuspiciousRequest(ip, type, details) {
    const key = `suspicious:${type}:${ip}`;
    const count = await redis.incr(key);
    await redis.expire(key, 3600);
    
    await this.logSecurityEvent('suspicious_request', {
      ip,
      type,
      count,
      ...details
    });
    
    // 检查是否需要封禁IP
    const threshold = this.alertThresholds[type + 'Attempts'] || 5;
    if (count >= threshold) {
      await this.blockIP(ip, `too_many_${type}_attempts`);
    }
  }
  
  /**
   * 封禁IP
   */
  async blockIP(ip, reason) {
    const blockKey = `blocked_ip:${ip}`;
    const blockDuration = 24 * 3600; // 24小时
    
    await redis.setex(blockKey, blockDuration, JSON.stringify({
      reason,
      blockedAt: new Date().toISOString()
    }));
    
    await this.logSecurityEvent('ip_blocked', {
      ip,
      reason,
      duration: blockDuration
    });
    
    // 发送告警
    await this.sendAlert('IP封禁', {
      ip,
      reason,
      action: 'blocked'
    });
  }
  
  /**
   * 检查IP是否被封禁
   */
  async isIPBlocked(ip) {
    const blockInfo = await redis.get(`blocked_ip:${ip}`);
    return blockInfo !== null;
  }
  
  /**
   * 检查告警条件
   */
  async checkAlerts(type, details) {
    // 实现告警逻辑
    const alertConditions = {
      failed_login: () => details.count >= this.alertThresholds.failedLogins,
      suspicious_request: () => details.count >= 3,
      sql_injection: () => true, // 任何SQL注入尝试都告警
      xss_attack: () => true     // 任何XSS攻击都告警
    };
    
    if (alertConditions[type] && alertConditions[type]()) {
      await this.sendAlert(`安全事件: ${type}`, details);
    }
  }
  
  /**
   * 发送告警
   */
  async sendAlert(title, details) {
    // 这里可以集成邮件、短信、钉钉等告警方式
    logger.error('安全告警', { title, details });
    
    // 存储告警记录
    await redis.lpush('security:alerts', JSON.stringify({
      title,
      details,
      timestamp: new Date().toISOString()
    }));
  }
  
  /**
   * 获取安全统计
   */
  async getSecurityStats() {
    const events = await redis.lrange('security:events', 0, -1);
    const alerts = await redis.lrange('security:alerts', 0, -1);
    
    const eventStats = {};
    events.forEach(eventStr => {
      const event = JSON.parse(eventStr);
      eventStats[event.type] = (eventStats[event.type] || 0) + 1;
    });
    
    return {
      totalEvents: events.length,
      totalAlerts: alerts.length,
      eventsByType: eventStats,
      recentEvents: events.slice(0, 10).map(e => JSON.parse(e)),
      recentAlerts: alerts.slice(0, 5).map(a => JSON.parse(a))
    };
  }
}

module.exports = new SecurityMonitor();
```

### IP封禁中间件

```javascript
// src/middleware/ipBlock.js
const securityMonitor = require('../utils/securityMonitor');
const logger = require('../utils/logger');

/**
 * IP封禁检查中间件
 */
const checkIPBlock = async (req, res, next) => {
  try {
    const ip = req.ip;
    
    // 检查IP是否被封禁
    const isBlocked = await securityMonitor.isIPBlocked(ip);
    
    if (isBlocked) {
      logger.warn('封禁IP访问尝试', {
        ip,
        url: req.url,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(403).json({
        code: 403,
        message: '访问被拒绝'
      });
    }
    
    next();
    
  } catch (error) {
    logger.error('IP封禁检查失败:', error);
    next(); // 出错时不阻止请求
  }
};

module.exports = checkIPBlock;
```

## 安全配置

### 环境变量配置

```bash
# .env.example
# 应用配置
NODE_ENV=production
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=meituan_alliance
DB_USER=app_user
DB_PASSWORD=secure_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-64-characters-long-hex-string
JWT_ISSUER=meituan-alliance
JWT_AUDIENCE=meituan-alliance-users

# 加密配置
ENCRYPTION_KEY=your-32-byte-encryption-key-in-hex-format-64-chars

# SSL证书路径
SSL_KEY_PATH=/path/to/private.key
SSL_CERT_PATH=/path/to/certificate.crt

# 美团联盟API
MEITUAN_APP_KEY=your_meituan_app_key
MEITUAN_APP_SECRET=your_meituan_app_secret

# 微信小程序配置
WX_APP_ID=your_wx_app_id
WX_APP_SECRET=your_wx_app_secret

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/meituan-alliance

# 监控配置
MONITOR_ENABLED=true
ALERT_EMAIL=<EMAIL>
```

### 安全检查清单

**部署前安全检查**:
- [ ] 所有敏感信息已移至环境变量
- [ ] 数据库连接使用强密码
- [ ] JWT密钥足够复杂且唯一
- [ ] SSL证书已正确配置
- [ ] 防火墙规则已设置
- [ ] 不必要的端口已关闭
- [ ] 系统补丁已更新
- [ ] 日志记录已启用
- [ ] 监控告警已配置
- [ ] 备份策略已实施

**代码安全检查**:
- [ ] 输入验证已实现
- [ ] SQL注入防护已启用
- [ ] XSS防护已实现
- [ ] CSRF保护已配置
- [ ] 认证授权正确实现
- [ ] 敏感数据已加密
- [ ] 错误信息不泄露敏感信息
- [ ] 安全头已设置
- [ ] 限流机制已实现
- [ ] 安全日志已记录

## 应急响应

### 安全事件响应流程

**1. 事件发现**:
- 监控系统告警
- 用户举报
- 安全扫描发现
- 日志分析发现

**2. 事件评估**:
- 确定事件类型和严重程度
- 评估影响范围
- 确定响应优先级

**3. 应急响应**:
- 立即阻止攻击
- 保护关键资产
- 收集证据
- 通知相关人员

**4. 恢复处理**:
- 修复安全漏洞
- 恢复正常服务
- 验证修复效果

**5. 事后分析**:
- 分析事件原因
- 总结经验教训
- 改进安全措施
- 更新应急预案

### 常见安全事件处理

**SQL注入攻击**:
```bash
# 1. 立即封禁攻击IP
redis-cli SET "blocked_ip:攻击IP" "sql_injection" EX 86400

# 2. 检查数据库日志
tail -f /var/log/mysql/mysql.log | grep "攻击IP"

# 3. 检查是否有数据泄露
SELECT * FROM audit_log WHERE ip = '攻击IP' AND action = 'SELECT';

# 4. 修复SQL注入漏洞
# 使用参数化查询替换字符串拼接
```

**XSS攻击**:
```bash
# 1. 清理恶意脚本
# 检查用户输入的内容，移除恶意脚本

# 2. 更新内容安全策略
# 加强CSP头配置

# 3. 通知受影响用户
# 发送安全提醒邮件
```

**数据泄露**:
```bash
# 1. 立即停止服务
sudo systemctl stop meituan-alliance

# 2. 隔离受影响系统
# 断开网络连接

# 3. 评估泄露范围
# 检查访问日志和数据库日志

# 4. 通知用户和监管部门
# 按照法律要求进行通知

# 5. 修复漏洞后恢复服务
```

## 安全培训

### 开发人员安全培训

**基础安全知识**:
- OWASP Top 10漏洞
- 安全编码规范
- 加密算法使用
- 认证授权机制

**实践培训**:
- 代码安全审查
- 渗透测试基础
- 安全工具使用
- 应急响应演练

### 安全意识培训

**定期培训内容**:
- 密码安全管理
- 钓鱼邮件识别
- 社会工程学防范
- 数据保护意识

## 总结

安全是一个持续的过程，需要在开发、部署、运维的各个环节都保持高度重视。通过实施本指南中的安全措施，可以有效保护美团联盟小程序的安全性：

1. **多层防护**: 网络、应用、数据、基础设施全方位防护
2. **纵深防御**: 多道安全防线，即使一道防线被突破也有其他保护
3. **持续监控**: 实时监控安全事件，快速响应威胁
4. **定期评估**: 定期进行安全评估和渗透测试
5. **安全文化**: 建立全员安全意识，将安全融入开发流程

安全工作永无止境，需要根据威胁环境的变化不断调整和完善安全策略。