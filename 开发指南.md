# 美团联盟小程序开发指南

## 快速开始

### 1. 环境准备

#### 必需软件
- **Python 3.9+**：[下载地址](https://www.python.org/downloads/)
- **微信开发者工具**：[下载地址](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- **Git**：版本控制工具
- **VS Code**：推荐的代码编辑器（可选）

#### Python环境配置
```bash
# 检查Python版本
python --version

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 升级pip
python -m pip install --upgrade pip
```

#### 账号准备
- **美团联盟账号**：[申请地址](https://union.meituan.com/)
- **微信公众平台账号**：用于小程序管理
- **微信小程序账号**：需要已认证的小程序账号

### 2. 后端项目搭建

#### 克隆项目
```bash
git clone <项目地址>
cd zhang_mt
```

#### 安装Python依赖
```bash
# 确保虚拟环境已激活
pip install -r requirements.txt
```

#### 创建requirements.txt文件
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
pydantic==2.5.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
httpx==0.25.2
python-dotenv==1.0.0
watchdog==3.0.0
```

#### 环境变量配置
创建`.env`文件：
```env
# 数据库配置
DATABASE_PATH=data/database.db

# JWT配置
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 美团联盟配置
MEITUAN_APP_KEY=your_meituan_app_key
MEITUAN_APP_SECRET=your_meituan_app_secret

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 应用配置
DEBUG=True
LOG_LEVEL=INFO
```

#### 初始化数据库
```bash
# 创建数据目录
mkdir -p data

# 运行数据库初始化脚本
python scripts/init_db.py
```

#### 启动后端服务
```bash
# 开发环境启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或使用Python直接运行
python -m uvicorn app.main:app --reload
```

### 3. 前端项目配置

#### 导入小程序项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择：`d:\~Web\zhang_mt`
4. AppID填写你的小程序AppID
5. 项目名称：美团联盟小程序

#### 修改app.json配置
```json
{
  "pages": [
    "pages/index/index",
    "pages/products/products",
    "pages/community/community",
    "pages/topic/topic",
    "pages/profile/profile"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "美团联盟",
    "navigationBarTextStyle": "black"
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "images/icon_home.png",
        "selectedIconPath": "images/icon_home_selected.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/products/products",
        "iconPath": "images/icon_products.png",
        "selectedIconPath": "images/icon_products_selected.png",
        "text": "商品"
      },
      {
        "pagePath": "pages/community/community",
        "iconPath": "images/icon_community.png",
        "selectedIconPath": "images/icon_community_selected.png",
        "text": "社区"
      },
      {
        "pagePath": "pages/profile/profile",
        "iconPath": "images/icon_profile.png",
        "selectedIconPath": "images/icon_profile_selected.png",
        "text": "我的"
      }
    ]
  },
  "navigateToMiniProgramAppIdList": [
    "wxde8ac0a21135c07d"
  ],
  "permission": {
    "scope.userLocation": {
      "desc": "你的位置信息将用于小程序位置接口的效果展示"
    }
  }
}
```

#### 配置API域名
在小程序后台配置服务器域名：
- request合法域名：`https://your-api-domain.com`
- uploadFile合法域名：`https://your-api-domain.com`
- downloadFile合法域名：`https://your-api-domain.com`

#### 修改API配置
在小程序中创建`utils/config.js`：
```javascript
// utils/config.js
const config = {
  // API基础地址
  apiBase: 'http://localhost:8000',  // 开发环境
  // apiBase: 'https://your-api-domain.com',  // 生产环境

  // 美团联盟配置
  meituan: {
    appKey: 'your_meituan_app_key',
    // 注意：不要在前端暴露app_secret
  },

  // 微信小程序配置
  wechat: {
    appId: 'your_wechat_app_id'
  }
};

module.exports = config;
```

## 功能测试指南

### 1. 首页功能测试

#### 测试项目
- [ ] 页面正常加载
- [ ] 地理位置获取（需真机测试）
- [ ] 搜索框交互
- [ ] 品牌按钮点击
- [ ] 轮播图滑动
- [ ] 分类切换
- [ ] 商品列表展示
- [ ] 商品点击跳转
- [ ] 下拉刷新
- [ ] 上拉加载更多

#### 测试步骤
1. 在开发者工具中打开首页
2. 检查页面布局是否正常
3. 点击各个交互元素
4. 测试滚动和刷新功能

### 2. 商品列表页测试

#### 测试项目
- [ ] 分类导航切换
- [ ] 筛选功能
- [ ] 排序功能
- [ ] 商品列表展示
- [ ] 商品详情跳转
- [ ] 分页加载

#### 测试步骤
1. 从首页点击分类进入商品列表
2. 测试顶部导航切换
3. 测试筛选和排序功能
4. 验证商品信息展示

### 3. 社区页面测试

#### 测试项目
- [ ] 内容列表展示
- [ ] 分类切换
- [ ] 文章详情查看
- [ ] 点赞功能
- [ ] 评论功能
- [ ] 分享功能
- [ ] 发布按钮

#### 测试步骤
1. 切换到社区tab
2. 浏览内容列表
3. 测试各种交互功能
4. 检查图片加载

### 4. 专题页面测试

#### 测试项目
- [ ] 文章列表展示
- [ ] 分类筛选
- [ ] 文章详情跳转
- [ ] 外链跳转（需配置）
- [ ] 分页加载

### 5. 个人中心测试

#### 测试项目
- [ ] 用户信息展示
- [ ] 登录功能
- [ ] 资产信息展示
- [ ] 订单管理
- [ ] 功能入口跳转
- [ ] 分享功能

## API集成指南

### 1. 美团联盟API集成

#### 获取API密钥
1. 登录美团联盟后台
2. 获取AppKey和AppSecret
3. 在后端服务中配置密钥

#### 主要API接口

**推广物料API**
```javascript
// 获取商品列表
wx.request({
  url: 'https://your-api.com/meituan/products',
  method: 'GET',
  data: {
    category: 'food',
    page: 1,
    limit: 20
  },
  success: (res) => {
    // 处理商品数据
  }
});
```

**转链API**
```javascript
// 生成推广链接
wx.request({
  url: 'https://your-api.com/meituan/generate-link',
  method: 'POST',
  data: {
    productId: '12345',
    userId: 'user123'
  },
  success: (res) => {
    // 获取推广链接，跳转美团小程序
    wx.navigateToMiniProgram({
      appId: 'wxde8ac0a21135c07d',
      path: res.data.path,
      extraData: res.data.extraData
    });
  }
});
```

### 2. 后端服务搭建

#### 技术栈推荐
- **Node.js + Express**
- **Python + Django/Flask**
- **Java + Spring Boot**
- **PHP + Laravel**

#### 核心功能模块
1. **用户系统**：处理微信登录，管理用户信息
2. **商品服务**：对接美团联盟API，提供商品数据
3. **内容管理**：社区内容的CRUD操作
4. **数据统计**：用户行为分析和推广效果统计

### 3. 数据库设计

#### 用户表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(100) UNIQUE NOT NULL,
  nickname VARCHAR(100),
  avatar_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 内容表 (posts)
```sql
CREATE TABLE posts (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  title VARCHAR(200) NOT NULL,
  content TEXT,
  images JSON,
  products JSON,
  category VARCHAR(50),
  like_count INT DEFAULT 0,
  comment_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 部署指南

### 1. 小程序发布

#### 发布流程
1. **代码上传**
   - 在微信开发者工具中点击"上传"
   - 填写版本号（如：1.0.0）
   - 填写项目备注

2. **提交审核**
   - 登录微信公众平台
   - 进入版本管理
   - 提交审核并填写审核信息

3. **发布上线**
   - 审核通过后点击发布
   - 设置发布时间（可立即发布）

#### 审核注意事项
- 确保所有功能正常运行
- 检查是否有违规内容
- 准备好小程序介绍和截图
- 确保服务器域名已配置

### 2. 后端部署

#### 服务器要求
- **操作系统**：Linux（推荐Ubuntu/CentOS）
- **内存**：至少2GB
- **存储**：至少20GB
- **带宽**：至少1Mbps

#### 部署步骤
1. **环境搭建**
   ```bash
   # 安装Node.js（以Node.js为例）
   curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # 安装PM2
   npm install -g pm2
   ```

2. **代码部署**
   ```bash
   # 克隆代码
   git clone your-backend-repo
   cd your-backend

   # 安装依赖
   npm install

   # 启动服务
   pm2 start app.js --name "meituan-api"
   ```

3. **配置HTTPS**
   ```bash
   # 安装Nginx
   sudo apt-get install nginx

   # 配置SSL证书（使用Let's Encrypt）
   sudo apt-get install certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

## 常见问题

### 1. 开发问题

**Q: 小程序无法跳转到美团小程序？**
A: 检查以下配置：
- app.json中是否配置了navigateToMiniProgramAppIdList
- 美团小程序AppID是否正确
- 跳转参数是否正确

**Q: 地理位置获取失败？**
A:
- 确保在app.json中配置了地理位置权限
- 在真机上测试（开发者工具模拟器可能不准确）
- 检查用户是否授权了地理位置

**Q: 图片加载失败？**
A:
- 检查图片URL是否正确
- 确保图片域名在小程序后台已配置
- 检查图片格式是否支持

### 2. API问题

**Q: 美团联盟API调用失败？**
A:
- 检查API密钥是否正确
- 确认账号状态是否正常
- 检查请求参数格式
- 查看API文档确认接口地址

**Q: 跨域问题？**
A:
- 小程序不存在跨域问题
- 如果是后端API，需要配置CORS
- 确保域名在小程序后台已配置

### 3. 部署问题

**Q: 服务器连接失败？**
A:
- 检查服务器是否正常运行
- 确认端口是否开放
- 检查防火墙设置
- 确认HTTPS配置是否正确

**Q: 小程序审核被拒？**
A:
- 检查是否有违规内容
- 确保所有功能正常运行
- 完善小程序信息和截图
- 根据审核意见进行修改

## 性能优化

### 1. 前端优化

- **图片优化**：使用webp格式，启用懒加载
- **代码分包**：按功能模块分包加载
- **缓存策略**：合理使用本地存储
- **请求优化**：减少不必要的API调用

### 2. 后端优化

- **数据库优化**：添加索引，优化查询
- **缓存机制**：使用Redis缓存热点数据
- **CDN加速**：静态资源使用CDN
- **负载均衡**：多服务器部署

## 监控和分析

### 1. 小程序数据分析

- 使用微信小程序后台的数据分析功能
- 关注用户访问、留存、转化等关键指标
- 定期分析用户行为数据

### 2. 业务数据监控

- 监控API调用成功率
- 跟踪推广订单转化率
- 分析用户使用习惯
- 监控服务器性能指标

## 联系支持

如在开发过程中遇到问题，可以通过以下方式获取帮助：

- **微信开发者社区**：https://developers.weixin.qq.com/community/
- **美团联盟帮助中心**：https://union.meituan.com/help
- **项目Issues**：在项目仓库提交Issue

---

祝开发顺利！🎉