# 美团联盟小程序故障排查指南

## 目录
- [概述](#概述)
- [前端问题排查](#前端问题排查)
- [后端API问题排查](#后端api问题排查)
- [数据库问题排查](#数据库问题排查)
- [网络问题排查](#网络问题排查)
- [性能问题排查](#性能问题排查)
- [安全问题排查](#安全问题排查)
- [部署问题排查](#部署问题排查)
- [监控和日志分析](#监控和日志分析)
- [常见问题FAQ](#常见问题faq)
- [应急处理流程](#应急处理流程)

## 概述

### 故障分类

本指南将故障按照影响范围和紧急程度分为以下几类：

- **P0 - 紧急故障**: 影响核心功能，需要立即处理
- **P1 - 高优先级**: 影响主要功能，需要在2小时内处理
- **P2 - 中优先级**: 影响部分功能，需要在24小时内处理
- **P3 - 低优先级**: 影响较小，可在下个版本修复

### 排查原则

1. **快速定位**: 优先检查最可能的原因
2. **分层排查**: 从前端到后端，从应用到基础设施
3. **日志优先**: 充分利用日志信息进行分析
4. **监控辅助**: 结合监控数据进行判断
5. **记录过程**: 详细记录排查过程和解决方案

## 前端问题排查

### 1. 小程序启动问题

#### 1.1 小程序无法启动

**症状**: 小程序打开后白屏或闪退

**排查步骤**:

```javascript
// 1. 检查 app.js 中的错误
App({
  onLaunch: function() {
    try {
      console.log('小程序启动');
      // 检查全局数据初始化
      this.globalData = {
        userInfo: null,
        token: wx.getStorageSync('token') || ''
      };
    } catch (error) {
      console.error('启动错误:', error);
      // 记录错误到日志服务
      this.reportError('app_launch_error', error);
    }
  },
  
  /**
   * 错误上报
   */
  reportError: function(type, error) {
    wx.request({
      url: 'https://your-api.com/api/log/error',
      method: 'POST',
      data: {
        type: type,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        version: this.globalData.version
      }
    });
  }
});
```

**常见原因及解决方案**:

| 原因 | 解决方案 |
|------|----------|
| app.json 配置错误 | 检查页面路径、tabBar配置 |
| 依赖的插件未正确引入 | 检查 app.json 中的 plugins 配置 |
| 全局变量初始化失败 | 添加 try-catch 保护 |
| 网络权限问题 | 检查域名白名单配置 |

#### 1.2 页面跳转失败

**症状**: 点击按钮或链接无法跳转到目标页面

**排查代码**:

```javascript
/**
 * 页面跳转封装函数
 */
function navigateTo(url, params = {}) {
  try {
    // 构建完整URL
    let fullUrl = url;
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');
      fullUrl += `?${queryString}`;
    }
    
    console.log('跳转到:', fullUrl);
    
    wx.navigateTo({
      url: fullUrl,
      success: function(res) {
        console.log('跳转成功:', res);
      },
      fail: function(err) {
        console.error('跳转失败:', err);
        
        // 尝试使用 redirectTo
        if (err.errMsg.includes('limit exceed')) {
          wx.redirectTo({
            url: fullUrl,
            fail: function(redirectErr) {
              console.error('重定向也失败:', redirectErr);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  } catch (error) {
    console.error('跳转异常:', error);
    wx.showToast({
      title: '页面跳转异常',
      icon: 'none'
    });
  }
}
```

**常见问题**:

- **页面栈溢出**: 使用 `wx.redirectTo` 或 `wx.reLaunch`
- **页面路径错误**: 检查页面是否在 app.json 中注册
- **参数过长**: URL参数总长度不能超过限制

### 2. 数据加载问题

#### 2.1 API请求失败

**排查工具函数**:

```javascript
/**
 * API请求封装
 */
function apiRequest(options) {
  const {
    url,
    method = 'GET',
    data = {},
    header = {},
    showLoading = true,
    loadingText = '加载中...'
  } = options;
  
  return new Promise((resolve, reject) => {
    // 显示加载提示
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      });
    }
    
    // 添加认证头
    const token = wx.getStorageSync('token');
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    // 记录请求开始时间
    const startTime = Date.now();
    
    wx.request({
      url: `${getApp().globalData.apiBaseUrl}${url}`,
      method: method,
      data: data,
      header: {
        'Content-Type': 'application/json',
        ...header
      },
      success: function(res) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`API请求成功 [${method} ${url}] 耗时: ${duration}ms`, res.data);
        
        if (showLoading) {
          wx.hideLoading();
        }
        
        // 统一处理响应
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data);
          } else {
            // 业务错误
            const error = new Error(res.data.message || '请求失败');
            error.code = res.data.code;
            reject(error);
          }
        } else {
          // HTTP错误
          const error = new Error(`HTTP ${res.statusCode}`);
          error.statusCode = res.statusCode;
          reject(error);
        }
      },
      fail: function(err) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.error(`API请求失败 [${method} ${url}] 耗时: ${duration}ms`, err);
        
        if (showLoading) {
          wx.hideLoading();
        }
        
        // 网络错误处理
        let errorMessage = '网络请求失败';
        if (err.errMsg.includes('timeout')) {
          errorMessage = '请求超时，请检查网络连接';
        } else if (err.errMsg.includes('fail')) {
          errorMessage = '网络连接失败，请检查网络设置';
        }
        
        const error = new Error(errorMessage);
        error.originalError = err;
        reject(error);
      },
      complete: function() {
        // 记录请求完成
        console.log(`API请求完成 [${method} ${url}]`);
      }
    });
  });
}

/**
 * 网络状态检查
 */
function checkNetworkStatus() {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: function(res) {
        console.log('网络类型:', res.networkType);
        resolve({
          isConnected: res.networkType !== 'none',
          networkType: res.networkType
        });
      },
      fail: function() {
        resolve({
          isConnected: false,
          networkType: 'unknown'
        });
      }
    });
  });
}
```

#### 2.2 数据渲染问题

**排查步骤**:

```javascript
/**
 * 数据更新调试工具
 */
function debugSetData(page, data, callback) {
  console.log('setData 前的数据:', page.data);
  console.log('要更新的数据:', data);
  
  const startTime = Date.now();
  
  page.setData(data, function() {
    const endTime = Date.now();
    console.log(`setData 完成，耗时: ${endTime - startTime}ms`);
    console.log('setData 后的数据:', page.data);
    
    if (callback) {
      callback();
    }
  });
}

/**
 * 页面数据验证
 */
function validatePageData(page, requiredFields) {
  const missingFields = [];
  
  requiredFields.forEach(field => {
    if (page.data[field] === undefined || page.data[field] === null) {
      missingFields.push(field);
    }
  });
  
  if (missingFields.length > 0) {
    console.warn('页面缺少必要数据:', missingFields);
    return false;
  }
  
  return true;
}
```

### 3. 用户交互问题

#### 3.1 按钮点击无响应

**排查代码**:

```javascript
/**
 * 防抖点击处理
 */
function debounceClick(fn, delay = 300) {
  let timer = null;
  
  return function(...args) {
    if (timer) {
      console.log('点击过于频繁，已忽略');
      return;
    }
    
    timer = setTimeout(() => {
      timer = null;
    }, delay);
    
    try {
      return fn.apply(this, args);
    } catch (error) {
      console.error('点击处理函数执行错误:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  };
}

// 使用示例
Page({
  data: {
    loading: false
  },
  
  onLoad: function() {
    // 绑定防抖点击
    this.handleSubmit = debounceClick(this.handleSubmit.bind(this), 500);
  },
  
  /**
   * 提交处理
   */
  handleSubmit: function(e) {
    console.log('提交按钮点击', e);
    
    if (this.data.loading) {
      console.log('正在处理中，忽略点击');
      return;
    }
    
    this.setData({ loading: true });
    
    // 模拟提交逻辑
    setTimeout(() => {
      this.setData({ loading: false });
    }, 2000);
  }
});
```

## 后端API问题排查

### 1. 服务启动问题

#### 1.1 Node.js 服务无法启动

**排查脚本**:

```javascript
/**
 * 服务启动检查脚本
 */
const fs = require('fs');
const path = require('path');
const http = require('http');

/**
 * 检查环境变量
 */
function checkEnvironment() {
  console.log('=== 环境变量检查 ===');
  
  const requiredEnvs = [
    'NODE_ENV',
    'PORT',
    'DB_HOST',
    'DB_PORT',
    'DB_USER',
    'DB_PASSWORD',
    'DB_NAME',
    'JWT_SECRET'
  ];
  
  const missingEnvs = [];
  
  requiredEnvs.forEach(env => {
    if (!process.env[env]) {
      missingEnvs.push(env);
    } else {
      console.log(`✓ ${env}: ${env.includes('PASSWORD') || env.includes('SECRET') ? '***' : process.env[env]}`);
    }
  });
  
  if (missingEnvs.length > 0) {
    console.error('✗ 缺少环境变量:', missingEnvs);
    return false;
  }
  
  return true;
}

/**
 * 检查端口占用
 */
function checkPort(port) {
  return new Promise((resolve) => {
    const server = http.createServer();
    
    server.listen(port, () => {
      server.close(() => {
        console.log(`✓ 端口 ${port} 可用`);
        resolve(true);
      });
    });
    
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.error(`✗ 端口 ${port} 已被占用`);
        resolve(false);
      }
    });
  });
}

/**
 * 检查文件权限
 */
function checkFilePermissions() {
  console.log('=== 文件权限检查 ===');
  
  const criticalFiles = [
    './app.js',
    './package.json',
    './config/database.js'
  ];
  
  criticalFiles.forEach(file => {
    try {
      fs.accessSync(file, fs.constants.R_OK);
      console.log(`✓ ${file} 可读`);
    } catch (err) {
      console.error(`✗ ${file} 无法读取:`, err.message);
    }
  });
}

/**
 * 检查依赖模块
 */
function checkDependencies() {
  console.log('=== 依赖模块检查 ===');
  
  const packageJson = require('./package.json');
  const dependencies = Object.keys(packageJson.dependencies || {});
  
  dependencies.forEach(dep => {
    try {
      require(dep);
      console.log(`✓ ${dep} 已安装`);
    } catch (err) {
      console.error(`✗ ${dep} 未安装或有问题:`, err.message);
    }
  });
}

/**
 * 主检查函数
 */
async function runStartupCheck() {
  console.log('开始服务启动检查...');
  
  // 检查环境变量
  if (!checkEnvironment()) {
    process.exit(1);
  }
  
  // 检查端口
  const port = process.env.PORT || 3000;
  if (!(await checkPort(port))) {
    process.exit(1);
  }
  
  // 检查文件权限
  checkFilePermissions();
  
  // 检查依赖
  checkDependencies();
  
  console.log('✓ 启动检查完成');
}

if (require.main === module) {
  runStartupCheck();
}

module.exports = { runStartupCheck };
```

#### 1.2 数据库连接问题

**数据库连接诊断**:

```javascript
/**
 * 数据库连接诊断工具
 */
const mysql = require('mysql2/promise');

/**
 * 数据库连接测试
 */
async function testDatabaseConnection() {
  console.log('=== 数据库连接测试 ===');
  
  const config = {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    connectTimeout: 10000,
    acquireTimeout: 10000
  };
  
  console.log('连接配置:', {
    ...config,
    password: '***'
  });
  
  try {
    // 测试基本连接
    console.log('1. 测试基本连接...');
    const connection = await mysql.createConnection(config);
    console.log('✓ 数据库连接成功');
    
    // 测试查询
    console.log('2. 测试查询...');
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✓ 查询测试成功:', rows);
    
    // 测试表是否存在
    console.log('3. 检查表结构...');
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
      ORDER BY TABLE_NAME
    `, [config.database]);
    
    console.log('✓ 数据库表:', tables.map(t => t.TABLE_NAME));
    
    await connection.end();
    return true;
    
  } catch (error) {
    console.error('✗ 数据库连接失败:', error.message);
    
    // 详细错误分析
    if (error.code === 'ECONNREFUSED') {
      console.error('  原因: 数据库服务未启动或端口不正确');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('  原因: 用户名或密码错误');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('  原因: 数据库不存在');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('  原因: 连接超时，检查网络或防火墙');
    }
    
    return false;
  }
}

/**
 * 数据库性能测试
 */
async function testDatabasePerformance() {
  console.log('=== 数据库性能测试 ===');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
    
    // 测试查询性能
    const startTime = Date.now();
    await connection.execute('SELECT COUNT(*) as count FROM users');
    const endTime = Date.now();
    
    const queryTime = endTime - startTime;
    console.log(`查询耗时: ${queryTime}ms`);
    
    if (queryTime > 1000) {
      console.warn('⚠ 查询响应较慢，可能需要优化');
    } else {
      console.log('✓ 查询性能正常');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('✗ 性能测试失败:', error.message);
  }
}
```

### 2. API响应问题

#### 2.1 响应超时

**超时监控中间件**:

```javascript
/**
 * API超时监控中间件
 */
function timeoutMonitor(timeoutMs = 30000) {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // 设置超时定时器
    const timeoutId = setTimeout(() => {
      if (!res.headersSent) {
        console.error(`API超时 [${req.method} ${req.path}] 超过 ${timeoutMs}ms`);
        res.status(408).json({
          code: 408,
          message: '请求超时',
          path: req.path,
          method: req.method,
          timeout: timeoutMs
        });
      }
    }, timeoutMs);
    
    // 监听响应完成
    res.on('finish', () => {
      clearTimeout(timeoutId);
      const duration = Date.now() - startTime;
      
      // 记录慢查询
      if (duration > 5000) {
        console.warn(`慢查询警告 [${req.method} ${req.path}] 耗时: ${duration}ms`);
      }
      
      console.log(`API完成 [${req.method} ${req.path}] 耗时: ${duration}ms 状态: ${res.statusCode}`);
    });
    
    next();
  };
}

/**
 * 性能分析中间件
 */
function performanceAnalyzer() {
  return (req, res, next) => {
    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();
    
    res.on('finish', () => {
      const endTime = process.hrtime.bigint();
      const endMemory = process.memoryUsage();
      
      const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒
      const memoryDiff = endMemory.heapUsed - startMemory.heapUsed;
      
      console.log(`性能分析 [${req.method} ${req.path}]:`, {
        duration: `${duration.toFixed(2)}ms`,
        memoryDiff: `${(memoryDiff / 1024 / 1024).toFixed(2)}MB`,
        statusCode: res.statusCode
      });
      
      // 内存泄漏警告
      if (memoryDiff > 10 * 1024 * 1024) { // 10MB
        console.warn(`内存使用异常 [${req.method} ${req.path}] 增加: ${(memoryDiff / 1024 / 1024).toFixed(2)}MB`);
      }
    });
    
    next();
  };
}
```

#### 2.2 错误响应分析

**错误处理中间件**:

```javascript
/**
 * 全局错误处理中间件
 */
function errorHandler() {
  return (err, req, res, next) => {
    console.error('API错误:', {
      message: err.message,
      stack: err.stack,
      path: req.path,
      method: req.method,
      body: req.body,
      query: req.query,
      headers: req.headers,
      timestamp: new Date().toISOString()
    });
    
    // 错误分类处理
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    
    if (err.name === 'ValidationError') {
      statusCode = 400;
      errorCode = 'VALIDATION_ERROR';
      message = '参数验证失败';
    } else if (err.name === 'UnauthorizedError') {
      statusCode = 401;
      errorCode = 'UNAUTHORIZED';
      message = '未授权访问';
    } else if (err.name === 'ForbiddenError') {
      statusCode = 403;
      errorCode = 'FORBIDDEN';
      message = '禁止访问';
    } else if (err.name === 'NotFoundError') {
      statusCode = 404;
      errorCode = 'NOT_FOUND';
      message = '资源不存在';
    } else if (err.code === 'ECONNREFUSED') {
      statusCode = 503;
      errorCode = 'SERVICE_UNAVAILABLE';
      message = '服务暂时不可用';
    }
    
    // 发送错误响应
    res.status(statusCode).json({
      code: statusCode,
      error: errorCode,
      message: message,
      path: req.path,
      timestamp: new Date().toISOString(),
      ...(process.env.NODE_ENV === 'development' && {
        stack: err.stack,
        details: err
      })
    });
    
    // 错误上报
    reportError(err, req);
  };
}

/**
 * 错误上报函数
 */
function reportError(error, req) {
  // 这里可以集成错误监控服务，如 Sentry
  const errorData = {
    message: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    timestamp: new Date().toISOString()
  };
  
  // 发送到错误监控服务
  // Sentry.captureException(error, { extra: errorData });
  
  console.log('错误已上报:', errorData);
}
```

## 数据库问题排查

### 1. 连接池问题

**连接池监控**:

```javascript
/**
 * 数据库连接池监控
 */
const mysql = require('mysql2/promise');

class DatabaseMonitor {
  constructor(pool) {
    this.pool = pool;
    this.startMonitoring();
  }
  
  /**
   * 开始监控
   */
  startMonitoring() {
    // 每30秒检查一次连接池状态
    setInterval(() => {
      this.checkPoolStatus();
    }, 30000);
    
    // 监听连接事件
    this.pool.on('connection', (connection) => {
      console.log('新连接建立:', connection.threadId);
    });
    
    this.pool.on('error', (err) => {
      console.error('连接池错误:', err);
    });
  }
  
  /**
   * 检查连接池状态
   */
  checkPoolStatus() {
    const status = {
      totalConnections: this.pool._allConnections.length,
      freeConnections: this.pool._freeConnections.length,
      usedConnections: this.pool._allConnections.length - this.pool._freeConnections.length,
      queuedRequests: this.pool._connectionQueue.length
    };
    
    console.log('连接池状态:', status);
    
    // 连接池告警
    if (status.usedConnections / status.totalConnections > 0.8) {
      console.warn('⚠ 连接池使用率过高:', (status.usedConnections / status.totalConnections * 100).toFixed(2) + '%');
    }
    
    if (status.queuedRequests > 10) {
      console.warn('⚠ 连接请求队列过长:', status.queuedRequests);
    }
  }
  
  /**
   * 测试连接池性能
   */
  async testPoolPerformance() {
    console.log('=== 连接池性能测试 ===');
    
    const testQueries = [];
    const startTime = Date.now();
    
    // 并发执行多个查询
    for (let i = 0; i < 20; i++) {
      testQueries.push(
        this.pool.execute('SELECT ? as test_id, NOW() as test_time', [i])
      );
    }
    
    try {
      const results = await Promise.all(testQueries);
      const endTime = Date.now();
      
      console.log(`✓ 并发查询测试完成，耗时: ${endTime - startTime}ms`);
      console.log(`✓ 成功执行 ${results.length} 个查询`);
      
    } catch (error) {
      console.error('✗ 连接池性能测试失败:', error.message);
    }
  }
}
```

### 2. 慢查询分析

**慢查询监控**:

```javascript
/**
 * 慢查询监控装饰器
 */
function slowQueryMonitor(threshold = 1000) {
  return function(target, propertyName, descriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function(...args) {
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;
        
        if (duration > threshold) {
          console.warn(`慢查询检测 [${propertyName}]:`, {
            duration: `${duration}ms`,
            threshold: `${threshold}ms`,
            args: args,
            timestamp: new Date().toISOString()
          });
          
          // 记录慢查询日志
          logSlowQuery(propertyName, duration, args);
        }
        
        return result;
        
      } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`查询错误 [${propertyName}] 耗时: ${duration}ms`, error);
        throw error;
      }
    };
    
    return descriptor;
  };
}

/**
 * 记录慢查询
 */
function logSlowQuery(queryName, duration, args) {
  const logEntry = {
    queryName,
    duration,
    args,
    timestamp: new Date().toISOString(),
    stackTrace: new Error().stack
  };
  
  // 写入慢查询日志文件
  const fs = require('fs');
  const path = require('path');
  
  const logFile = path.join(__dirname, '../logs/slow-queries.log');
  fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');
}

// 使用示例
class UserService {
  @slowQueryMonitor(500) // 超过500ms的查询会被记录
  async getUserById(id) {
    const [rows] = await db.execute(
      'SELECT * FROM users WHERE id = ?',
      [id]
    );
    return rows[0];
  }
  
  @slowQueryMonitor(1000)
  async getUsersWithPosts() {
    const [rows] = await db.execute(`
      SELECT u.*, COUNT(p.id) as post_count
      FROM users u
      LEFT JOIN posts p ON u.id = p.user_id
      GROUP BY u.id
    `);
    return rows;
  }
}
```

## 网络问题排查

### 1. 网络连通性检查

**网络诊断工具**:

```javascript
/**
 * 网络连通性检查工具
 */
const http = require('http');
const https = require('https');
const dns = require('dns');
const { promisify } = require('util');

class NetworkDiagnostic {
  /**
   * DNS解析测试
   */
  async testDNSResolution(hostname) {
    console.log(`=== DNS解析测试: ${hostname} ===`);
    
    try {
      const lookup = promisify(dns.lookup);
      const result = await lookup(hostname);
      
      console.log(`✓ DNS解析成功: ${hostname} -> ${result.address}`);
      return true;
      
    } catch (error) {
      console.error(`✗ DNS解析失败: ${hostname}`, error.message);
      return false;
    }
  }
  
  /**
   * HTTP连接测试
   */
  async testHTTPConnection(url, timeout = 10000) {
    console.log(`=== HTTP连接测试: ${url} ===`);
    
    return new Promise((resolve) => {
      const startTime = Date.now();
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const req = client.request({
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname,
        method: 'GET',
        timeout: timeout
      }, (res) => {
        const duration = Date.now() - startTime;
        console.log(`✓ HTTP连接成功: ${res.statusCode} 耗时: ${duration}ms`);
        resolve(true);
      });
      
      req.on('error', (error) => {
        const duration = Date.now() - startTime;
        console.error(`✗ HTTP连接失败 耗时: ${duration}ms`, error.message);
        resolve(false);
      });
      
      req.on('timeout', () => {
        console.error(`✗ HTTP连接超时: ${timeout}ms`);
        req.destroy();
        resolve(false);
      });
      
      req.end();
    });
  }
  
  /**
   * 网络延迟测试
   */
  async testNetworkLatency(hostname, count = 5) {
    console.log(`=== 网络延迟测试: ${hostname} ===`);
    
    const latencies = [];
    
    for (let i = 0; i < count; i++) {
      const startTime = Date.now();
      
      try {
        await this.testHTTPConnection(`http://${hostname}`);
        const latency = Date.now() - startTime;
        latencies.push(latency);
        
      } catch (error) {
        console.error(`第${i + 1}次测试失败:`, error.message);
      }
    }
    
    if (latencies.length > 0) {
      const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
      const minLatency = Math.min(...latencies);
      const maxLatency = Math.max(...latencies);
      
      console.log(`延迟统计: 平均 ${avgLatency.toFixed(2)}ms, 最小 ${minLatency}ms, 最大 ${maxLatency}ms`);
      
      return {
        average: avgLatency,
        min: minLatency,
        max: maxLatency,
        samples: latencies
      };
    }
    
    return null;
  }
  
  /**
   * 综合网络诊断
   */
  async runFullDiagnostic(targets) {
    console.log('开始综合网络诊断...');
    
    const results = {};
    
    for (const target of targets) {
      console.log(`\n诊断目标: ${target}`);
      
      results[target] = {
        dns: await this.testDNSResolution(target),
        http: await this.testHTTPConnection(`http://${target}`),
        https: await this.testHTTPConnection(`https://${target}`),
        latency: await this.testNetworkLatency(target)
      };
    }
    
    console.log('\n=== 诊断结果汇总 ===');
    console.table(results);
    
    return results;
  }
}

// 使用示例
const diagnostic = new NetworkDiagnostic();

// 诊断关键服务
diagnostic.runFullDiagnostic([
  'api.meituan.com',
  'your-backend-api.com',
  'your-database-host.com'
]);
```

### 2. API网关问题

**API网关监控**:

```javascript
/**
 * API网关健康检查
 */
class APIGatewayMonitor {
  constructor(gatewayUrl) {
    this.gatewayUrl = gatewayUrl;
    this.healthCheckInterval = null;
  }
  
  /**
   * 开始健康检查
   */
  startHealthCheck(interval = 60000) {
    console.log('开始API网关健康检查...');
    
    this.healthCheckInterval = setInterval(() => {
      this.checkGatewayHealth();
    }, interval);
    
    // 立即执行一次
    this.checkGatewayHealth();
  }
  
  /**
   * 停止健康检查
   */
  stopHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('API网关健康检查已停止');
    }
  }
  
  /**
   * 检查网关健康状态
   */
  async checkGatewayHealth() {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${this.gatewayUrl}/health`, {
        method: 'GET',
        timeout: 10000
      });
      
      const duration = Date.now() - startTime;
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✓ API网关健康 [${response.status}] 耗时: ${duration}ms`, data);
        
        // 检查具体服务状态
        if (data.services) {
          this.checkServicesHealth(data.services);
        }
        
      } else {
        console.error(`✗ API网关异常 [${response.status}] 耗时: ${duration}ms`);
      }
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`✗ API网关连接失败 耗时: ${duration}ms`, error.message);
      
      // 触发告警
      this.triggerAlert('gateway_connection_failed', error);
    }
  }
  
  /**
   * 检查后端服务健康状态
   */
  checkServicesHealth(services) {
    Object.entries(services).forEach(([serviceName, status]) => {
      if (status.healthy) {
        console.log(`  ✓ ${serviceName}: 健康 (${status.responseTime}ms)`);
      } else {
        console.error(`  ✗ ${serviceName}: 异常`, status.error);
        this.triggerAlert('service_unhealthy', { serviceName, status });
      }
    });
  }
  
  /**
   * 触发告警
   */
  triggerAlert(type, data) {
    const alert = {
      type,
      data,
      timestamp: new Date().toISOString(),
      severity: type.includes('failed') ? 'critical' : 'warning'
    };
    
    console.error('🚨 告警触发:', alert);
    
    // 这里可以集成告警系统，如发送邮件、短信等
    // alertService.send(alert);
  }
}
```

## 性能问题排查

### 1. 内存泄漏检测

**内存监控工具**:

```javascript
/**
 * 内存泄漏检测工具
 */
class MemoryLeakDetector {
  constructor() {
    this.memorySnapshots = [];
    this.monitoringInterval = null;
    this.alertThreshold = 100 * 1024 * 1024; // 100MB
  }
  
  /**
   * 开始内存监控
   */
  startMonitoring(interval = 30000) {
    console.log('开始内存泄漏监控...');
    
    this.monitoringInterval = setInterval(() => {
      this.takeMemorySnapshot();
      this.analyzeMemoryTrend();
    }, interval);
    
    // 立即拍摄一次快照
    this.takeMemorySnapshot();
  }
  
  /**
   * 停止内存监控
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('内存监控已停止');
    }
  }
  
  /**
   * 拍摄内存快照
   */
  takeMemorySnapshot() {
    const memoryUsage = process.memoryUsage();
    const timestamp = Date.now();
    
    const snapshot = {
      timestamp,
      rss: memoryUsage.rss,
      heapTotal: memoryUsage.heapTotal,
      heapUsed: memoryUsage.heapUsed,
      external: memoryUsage.external,
      arrayBuffers: memoryUsage.arrayBuffers
    };
    
    this.memorySnapshots.push(snapshot);
    
    // 只保留最近100个快照
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots.shift();
    }
    
    console.log('内存快照:', {
      heapUsed: `${(snapshot.heapUsed / 1024 / 1024).toFixed(2)}MB`,
      heapTotal: `${(snapshot.heapTotal / 1024 / 1024).toFixed(2)}MB`,
      rss: `${(snapshot.rss / 1024 / 1024).toFixed(2)}MB`
    });
  }
  
  /**
   * 分析内存趋势
   */
  analyzeMemoryTrend() {
    if (this.memorySnapshots.length < 5) {
      return; // 需要至少5个快照才能分析趋势
    }
    
    const recent = this.memorySnapshots.slice(-5);
    const oldest = recent[0];
    const newest = recent[recent.length - 1];
    
    const heapGrowth = newest.heapUsed - oldest.heapUsed;
    const timeSpan = newest.timestamp - oldest.timestamp;
    const growthRate = heapGrowth / timeSpan * 1000; // 每秒增长字节数
    
    console.log(`内存增长率: ${(growthRate / 1024).toFixed(2)} KB/s`);
    
    // 检测内存泄漏
    if (heapGrowth > this.alertThreshold) {
      console.warn(`⚠ 检测到可能的内存泄漏: ${(heapGrowth / 1024 / 1024).toFixed(2)}MB`);
      this.generateMemoryReport();
    }
    
    // 检测内存使用过高
    if (newest.heapUsed > 500 * 1024 * 1024) { // 500MB
      console.warn(`⚠ 内存使用过高: ${(newest.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    }
  }
  
  /**
   * 生成内存报告
   */
  generateMemoryReport() {
    const report = {
      timestamp: new Date().toISOString(),
      snapshots: this.memorySnapshots,
      analysis: this.analyzeMemoryPattern(),
      recommendations: this.getMemoryOptimizationTips()
    };
    
    // 保存报告到文件
    const fs = require('fs');
    const path = require('path');
    
    const reportFile = path.join(__dirname, '../logs/memory-report.json');
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    console.log(`内存报告已生成: ${reportFile}`);
  }
  
  /**
   * 分析内存模式
   */
  analyzeMemoryPattern() {
    if (this.memorySnapshots.length < 10) {
      return { pattern: 'insufficient_data' };
    }
    
    const values = this.memorySnapshots.map(s => s.heapUsed);
    const trend = this.calculateTrend(values);
    
    if (trend > 1000000) { // 1MB/snapshot
      return { pattern: 'memory_leak', severity: 'high' };
    } else if (trend > 100000) { // 100KB/snapshot
      return { pattern: 'memory_leak', severity: 'medium' };
    } else if (trend < -100000) {
      return { pattern: 'memory_cleanup', severity: 'good' };
    } else {
      return { pattern: 'stable', severity: 'normal' };
    }
  }
  
  /**
   * 计算趋势
   */
  calculateTrend(values) {
    const n = values.length;
    const sumX = n * (n - 1) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
    const sumXX = n * (n - 1) * (2 * n - 1) / 6;
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }
  
  /**
   * 获取内存优化建议
   */
  getMemoryOptimizationTips() {
    return [
      '检查是否有未清理的定时器',
      '确保事件监听器被正确移除',
      '检查闭包是否持有不必要的引用',
      '考虑使用对象池来减少GC压力',
      '检查是否有循环引用',
      '使用 WeakMap 和 WeakSet 来避免内存泄漏'
    ];
  }
}

// 使用示例
const memoryDetector = new MemoryLeakDetector();
memoryDetector.startMonitoring(30000); // 每30秒检查一次

// 在应用关闭时停止监控
process.on('SIGINT', () => {
  memoryDetector.stopMonitoring();
  process.exit(0);
});
```

### 2. CPU使用率监控

**CPU性能监控**:

```javascript
/**
 * CPU性能监控工具
 */
const os = require('os');

class CPUMonitor {
  constructor() {
    this.cpuSamples = [];
    this.monitoringInterval = null;
  }
  
  /**
   * 开始CPU监控
   */
  startMonitoring(interval = 5000) {
    console.log('开始CPU性能监控...');
    
    this.monitoringInterval = setInterval(() => {
      this.sampleCPUUsage();
    }, interval);
    
    // 立即采样一次
    this.sampleCPUUsage();
  }
  
  /**
   * 停止CPU监控
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('CPU监控已停止');
    }
  }
  
  /**
   * 采样CPU使用率
   */
  async sampleCPUUsage() {
    const startTime = process.hrtime.bigint();
    const startUsage = process.cpuUsage();
    
    // 等待100ms后再次采样
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const endTime = process.hrtime.bigint();
    const endUsage = process.cpuUsage(startUsage);
    
    // 计算CPU使用率
    const elapsedTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
    const cpuPercent = (endUsage.user + endUsage.system) / 1000 / elapsedTime * 100;
    
    const sample = {
      timestamp: Date.now(),
      cpuPercent: cpuPercent,
      userTime: endUsage.user,
      systemTime: endUsage.system,
      loadAverage: os.loadavg(),
      freeMemory: os.freemem(),
      totalMemory: os.totalmem()
    };
    
    this.cpuSamples.push(sample);
    
    // 只保留最近50个样本
    if (this.cpuSamples.length > 50) {
      this.cpuSamples.shift();
    }
    
    console.log(`CPU使用率: ${cpuPercent.toFixed(2)}% 负载: [${sample.loadAverage.map(l => l.toFixed(2)).join(', ')}]`);
    
    // 检查CPU使用率过高
    if (cpuPercent > 80) {
      console.warn(`⚠ CPU使用率过高: ${cpuPercent.toFixed(2)}%`);
      this.analyzeCPUUsage();
    }
  }
  
  /**
   * 分析CPU使用情况
   */
  analyzeCPUUsage() {
    if (this.cpuSamples.length < 5) {
      return;
    }
    
    const recent = this.cpuSamples.slice(-5);
    const avgCPU = recent.reduce((sum, sample) => sum + sample.cpuPercent, 0) / recent.length;
    
    console.log(`最近5次平均CPU使用率: ${avgCPU.toFixed(2)}%`);
    
    if (avgCPU > 70) {
      console.warn('CPU持续高负载，建议检查:');
      console.warn('1. 是否有死循环或计算密集型操作');
      console.warn('2. 数据库查询是否优化');
      console.warn('3. 是否有内存泄漏导致频繁GC');
      console.warn('4. 考虑增加服务器资源或优化算法');
      
      // 生成CPU分析报告
      this.generateCPUReport();
    }
  }
  
  /**
   * 生成CPU分析报告
   */
  generateCPUReport() {
    const report = {
      timestamp: new Date().toISOString(),
      samples: this.cpuSamples,
      analysis: {
        averageCPU: this.cpuSamples.reduce((sum, s) => sum + s.cpuPercent, 0) / this.cpuSamples.length,
        maxCPU: Math.max(...this.cpuSamples.map(s => s.cpuPercent)),
        minCPU: Math.min(...this.cpuSamples.map(s => s.cpuPercent))
      },
      systemInfo: {
        platform: os.platform(),
        arch: os.arch(),
        cpus: os.cpus().length,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem()
      }
    };
    
    // 保存报告
    const fs = require('fs');
    const path = require('path');
    
    const reportFile = path.join(__dirname, '../logs/cpu-report.json');
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    console.log(`CPU分析报告已生成: ${reportFile}`);
  }
}

// 使用示例
const cpuMonitor = new CPUMonitor();
cpuMonitor.startMonitoring(5000); // 每5秒检查一次
```

## 安全问题排查

### 1. 认证授权问题

**JWT Token验证**:

```javascript
/**
 * JWT Token诊断工具
 */
const jwt = require('jsonwebtoken');

class JWTDiagnostic {
  constructor(secret) {
    this.secret = secret;
  }
  
  /**
   * 验证Token
   */
  verifyToken(token) {
    console.log('=== JWT Token诊断 ===');
    
    if (!token) {
      console.error('✗ Token为空');
      return { valid: false, error: 'Token为空' };
    }
    
    // 检查Token格式
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('✗ Token格式错误，应该包含3个部分');
      return { valid: false, error: 'Token格式错误' };
    }
    
    try {
      // 解码Header
      const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
      console.log('Token Header:', header);
      
      // 解码Payload（不验证签名）
      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
      console.log('Token Payload:', payload);
      
      // 检查过期时间
      if (payload.exp) {
        const expirationDate = new Date(payload.exp * 1000);
        const now = new Date();
        
        console.log(`Token过期时间: ${expirationDate.toISOString()}`);
        console.log(`当前时间: ${now.toISOString()}`);
        
        if (now > expirationDate) {
          console.error('✗ Token已过期');
          return { valid: false, error: 'Token已过期', payload };
        } else {
          console.log('✓ Token未过期');
        }
      }
      
      // 验证签名
      const decoded = jwt.verify(token, this.secret);
      console.log('✓ Token签名验证成功');
      
      return { valid: true, payload: decoded };
      
    } catch (error) {
      console.error('✗ Token验证失败:', error.message);
      
      if (error.name === 'TokenExpiredError') {
        return { valid: false, error: 'Token已过期', expired: true };
      } else if (error.name === 'JsonWebTokenError') {
        return { valid: false, error: 'Token签名无效' };
      } else {
        return { valid: false, error: error.message };
      }
    }
  }
  
  /**
   * 生成测试Token
   */
  generateTestToken(payload, expiresIn = '1h') {
    try {
      const token = jwt.sign(payload, this.secret, { expiresIn });
      console.log('✓ 测试Token生成成功');
      return token;
    } catch (error) {
      console.error('✗ 测试Token生成失败:', error.message);
      return null;
    }
  }
  
  /**
   * Token性能测试
   */
  performanceTest(iterations = 1000) {
    console.log(`=== JWT性能测试 (${iterations}次) ===`);
    
    const testPayload = { userId: 123, role: 'user' };
    
    // 生成性能测试
    const generateStart = Date.now();
    const tokens = [];
    
    for (let i = 0; i < iterations; i++) {
      tokens.push(jwt.sign(testPayload, this.secret));
    }
    
    const generateTime = Date.now() - generateStart;
    console.log(`Token生成: ${generateTime}ms (${(generateTime/iterations).toFixed(2)}ms/token)`);
    
    // 验证性能测试
    const verifyStart = Date.now();
    let validCount = 0;
    
    for (const token of tokens) {
      try {
        jwt.verify(token, this.secret);
        validCount++;
      } catch (error) {
        // 忽略错误
      }
    }
    
    const verifyTime = Date.now() - verifyStart;
    console.log(`Token验证: ${verifyTime}ms (${(verifyTime/iterations).toFixed(2)}ms/token)`);
    console.log(`验证成功率: ${(validCount/iterations*100).toFixed(2)}%`);
  }
}

// 使用示例
const jwtDiagnostic = new JWTDiagnostic(process.env.JWT_SECRET);

// 验证Token
const result = jwtDiagnostic.verifyToken('your-jwt-token-here');
console.log('验证结果:', result);
```

### 2. 输入验证问题

**参数验证工具**:

```javascript
/**
 * 输入验证诊断工具
 */
class InputValidationDiagnostic {
  /**
   * SQL注入检测
   */
  detectSQLInjection(input) {
    console.log('=== SQL注入检测 ===');
    
    const sqlPatterns = [
      /('|(\-\-)|(;)|(\||\|)|(\*|\*))/i,
      /(union|select|insert|delete|update|drop|create|alter|exec|execute)/i,
      /(script|javascript|vbscript|onload|onerror|onclick)/i
    ];
    
    const risks = [];
    
    sqlPatterns.forEach((pattern, index) => {
      if (pattern.test(input)) {
        risks.push({
          type: 'sql_injection',
          pattern: pattern.toString(),
          severity: index === 0 ? 'high' : 'medium'
        });
      }
    });
    
    if (risks.length > 0) {
      console.error('✗ 检测到SQL注入风险:', risks);
      return { safe: false, risks };
    } else {
      console.log('✓ 未检测到SQL注入风险');
      return { safe: true, risks: [] };
    }
  }
  
  /**
   * XSS攻击检测
   */
  detectXSS(input) {
    console.log('=== XSS攻击检测 ===');
    
    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<img[^>]*src[^>]*>/gi
    ];
    
    const risks = [];
    
    xssPatterns.forEach((pattern) => {
      const matches = input.match(pattern);
      if (matches) {
        risks.push({
          type: 'xss',
          pattern: pattern.toString(),
          matches: matches,
          severity: 'high'
        });
      }
    });
    
    if (risks.length > 0) {
      console.error('✗ 检测到XSS攻击风险:', risks);
      return { safe: false, risks };
    } else {
      console.log('✓ 未检测到XSS攻击风险');
      return { safe: true, risks: [] };
    }
  }
  
  /**
   * 参数长度检查
   */
  checkParameterLength(params, limits) {
    console.log('=== 参数长度检查 ===');
    
    const violations = [];
    
    Object.entries(params).forEach(([key, value]) => {
      const limit = limits[key];
      if (limit && typeof value === 'string' && value.length > limit) {
        violations.push({
          parameter: key,
          actualLength: value.length,
          maxLength: limit,
          severity: 'medium'
        });
      }
    });
    
    if (violations.length > 0) {
      console.error('✗ 参数长度超限:', violations);
      return { valid: false, violations };
    } else {
      console.log('✓ 参数长度检查通过');
      return { valid: true, violations: [] };
    }
  }
  
  /**
   * 综合安全检查
   */
  comprehensiveSecurityCheck(data) {
    console.log('开始综合安全检查...');
    
    const results = {
      timestamp: new Date().toISOString(),
      checks: {},
      overallSafe: true
    };
    
    // 检查每个字段
    Object.entries(data).forEach(([key, value]) => {
      if (typeof value === 'string') {
        const sqlCheck = this.detectSQLInjection(value);
        const xssCheck = this.detectXSS(value);
        
        results.checks[key] = {
          sqlInjection: sqlCheck,
          xss: xssCheck,
          safe: sqlCheck.safe && xssCheck.safe
        };
        
        if (!results.checks[key].safe) {
          results.overallSafe = false;
        }
      }
    });
    
    console.log('安全检查结果:', results.overallSafe ? '✓ 安全' : '✗ 存在风险');
    return results;
  }
}
```

## 部署问题排查

### 1. 容器化部署问题

**Docker诊断工具**:

```javascript
/**
 * Docker部署诊断脚本
 */
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

class DockerDiagnostic {
  /**
   * 检查Docker环境
   */
  async checkDockerEnvironment() {
    console.log('=== Docker环境检查 ===');
    
    try {
      // 检查Docker版本
      const { stdout: dockerVersion } = await execAsync('docker --version');
      console.log('✓ Docker版本:', dockerVersion.trim());
      
      // 检查Docker Compose版本
      try {
        const { stdout: composeVersion } = await execAsync('docker-compose --version');
        console.log('✓ Docker Compose版本:', composeVersion.trim());
      } catch (error) {
        console.warn('⚠ Docker Compose未安装');
      }
      
      // 检查Docker服务状态
      const { stdout: dockerInfo } = await execAsync('docker info --format "{{.ServerVersion}}"');
      console.log('✓ Docker服务运行正常，版本:', dockerInfo.trim());
      
      return true;
      
    } catch (error) {
      console.error('✗ Docker环境检查失败:', error.message);
      return false;
    }
  }
  
  /**
   * 检查容器状态
   */
  async checkContainerStatus(containerName) {
    console.log(`=== 容器状态检查: ${containerName} ===`);
    
    try {
      // 获取容器信息
      const { stdout } = await execAsync(`docker inspect ${containerName}`);
      const containerInfo = JSON.parse(stdout)[0];
      
      console.log('容器状态:', containerInfo.State.Status);
      console.log('容器健康状态:', containerInfo.State.Health?.Status || '未配置');
      console.log('重启次数:', containerInfo.RestartCount);
      
      if (containerInfo.State.Status !== 'running') {
        console.error('✗ 容器未运行');
        
        // 获取容器日志
        await this.getContainerLogs(containerName);
        return false;
      }
      
      console.log('✓ 容器运行正常');
      return true;
      
    } catch (error) {
      console.error('✗ 容器状态检查失败:', error.message);
      return false;
    }
  }
  
  /**
   * 获取容器日志
   */
  async getContainerLogs(containerName, lines = 50) {
    console.log(`=== 容器日志: ${containerName} ===`);
    
    try {
      const { stdout } = await execAsync(`docker logs --tail ${lines} ${containerName}`);
      console.log(stdout);
      
      // 分析错误日志
      const errorLines = stdout.split('\n').filter(line => 
        line.toLowerCase().includes('error') || 
        line.toLowerCase().includes('exception') ||
        line.toLowerCase().includes('failed')
      );
      
      if (errorLines.length > 0) {
        console.error('发现错误日志:');
        errorLines.forEach(line => console.error('  ', line));
      }
      
    } catch (error) {
      console.error('✗ 获取容器日志失败:', error.message);
    }
  }
  
  /**
   * 检查容器资源使用
   */
  async checkContainerResources(containerName) {
    console.log(`=== 容器资源使用: ${containerName} ===`);
    
    try {
      const { stdout } = await execAsync(`docker stats ${containerName} --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"`);
      console.log(stdout);
      
      // 解析资源使用情况
      const lines = stdout.trim().split('\n');
      if (lines.length > 1) {
        const stats = lines[1].split('\t');
        const cpuPercent = parseFloat(stats[0]);
        
        if (cpuPercent > 80) {
          console.warn(`⚠ CPU使用率过高: ${cpuPercent}%`);
        }
      }
      
    } catch (error) {
      console.error('✗ 检查容器资源失败:', error.message);
    }
  }
}
```

### 2. 服务器环境问题

**系统环境诊断**:

```javascript
/**
 * 系统环境诊断工具
 */
const os = require('os');
const fs = require('fs');
const path = require('path');

class SystemDiagnostic {
  /**
   * 系统信息检查
   */
  checkSystemInfo() {
    console.log('=== 系统信息检查 ===');
    
    const systemInfo = {
      platform: os.platform(),
      arch: os.arch(),
      release: os.release(),
      hostname: os.hostname(),
      uptime: os.uptime(),
      loadavg: os.loadavg(),
      totalmem: os.totalmem(),
      freemem: os.freemem(),
      cpus: os.cpus().length
    };
    
    console.log('操作系统:', systemInfo.platform, systemInfo.arch);
    console.log('系统版本:', systemInfo.release);
    console.log('主机名:', systemInfo.hostname);
    console.log('运行时间:', Math.floor(systemInfo.uptime / 3600), '小时');
    console.log('CPU核心数:', systemInfo.cpus);
    console.log('总内存:', (systemInfo.totalmem / 1024 / 1024 / 1024).toFixed(2), 'GB');
    console.log('可用内存:', (systemInfo.freemem / 1024 / 1024 / 1024).toFixed(2), 'GB');
    console.log('负载平均值:', systemInfo.loadavg.map(l => l.toFixed(2)).join(', '));
    
    // 内存使用率检查
    const memoryUsage = (1 - systemInfo.freemem / systemInfo.totalmem) * 100;
    if (memoryUsage > 80) {
      console.warn(`⚠ 内存使用率过高: ${memoryUsage.toFixed(2)}%`);
    }
    
    // 负载检查
    const avgLoad = systemInfo.loadavg[0];
    if (avgLoad > systemInfo.cpus) {
      console.warn(`⚠ 系统负载过高: ${avgLoad.toFixed(2)} (CPU核心数: ${systemInfo.cpus})`);
    }
    
    return systemInfo;
  }
  
  /**
   * 磁盘空间检查
   */
  async checkDiskSpace() {
    console.log('=== 磁盘空间检查 ===');
    
    try {
      const { stdout } = await execAsync('df -h');
      console.log(stdout);
      
      // 解析磁盘使用情况
      const lines = stdout.split('\n').slice(1);
      lines.forEach(line => {
        const parts = line.split(/\s+/);
        if (parts.length >= 5) {
          const usage = parseInt(parts[4]);
          if (usage > 80) {
            console.warn(`⚠ 磁盘使用率过高: ${parts[5]} ${usage}%`);
          }
        }
      });
      
    } catch (error) {
      console.error('✗ 磁盘空间检查失败:', error.message);
    }
  }
  
  /**
   * 网络连接检查
   */
  async checkNetworkConnections() {
    console.log('=== 网络连接检查 ===');
    
    try {
      // 检查监听端口
      const { stdout } = await execAsync('netstat -tlnp');
      console.log('监听端口:');
      
      const lines = stdout.split('\n');
      lines.forEach(line => {
        if (line.includes('LISTEN')) {
          const parts = line.split(/\s+/);
          console.log(`  ${parts[3]} -> ${parts[6] || 'unknown'}`);
        }
      });
      
    } catch (error) {
      console.error('✗ 网络连接检查失败:', error.message);
    }
  }
  
  /**
   * 服务状态检查
   */
  async checkServices(services) {
    console.log('=== 服务状态检查 ===');
    
    for (const service of services) {
      try {
        const { stdout } = await execAsync(`systemctl is-active ${service}`);
        const status = stdout.trim();
        
        if (status === 'active') {
          console.log(`✓ ${service}: 运行中`);
        } else {
          console.error(`✗ ${service}: ${status}`);
        }
        
      } catch (error) {
        console.error(`✗ ${service}: 检查失败 - ${error.message}`);
      }
    }
  }
}
```

## 监控和日志分析

### 1. 日志分析工具

**日志分析器**:

```javascript
/**
 * 日志分析工具
 */
const fs = require('fs');
const readline = require('readline');

class LogAnalyzer {
  constructor() {
    this.errorPatterns = [
      /error/i,
      /exception/i,
      /failed/i,
      /timeout/i,
      /refused/i
    ];
    
    this.warningPatterns = [
      /warning/i,
      /warn/i,
      /deprecated/i,
      /slow/i
    ];
  }
  
  /**
   * 分析日志文件
   */
  async analyzeLogFile(filePath, options = {}) {
    console.log(`=== 日志分析: ${filePath} ===`);
    
    const {
      timeRange = null,
      errorOnly = false,
      maxLines = 1000
    } = options;
    
    const stats = {
      totalLines: 0,
      errorLines: 0,
      warningLines: 0,
      errors: [],
      warnings: [],
      timeRange: timeRange
    };
    
    try {
      const fileStream = fs.createReadStream(filePath);
      const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
      });
      
      for await (const line of rl) {
        stats.totalLines++;
        
        if (stats.totalLines > maxLines) {
          console.log(`已达到最大行数限制: ${maxLines}`);
          break;
        }
        
        // 时间范围过滤
        if (timeRange && !this.isInTimeRange(line, timeRange)) {
          continue;
        }
        
        // 错误检测
        if (this.isErrorLine(line)) {
          stats.errorLines++;
          stats.errors.push({
            line: stats.totalLines,
            content: line,
            timestamp: this.extractTimestamp(line)
          });
        }
        
        // 警告检测
        if (this.isWarningLine(line)) {
          stats.warningLines++;
          stats.warnings.push({
            line: stats.totalLines,
            content: line,
            timestamp: this.extractTimestamp(line)
          });
        }
      }
      
      // 输出分析结果
      this.printAnalysisResults(stats);
      
      return stats;
      
    } catch (error) {
      console.error('✗ 日志分析失败:', error.message);
      return null;
    }
  }
  
  /**
   * 检查是否为错误行
   */
  isErrorLine(line) {
    return this.errorPatterns.some(pattern => pattern.test(line));
  }
  
  /**
   * 检查是否为警告行
   */
  isWarningLine(line) {
    return this.warningPatterns.some(pattern => pattern.test(line));
  }
  
  /**
   * 提取时间戳
   */
  extractTimestamp(line) {
    // 常见的时间戳格式
    const timePatterns = [
      /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/,
      /\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/,
      /\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}:\d{2}/
    ];
    
    for (const pattern of timePatterns) {
      const match = line.match(pattern);
      if (match) {
        return match[0];
      }
    }
    
    return null;
  }
  
  /**
   * 检查是否在时间范围内
   */
  isInTimeRange(line, timeRange) {
    const timestamp = this.extractTimestamp(line);
    if (!timestamp) return true;
    
    const lineTime = new Date(timestamp);
    const { start, end } = timeRange;
    
    return lineTime >= start && lineTime <= end;
  }
  
  /**
   * 打印分析结果
   */
  printAnalysisResults(stats) {
    console.log('\n=== 分析结果 ===');
    console.log(`总行数: ${stats.totalLines}`);
    console.log(`错误行数: ${stats.errorLines}`);
    console.log(`警告行数: ${stats.warningLines}`);
    console.log(`错误率: ${(stats.errorLines / stats.totalLines * 100).toFixed(2)}%`);
    
    if (stats.errors.length > 0) {
      console.log('\n最近的错误:');
      stats.errors.slice(-5).forEach(error => {
        console.log(`  行${error.line}: ${error.content}`);
      });
    }
    
    if (stats.warnings.length > 0) {
      console.log('\n最近的警告:');
      stats.warnings.slice(-5).forEach(warning => {
        console.log(`  行${warning.line}: ${warning.content}`);
      });
    }
  }
  
  /**
   * 生成日志报告
   */
  generateReport(stats, outputPath) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalLines: stats.totalLines,
        errorLines: stats.errorLines,
        warningLines: stats.warningLines,
        errorRate: stats.errorLines / stats.totalLines * 100
      },
      errors: stats.errors,
      warnings: stats.warnings,
      recommendations: this.generateRecommendations(stats)
    };
    
    fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
    console.log(`日志分析报告已生成: ${outputPath}`);
  }
  
  /**
   * 生成建议
   */
  generateRecommendations(stats) {
    const recommendations = [];
    
    if (stats.errorLines > stats.totalLines * 0.1) {
      recommendations.push('错误率过高，建议检查应用逻辑和配置');
    }
    
    if (stats.warningLines > stats.totalLines * 0.2) {
      recommendations.push('警告过多，建议优化代码和配置');
    }
    
    // 分析错误模式
    const errorMessages = stats.errors.map(e => e.content);
    const commonErrors = this.findCommonPatterns(errorMessages);
    
    if (commonErrors.length > 0) {
      recommendations.push(`发现常见错误模式: ${commonErrors.join(', ')}`);
    }
    
    return recommendations;
  }
  
  /**
   * 查找常见模式
   */
  findCommonPatterns(messages) {
    const patterns = {};
    
    messages.forEach(message => {
      // 简单的模式匹配
      if (message.includes('connection')) {
        patterns['连接问题'] = (patterns['连接问题'] || 0) + 1;
      }
      if (message.includes('timeout')) {
        patterns['超时问题'] = (patterns['超时问题'] || 0) + 1;
      }
      if (message.includes('database')) {
        patterns['数据库问题'] = (patterns['数据库问题'] || 0) + 1;
      }
    });
    
    return Object.keys(patterns).filter(pattern => patterns[pattern] > 2);
  }
}
```

## 常见问题FAQ

### 前端问题

**Q: 小程序页面白屏怎么办？**
A: 
1. 检查 app.json 配置是否正确
2. 查看控制台错误信息
3. 检查页面路径是否正确
4. 确认网络请求是否正常

**Q: 数据不显示怎么排查？**
A:
1. 检查 API 请求是否成功
2. 验证数据格式是否正确
3. 确认 setData 是否正确调用
4. 检查 WXML 模板语法

**Q: 页面跳转失败怎么处理？**
A:
1. 检查页面栈是否溢出
2. 确认页面路径是否正确
3. 检查参数长度是否超限
4. 使用 redirectTo 或 reLaunch

### 后端问题

**Q: API 响应慢怎么优化？**
A:
1. 检查数据库查询是否有索引
2. 优化 SQL 查询语句
3. 使用缓存减少数据库访问
4. 检查网络延迟

**Q: 数据库连接失败怎么处理？**
A:
1. 检查数据库服务是否启动
2. 验证连接参数是否正确
3. 检查网络连通性
4. 查看数据库日志

**Q: 内存使用过高怎么排查？**
A:
1. 检查是否有内存泄漏
2. 优化数据结构和算法
3. 使用对象池减少 GC 压力
4. 监控内存使用趋势

### 部署问题

**Q: 服务启动失败怎么排查？**
A:
1. 检查端口是否被占用
2. 验证环境变量配置
3. 查看启动日志
4. 检查文件权限

**Q: Docker 容器无法启动？**
A:
1. 检查 Dockerfile 配置
2. 验证镜像是否正确构建
3. 查看容器日志
4. 检查资源限制

## 应急处理流程

### 1. 故障响应流程

```
发现故障 → 评估影响 → 分类处理 → 快速修复 → 根因分析 → 预防措施
```

### 2. 故障等级定义

- **P0 - 紧急**: 核心功能完全不可用，影响所有用户
- **P1 - 高**: 主要功能受影响，影响大部分用户
- **P2 - 中**: 部分功能受影响，影响少部分用户
- **P3 - 低**: 轻微影响，不影响核心功能

### 3. 应急联系方式

- **技术负责人**: [联系方式]
- **运维团队**: [联系方式]
- **产品负责人**: [联系方式]

### 4. 故障记录模板

```markdown
## 故障报告

**故障时间**: 
**故障等级**: 
**影响范围**: 
**故障现象**: 
**根本原因**: 
**解决方案**: 
**预防措施**: 
**责任人**: 
```

---

本故障排查指南涵盖了美团联盟小程序开发和运维过程中可能遇到的各种问题。建议开发团队定期更新和完善本指南，确保能够快速定位和解决问题。