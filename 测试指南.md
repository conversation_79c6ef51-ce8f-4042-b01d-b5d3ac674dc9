# 美团联盟小程序测试指南

## 目录
- [测试概述](#测试概述)
- [测试环境准备](#测试环境准备)
- [前端测试](#前端测试)
- [后端API测试](#后端api测试)
- [集成测试](#集成测试)
- [性能测试](#性能测试)
- [安全测试](#安全测试)
- [用户验收测试](#用户验收测试)
- [自动化测试](#自动化测试)
- [测试报告](#测试报告)

## 测试概述

### 测试目标
- 确保所有功能模块正常工作
- 验证API接口的正确性和稳定性
- 保证用户体验的流畅性
- 确认系统的安全性和性能

### 测试范围
- **前端功能测试**: 小程序各页面功能
- **后端API测试**: 接口功能和数据验证
- **集成测试**: 前后端交互测试
- **性能测试**: 响应时间和并发测试
- **安全测试**: 数据安全和权限验证
- **兼容性测试**: 不同设备和微信版本

### 测试策略
- **测试驱动开发**: 先写测试，后写代码
- **持续集成**: 代码提交自动触发测试
- **分层测试**: 单元测试 → 集成测试 → 系统测试
- **风险驱动**: 优先测试高风险功能

## 测试环境准备

### 1. 开发环境配置

#### 1.1 微信开发者工具

```javascript
/**
 * 测试环境配置
 * 在 app.js 中添加测试模式配置
 */
App({
  globalData: {
    // 测试环境标识
    isTestMode: true,
    
    // 测试API地址
    apiBaseUrl: 'http://localhost:3000/api',
    
    // 测试用户信息
    testUser: {
      openid: 'test_openid_123',
      nickname: '测试用户',
      avatar: 'https://example.com/test-avatar.jpg'
    },
    
    // 调试配置
    debug: {
      enableConsole: true,
      enableMock: true,
      enablePerformanceMonitor: true
    }
  },
  
  /**
   * 初始化测试环境
   */
  onLaunch: function() {
    if (this.globalData.isTestMode) {
      console.log('=== 测试模式启动 ===');
      this.initTestEnvironment();
    }
  },
  
  /**
   * 初始化测试环境
   */
  initTestEnvironment: function() {
    // 启用调试模式
    wx.setEnableDebug({
      enableDebug: true
    });
    
    // 模拟登录状态
    if (this.globalData.debug.enableMock) {
      wx.setStorageSync('token', 'test_token_123');
      wx.setStorageSync('userInfo', this.globalData.testUser);
    }
    
    // 性能监控
    if (this.globalData.debug.enablePerformanceMonitor) {
      this.startPerformanceMonitor();
    }
  },
  
  /**
   * 启动性能监控
   */
  startPerformanceMonitor: function() {
    const performance = wx.getPerformance();
    const observer = performance.createObserver((entryList) => {
      console.log('性能数据:', entryList.getEntries());
    });
    observer.observe({ entryTypes: ['render', 'script'] });
  }
});
```

#### 1.2 测试数据准备

**创建 `test/data/mock-data.js` 文件**:
```javascript
/**
 * 测试模拟数据
 */
const mockData = {
  // 用户数据
  users: [
    {
      id: 1,
      openid: 'test_openid_123',
      nickname: '测试用户1',
      avatar: 'https://example.com/avatar1.jpg',
      phone: '13800138001'
    },
    {
      id: 2,
      openid: 'test_openid_456',
      nickname: '测试用户2',
      avatar: 'https://example.com/avatar2.jpg',
      phone: '13800138002'
    }
  ],
  
  // 商户数据
  merchants: [
    {
      poi_id: '123456',
      poi_name: '测试餐厅1',
      category_name: '美食',
      avg_price: 50,
      pic_url: 'https://example.com/restaurant1.jpg',
      address: '北京市朝阳区测试街道1号',
      commission_rate: '5%',
      promotion_url: 'https://example.com/promotion1'
    },
    {
      poi_id: '789012',
      poi_name: '测试酒店1',
      category_name: '酒店',
      avg_price: 200,
      pic_url: 'https://example.com/hotel1.jpg',
      address: '北京市海淀区测试大道2号',
      commission_rate: '8%',
      promotion_url: 'https://example.com/promotion2'
    }
  ],
  
  // 社区内容数据
  posts: [
    {
      id: 1,
      title: '测试帖子1',
      content: '这是一个测试帖子的内容',
      images: ['https://example.com/post1.jpg'],
      author: '测试用户1',
      created_at: '2024-01-01 12:00:00',
      likes: 10,
      comments: 5
    }
  ],
  
  // API响应模板
  apiResponses: {
    success: {
      code: 200,
      message: '成功',
      data: null
    },
    error: {
      code: 500,
      message: '服务器错误',
      data: null
    },
    unauthorized: {
      code: 401,
      message: '未授权',
      data: null
    }
  }
};

module.exports = mockData;
```

### 2. 测试工具安装

#### 2.1 前端测试工具

```bash
# 创建测试目录
mkdir test
cd test

# 初始化package.json
npm init -y

# 安装测试框架
npm install --save-dev jest
npm install --save-dev @jest/globals
npm install --save-dev jest-environment-jsdom

# 安装小程序测试工具
npm install --save-dev miniprogram-simulate
npm install --save-dev miniprogram-api-typings
```

#### 2.2 后端测试工具

```bash
# 进入后端项目目录
cd ../backend

# 安装测试依赖
npm install --save-dev mocha
npm install --save-dev chai
npm install --save-dev supertest
npm install --save-dev sinon
npm install --save-dev nyc  # 代码覆盖率
```

## 前端测试

### 1. 页面功能测试

#### 1.1 首页测试

**创建 `test/pages/index.test.js` 文件**:
```javascript
/**
 * 首页功能测试
 */
const simulate = require('miniprogram-simulate');

describe('首页测试', () => {
  let id;
  
  beforeAll(() => {
    // 加载页面
    id = simulate.load({
      template: `
        <view class="container">
          <swiper class="banner-swiper">
            <swiper-item wx:for="{{banners}}" wx:key="id">
              <image src="{{item.image}}" mode="aspectFill"></image>
            </swiper-item>
          </swiper>
          
          <view class="category-grid">
            <view class="category-item" wx:for="{{categories}}" wx:key="id" 
                  bindtap="onCategoryTap" data-id="{{item.id}}">
              <image src="{{item.icon}}"></image>
              <text>{{item.name}}</text>
            </view>
          </view>
          
          <view class="recommend-section">
            <text class="section-title">推荐商家</text>
            <view class="merchant-list">
              <view class="merchant-item" wx:for="{{merchants}}" wx:key="poi_id"
                    bindtap="onMerchantTap" data-poi-id="{{item.poi_id}}">
                <image src="{{item.pic_url}}"></image>
                <view class="merchant-info">
                  <text class="name">{{item.poi_name}}</text>
                  <text class="price">人均 ¥{{item.avg_price}}</text>
                  <text class="commission">返佣 {{item.commission_rate}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      `,
      usingComponents: {},
      data: {
        banners: [
          { id: 1, image: 'https://example.com/banner1.jpg' },
          { id: 2, image: 'https://example.com/banner2.jpg' }
        ],
        categories: [
          { id: 1, name: '美食', icon: 'https://example.com/food.png' },
          { id: 2, name: '酒店', icon: 'https://example.com/hotel.png' }
        ],
        merchants: [
          {
            poi_id: '123456',
            poi_name: '测试餐厅',
            avg_price: 50,
            commission_rate: '5%',
            pic_url: 'https://example.com/restaurant.jpg'
          }
        ]
      },
      methods: {
        /**
         * 分类点击事件
         */
        onCategoryTap: function(e) {
          const categoryId = e.currentTarget.dataset.id;
          console.log('点击分类:', categoryId);
          // 模拟页面跳转
          wx.navigateTo({
            url: `/pages/products/products?category=${categoryId}`
          });
        },
        
        /**
         * 商家点击事件
         */
        onMerchantTap: function(e) {
          const poiId = e.currentTarget.dataset.poiId;
          console.log('点击商家:', poiId);
          // 模拟生成推广链接
          this.generatePromotionLink(poiId);
        },
        
        /**
         * 生成推广链接
         */
        generatePromotionLink: function(poiId) {
          // 模拟API调用
          return new Promise((resolve) => {
            setTimeout(() => {
              const promotionUrl = `https://example.com/promotion/${poiId}`;
              resolve(promotionUrl);
            }, 100);
          });
        }
      }
    }, 'page');
  });
  
  afterAll(() => {
    simulate.unload(id);
  });
  
  /**
   * 测试页面初始化
   */
  test('页面初始化正常', () => {
    const comp = simulate.render(id);
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    // 检查轮播图
    const banners = comp.querySelectorAll('.banner-swiper swiper-item');
    expect(banners.length).toBe(2);
    
    // 检查分类网格
    const categories = comp.querySelectorAll('.category-item');
    expect(categories.length).toBe(2);
    
    // 检查推荐商家
    const merchants = comp.querySelectorAll('.merchant-item');
    expect(merchants.length).toBe(1);
  });
  
  /**
   * 测试分类点击
   */
  test('分类点击跳转正常', () => {
    const comp = simulate.render(id);
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    // 模拟点击第一个分类
    const firstCategory = comp.querySelector('.category-item');
    firstCategory.dispatchEvent('tap', {
      currentTarget: {
        dataset: { id: 1 }
      }
    });
    
    // 验证跳转逻辑（这里需要mock wx.navigateTo）
    // 实际项目中需要使用jest.mock来模拟微信API
  });
  
  /**
   * 测试商家点击
   */
  test('商家点击生成推广链接', async () => {
    const comp = simulate.render(id);
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    // 模拟点击商家
    const merchantItem = comp.querySelector('.merchant-item');
    merchantItem.dispatchEvent('tap', {
      currentTarget: {
        dataset: { poiId: '123456' }
      }
    });
    
    // 验证推广链接生成
    const promotionUrl = await comp.instance.generatePromotionLink('123456');
    expect(promotionUrl).toContain('123456');
  });
});
```

#### 1.2 商品列表页测试

**创建 `test/pages/products.test.js` 文件**:
```javascript
/**
 * 商品列表页测试
 */
const simulate = require('miniprogram-simulate');
const mockData = require('../data/mock-data');

describe('商品列表页测试', () => {
  let id;
  
  beforeAll(() => {
    id = simulate.load({
      template: `
        <view class="container">
          <!-- 搜索栏 -->
          <view class="search-bar">
            <input placeholder="搜索商家或商品" bindinput="onSearchInput" 
                   value="{{searchKeyword}}" />
            <button bindtap="onSearch">搜索</button>
          </view>
          
          <!-- 筛选栏 -->
          <view class="filter-bar">
            <view class="filter-item" wx:for="{{filters}}" wx:key="key"
                  bindtap="onFilterTap" data-key="{{item.key}}" 
                  class="{{item.active ? 'active' : ''}}">
              <text>{{item.label}}</text>
            </view>
          </view>
          
          <!-- 商品列表 -->
          <scroll-view class="product-list" scroll-y="true" 
                       bindscrolltolower="onLoadMore">
            <view class="product-item" wx:for="{{products}}" wx:key="poi_id"
                  bindtap="onProductTap" data-poi-id="{{item.poi_id}}">
              <image src="{{item.pic_url}}" mode="aspectFill"></image>
              <view class="product-info">
                <text class="name">{{item.poi_name}}</text>
                <text class="category">{{item.category_name}}</text>
                <text class="price">人均 ¥{{item.avg_price}}</text>
                <text class="commission">返佣 {{item.commission_rate}}</text>
                <text class="address">{{item.address}}</text>
              </view>
              <view class="actions">
                <button bindtap="onFavorite" data-poi-id="{{item.poi_id}}"
                        class="{{item.is_favorite ? 'favorited' : ''}}">
                  {{item.is_favorite ? '已收藏' : '收藏'}}
                </button>
                <button bindtap="onShare" data-poi-id="{{item.poi_id}}">
                  分享
                </button>
              </view>
            </view>
          </scroll-view>
          
          <!-- 加载状态 -->
          <view class="loading" wx:if="{{loading}}">
            <text>加载中...</text>
          </view>
        </view>
      `,
      data: {
        searchKeyword: '',
        filters: [
          { key: 'all', label: '全部', active: true },
          { key: 'food', label: '美食', active: false },
          { key: 'hotel', label: '酒店', active: false }
        ],
        products: mockData.merchants.map(item => ({
          ...item,
          is_favorite: false
        })),
        loading: false,
        hasMore: true,
        page: 1
      },
      methods: {
        /**
         * 搜索输入
         */
        onSearchInput: function(e) {
          this.setData({
            searchKeyword: e.detail.value
          });
        },
        
        /**
         * 执行搜索
         */
        onSearch: function() {
          console.log('搜索关键词:', this.data.searchKeyword);
          this.loadProducts(true);
        },
        
        /**
         * 筛选点击
         */
        onFilterTap: function(e) {
          const filterKey = e.currentTarget.dataset.key;
          const filters = this.data.filters.map(item => ({
            ...item,
            active: item.key === filterKey
          }));
          
          this.setData({ filters });
          this.loadProducts(true);
        },
        
        /**
         * 商品点击
         */
        onProductTap: function(e) {
          const poiId = e.currentTarget.dataset.poiId;
          console.log('点击商品:', poiId);
          
          // 生成推广链接并跳转
          this.generateAndNavigate(poiId);
        },
        
        /**
         * 收藏操作
         */
        onFavorite: function(e) {
          e.stopPropagation(); // 阻止事件冒泡
          const poiId = e.currentTarget.dataset.poiId;
          
          const products = this.data.products.map(item => {
            if (item.poi_id === poiId) {
              return { ...item, is_favorite: !item.is_favorite };
            }
            return item;
          });
          
          this.setData({ products });
          
          // 模拟API调用
          this.updateFavoriteStatus(poiId, !this.getFavoriteStatus(poiId));
        },
        
        /**
         * 分享操作
         */
        onShare: function(e) {
          e.stopPropagation();
          const poiId = e.currentTarget.dataset.poiId;
          console.log('分享商品:', poiId);
          
          // 模拟分享逻辑
          wx.showShareMenu({
            withShareTicket: true
          });
        },
        
        /**
         * 加载更多
         */
        onLoadMore: function() {
          if (!this.data.hasMore || this.data.loading) {
            return;
          }
          
          this.loadProducts(false);
        },
        
        /**
         * 加载商品数据
         */
        loadProducts: function(reset = false) {
          this.setData({ loading: true });
          
          // 模拟API调用
          setTimeout(() => {
            const newProducts = reset ? mockData.merchants : 
              [...this.data.products, ...mockData.merchants];
            
            this.setData({
              products: newProducts,
              loading: false,
              hasMore: newProducts.length < 100, // 模拟总数限制
              page: reset ? 1 : this.data.page + 1
            });
          }, 500);
        },
        
        /**
         * 生成推广链接并跳转
         */
        generateAndNavigate: function(poiId) {
          return new Promise((resolve) => {
            setTimeout(() => {
              const promotionUrl = `https://example.com/promotion/${poiId}`;
              // 模拟跳转到美团小程序
              wx.navigateToMiniProgram({
                appId: 'meituan_app_id',
                path: promotionUrl
              });
              resolve(promotionUrl);
            }, 100);
          });
        },
        
        /**
         * 更新收藏状态
         */
        updateFavoriteStatus: function(poiId, isFavorite) {
          // 模拟API调用
          return new Promise((resolve) => {
            setTimeout(() => {
              console.log(`商品 ${poiId} 收藏状态更新为: ${isFavorite}`);
              resolve({ success: true });
            }, 200);
          });
        },
        
        /**
         * 获取收藏状态
         */
        getFavoriteStatus: function(poiId) {
          const product = this.data.products.find(item => item.poi_id === poiId);
          return product ? product.is_favorite : false;
        }
      }
    }, 'page');
  });
  
  afterAll(() => {
    simulate.unload(id);
  });
  
  /**
   * 测试页面初始化
   */
  test('页面初始化正常', () => {
    const comp = simulate.render(id);
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    // 检查搜索栏
    const searchInput = comp.querySelector('.search-bar input');
    expect(searchInput).toBeTruthy();
    
    // 检查筛选栏
    const filters = comp.querySelectorAll('.filter-item');
    expect(filters.length).toBe(3);
    
    // 检查商品列表
    const products = comp.querySelectorAll('.product-item');
    expect(products.length).toBe(mockData.merchants.length);
  });
  
  /**
   * 测试搜索功能
   */
  test('搜索功能正常', () => {
    const comp = simulate.render(id);
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    // 模拟输入搜索关键词
    const searchInput = comp.querySelector('.search-bar input');
    searchInput.dispatchEvent('input', {
      detail: { value: '测试餐厅' }
    });
    
    expect(comp.instance.data.searchKeyword).toBe('测试餐厅');
    
    // 模拟点击搜索按钮
    const searchButton = comp.querySelector('.search-bar button');
    searchButton.dispatchEvent('tap');
    
    // 验证搜索逻辑执行
    expect(comp.instance.data.loading).toBe(true);
  });
  
  /**
   * 测试筛选功能
   */
  test('筛选功能正常', () => {
    const comp = simulate.render(id);
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    // 模拟点击美食筛选
    const foodFilter = comp.querySelectorAll('.filter-item')[1];
    foodFilter.dispatchEvent('tap', {
      currentTarget: {
        dataset: { key: 'food' }
      }
    });
    
    // 验证筛选状态更新
    const activeFilter = comp.instance.data.filters.find(item => item.active);
    expect(activeFilter.key).toBe('food');
  });
  
  /**
   * 测试收藏功能
   */
  test('收藏功能正常', () => {
    const comp = simulate.render(id);
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    // 模拟点击收藏按钮
    const favoriteButton = comp.querySelector('.actions button');
    favoriteButton.dispatchEvent('tap', {
      currentTarget: {
        dataset: { poiId: '123456' }
      },
      stopPropagation: () => {}
    });
    
    // 验证收藏状态更新
    const product = comp.instance.data.products.find(item => item.poi_id === '123456');
    expect(product.is_favorite).toBe(true);
  });
});
```

### 2. 组件测试

#### 2.1 自定义组件测试

**创建 `test/components/merchant-card.test.js` 文件**:
```javascript
/**
 * 商家卡片组件测试
 */
const simulate = require('miniprogram-simulate');

describe('商家卡片组件测试', () => {
  let id;
  
  beforeAll(() => {
    id = simulate.load({
      template: `
        <view class="merchant-card" bindtap="onTap">
          <image class="merchant-image" src="{{merchant.pic_url}}" 
                 mode="aspectFill"></image>
          <view class="merchant-info">
            <text class="name">{{merchant.poi_name}}</text>
            <text class="category">{{merchant.category_name}}</text>
            <text class="price">人均 ¥{{merchant.avg_price}}</text>
            <text class="commission">返佣 {{merchant.commission_rate}}</text>
            <text class="address">{{merchant.address}}</text>
          </view>
          <view class="actions">
            <button class="favorite-btn" bindtap="onFavorite"
                    class="{{merchant.is_favorite ? 'favorited' : ''}}">
              {{merchant.is_favorite ? '已收藏' : '收藏'}}
            </button>
            <button class="share-btn" bindtap="onShare">分享</button>
          </view>
        </view>
      `,
      properties: {
        merchant: {
          type: Object,
          value: {}
        }
      },
      methods: {
        /**
         * 卡片点击事件
         */
        onTap: function() {
          this.triggerEvent('tap', {
            merchant: this.data.merchant
          });
        },
        
        /**
         * 收藏点击事件
         */
        onFavorite: function(e) {
          e.stopPropagation();
          this.triggerEvent('favorite', {
            poiId: this.data.merchant.poi_id,
            isFavorite: !this.data.merchant.is_favorite
          });
        },
        
        /**
         * 分享点击事件
         */
        onShare: function(e) {
          e.stopPropagation();
          this.triggerEvent('share', {
            merchant: this.data.merchant
          });
        }
      }
    }, 'component');
  });
  
  afterAll(() => {
    simulate.unload(id);
  });
  
  /**
   * 测试组件渲染
   */
  test('组件渲染正常', () => {
    const comp = simulate.render(id, {
      merchant: {
        poi_id: '123456',
        poi_name: '测试餐厅',
        category_name: '美食',
        avg_price: 50,
        commission_rate: '5%',
        address: '测试地址',
        pic_url: 'https://example.com/test.jpg',
        is_favorite: false
      }
    });
    
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    // 检查商家名称
    const nameElement = comp.querySelector('.name');
    expect(nameElement.textContent).toBe('测试餐厅');
    
    // 检查分类
    const categoryElement = comp.querySelector('.category');
    expect(categoryElement.textContent).toBe('美食');
    
    // 检查价格
    const priceElement = comp.querySelector('.price');
    expect(priceElement.textContent).toBe('人均 ¥50');
  });
  
  /**
   * 测试点击事件
   */
  test('点击事件正常触发', () => {
    const comp = simulate.render(id, {
      merchant: {
        poi_id: '123456',
        poi_name: '测试餐厅'
      }
    });
    
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    let tapEventTriggered = false;
    comp.addEventListener('tap', (e) => {
      tapEventTriggered = true;
      expect(e.detail.merchant.poi_id).toBe('123456');
    });
    
    // 模拟点击卡片
    comp.dispatchEvent('tap');
    expect(tapEventTriggered).toBe(true);
  });
  
  /**
   * 测试收藏功能
   */
  test('收藏功能正常', () => {
    const comp = simulate.render(id, {
      merchant: {
        poi_id: '123456',
        is_favorite: false
      }
    });
    
    const parent = document.createElement('parent-wrapper');
    comp.attach(parent);
    
    let favoriteEventTriggered = false;
    comp.addEventListener('favorite', (e) => {
      favoriteEventTriggered = true;
      expect(e.detail.poiId).toBe('123456');
      expect(e.detail.isFavorite).toBe(true);
    });
    
    // 模拟点击收藏按钮
    const favoriteBtn = comp.querySelector('.favorite-btn');
    favoriteBtn.dispatchEvent('tap', {
      stopPropagation: () => {}
    });
    
    expect(favoriteEventTriggered).toBe(true);
  });
});
```

## 后端API测试

### 1. 单元测试

#### 1.1 用户服务测试

**创建 `test/services/user.test.js` 文件**:
```javascript
/**
 * 用户服务单元测试
 */
const { expect } = require('chai');
const sinon = require('sinon');
const UserService = require('../../services/UserService');
const User = require('../../models/User');

describe('UserService 测试', () => {
  let userService;
  let userModelStub;
  
  beforeEach(() => {
    userService = new UserService();
    userModelStub = sinon.stub(User);
  });
  
  afterEach(() => {
    sinon.restore();
  });
  
  describe('createUser', () => {
    /**
     * 测试创建用户成功
     */
    it('应该成功创建用户', async () => {
      const userData = {
        openid: 'test_openid_123',
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.jpg'
      };
      
      const expectedUser = {
        id: 1,
        ...userData,
        created_at: new Date()
      };
      
      userModelStub.create.resolves(expectedUser);
      
      const result = await userService.createUser(userData);
      
      expect(result).to.deep.equal(expectedUser);
      expect(userModelStub.create.calledOnce).to.be.true;
      expect(userModelStub.create.calledWith(userData)).to.be.true;
    });
    
    /**
     * 测试创建用户失败
     */
    it('应该处理创建用户失败的情况', async () => {
      const userData = {
        openid: 'test_openid_123'
      };
      
      const error = new Error('数据库错误');
      userModelStub.create.rejects(error);
      
      try {
        await userService.createUser(userData);
        expect.fail('应该抛出错误');
      } catch (err) {
        expect(err.message).to.equal('数据库错误');
      }
    });
  });
  
  describe('getUserByOpenid', () => {
    /**
     * 测试根据openid获取用户
     */
    it('应该根据openid返回用户信息', async () => {
      const openid = 'test_openid_123';
      const expectedUser = {
        id: 1,
        openid: openid,
        nickname: '测试用户'
      };
      
      userModelStub.findOne.resolves(expectedUser);
      
      const result = await userService.getUserByOpenid(openid);
      
      expect(result).to.deep.equal(expectedUser);
      expect(userModelStub.findOne.calledOnce).to.be.true;
      expect(userModelStub.findOne.calledWith({ where: { openid } })).to.be.true;
    });
    
    /**
     * 测试用户不存在的情况
     */
    it('应该在用户不存在时返回null', async () => {
      const openid = 'nonexistent_openid';
      
      userModelStub.findOne.resolves(null);
      
      const result = await userService.getUserByOpenid(openid);
      
      expect(result).to.be.null;
    });
  });
  
  describe('updateUserProfile', () => {
    /**
     * 测试更新用户资料
     */
    it('应该成功更新用户资料', async () => {
      const userId = 1;
      const updateData = {
        nickname: '新昵称',
        phone: '13800138000'
      };
      
      const updatedUser = {
        id: userId,
        ...updateData,
        updated_at: new Date()
      };
      
      userModelStub.update.resolves([1]); // 返回受影响的行数
      userModelStub.findByPk.resolves(updatedUser);
      
      const result = await userService.updateUserProfile(userId, updateData);
      
      expect(result).to.deep.equal(updatedUser);
      expect(userModelStub.update.calledOnce).to.be.true;
      expect(userModelStub.findByPk.calledWith(userId)).to.be.true;
    });
  });
});
```

#### 1.2 API控制器测试

**创建 `test/controllers/user.test.js` 文件**:
```javascript
/**
 * 用户控制器测试
 */
const request = require('supertest');
const { expect } = require('chai');
const sinon = require('sinon');
const app = require('../../app');
const UserService = require('../../services/UserService');
const AuthService = require('../../services/AuthService');

describe('User Controller 测试', () => {
  let userServiceStub;
  let authServiceStub;
  
  beforeEach(() => {
    userServiceStub = sinon.stub(UserService.prototype);
    authServiceStub = sinon.stub(AuthService.prototype);
  });
  
  afterEach(() => {
    sinon.restore();
  });
  
  describe('POST /api/user/login', () => {
    /**
     * 测试用户登录成功
     */
    it('应该成功登录并返回token', async () => {
      const loginData = {
        code: 'wx_login_code_123',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      };
      
      const mockUser = {
        id: 1,
        openid: 'test_openid_123',
        nickname: '测试用户'
      };
      
      const mockToken = 'jwt_token_123';
      
      authServiceStub.getOpenidByCode.resolves('test_openid_123');
      userServiceStub.getUserByOpenid.resolves(mockUser);
      authServiceStub.generateToken.returns(mockToken);
      
      const response = await request(app)
        .post('/api/user/login')
        .send(loginData)
        .expect(200);
      
      expect(response.body.code).to.equal(200);
      expect(response.body.message).to.equal('登录成功');
      expect(response.body.data.token).to.equal(mockToken);
      expect(response.body.data.userInfo).to.deep.equal(mockUser);
    });
    
    /**
     * 测试新用户注册
     */
    it('应该为新用户创建账户', async () => {
      const loginData = {
        code: 'wx_login_code_123',
        userInfo: {
          nickName: '新用户',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      };
      
      const newUser = {
        id: 2,
        openid: 'new_openid_123',
        nickname: '新用户'
      };
      
      authServiceStub.getOpenidByCode.resolves('new_openid_123');
      userServiceStub.getUserByOpenid.resolves(null); // 用户不存在
      userServiceStub.createUser.resolves(newUser);
      authServiceStub.generateToken.returns('new_token_123');
      
      const response = await request(app)
        .post('/api/user/login')
        .send(loginData)
        .expect(200);
      
      expect(response.body.code).to.equal(200);
      expect(response.body.data.userInfo.id).to.equal(2);
      expect(userServiceStub.createUser.calledOnce).to.be.true;
    });
    
    /**
     * 测试登录参数错误
     */
    it('应该处理登录参数错误', async () => {
      const response = await request(app)
        .post('/api/user/login')
        .send({})
        .expect(400);
      
      expect(response.body.code).to.equal(400);
      expect(response.body.message).to.include('参数');
    });
  });
  
  describe('GET /api/user/profile', () => {
    /**
     * 测试获取用户资料
     */
    it('应该返回用户资料', async () => {
      const mockUser = {
        id: 1,
        nickname: '测试用户',
        phone: '13800138000',
        total_commission: 1250.50
      };
      
      userServiceStub.getUserProfile.resolves(mockUser);
      
      const response = await request(app)
        .get('/api/user/profile')
        .set('Authorization', 'Bearer valid_token')
        .expect(200);
      
      expect(response.body.code).to.equal(200);
      expect(response.body.data).to.deep.equal(mockUser);
    });
    
    /**
     * 测试未授权访问
     */
    it('应该拒绝未授权的访问', async () => {
      const response = await request(app)
        .get('/api/user/profile')
        .expect(401);
      
      expect(response.body.code).to.equal(401);
      expect(response.body.message).to.include('未授权');
    });
  });
});
```

### 2. 集成测试

#### 2.1 API集成测试

**创建 `test/integration/api.test.js` 文件**:
```javascript
/**
 * API集成测试
 */
const request = require('supertest');
const { expect } = require('chai');
const app = require('../../app');
const db = require('../../config/database');

describe('API 集成测试', () => {
  let authToken;
  let testUser;
  
  before(async () => {
    // 初始化测试数据库
    await db.sync({ force: true });
    
    // 创建测试用户并获取token
    const loginResponse = await request(app)
      .post('/api/user/login')
      .send({
        code: 'test_code',
        userInfo: {
          nickName: '集成测试用户',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      });
    
    authToken = loginResponse.body.data.token;
    testUser = loginResponse.body.data.userInfo;
  });
  
  after(async () => {
    // 清理测试数据
    await db.drop();
  });
  
  describe('用户相关API', () => {
    /**
     * 测试完整的用户流程
     */
    it('应该完成完整的用户操作流程', async () => {
      // 1. 获取用户资料
      const profileResponse = await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(profileResponse.body.data.id).to.equal(testUser.id);
      
      // 2. 更新用户资料
      const updateData = {
        nickname: '更新后的昵称',
        phone: '13800138000'
      };
      
      const updateResponse = await request(app)
        .put('/api/user/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);
      
      expect(updateResponse.body.data.nickname).to.equal(updateData.nickname);
      
      // 3. 验证更新结果
      const updatedProfileResponse = await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(updatedProfileResponse.body.data.nickname).to.equal(updateData.nickname);
    });
  });
  
  describe('收藏相关API', () => {
    /**
     * 测试收藏功能流程
     */
    it('应该完成完整的收藏操作流程', async () => {
      const favoriteData = {
        poi_id: '123456',
        poi_name: '测试餐厅',
        poi_image: 'https://example.com/restaurant.jpg',
        category: '美食',
        avg_price: 50
      };
      
      // 1. 添加收藏
      const addResponse = await request(app)
        .post('/api/favorites')
        .set('Authorization', `Bearer ${authToken}`)
        .send(favoriteData)
        .expect(200);
      
      expect(addResponse.body.code).to.equal(200);
      
      // 2. 获取收藏列表
      const listResponse = await request(app)
        .get('/api/favorites')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(listResponse.body.data.list.length).to.be.greaterThan(0);
      expect(listResponse.body.data.list[0].poi_id).to.equal(favoriteData.poi_id);
      
      // 3. 删除收藏
      const deleteResponse = await request(app)
        .delete(`/api/favorites/${favoriteData.poi_id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(deleteResponse.body.code).to.equal(200);
      
      // 4. 验证删除结果
      const emptyListResponse = await request(app)
        .get('/api/favorites')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(emptyListResponse.body.data.list.length).to.equal(0);
    });
  });
  
  describe('社区相关API', () => {
    /**
     * 测试社区内容流程
     */
    it('应该完成完整的社区内容操作流程', async () => {
      const postData = {
        title: '测试帖子标题',
        content: '这是一个测试帖子的内容',
        images: ['https://example.com/image1.jpg'],
        tags: ['美食', '推荐']
      };
      
      // 1. 发布内容
      const createResponse = await request(app)
        .post('/api/community/posts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(postData)
        .expect(200);
      
      const postId = createResponse.body.data.id;
      expect(postId).to.be.a('number');
      
      // 2. 获取内容列表
      const listResponse = await request(app)
        .get('/api/community/posts')
        .expect(200);
      
      expect(listResponse.body.data.list.length).to.be.greaterThan(0);
      
      // 3. 获取内容详情
      const detailResponse = await request(app)
        .get(`/api/community/posts/${postId}`)
        .expect(200);
      
      expect(detailResponse.body.data.title).to.equal(postData.title);
      
      // 4. 点赞内容
      const likeResponse = await request(app)
        .post(`/api/community/posts/${postId}/like`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(likeResponse.body.code).to.equal(200);
      
      // 5. 评论内容
      const commentData = {
        content: '这是一个测试评论'
      };
      
      const commentResponse = await request(app)
        .post(`/api/community/posts/${postId}/comments`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(commentData)
        .expect(200);
      
      expect(commentResponse.body.code).to.equal(200);
    });
  });
});
```

## 性能测试

### 1. 负载测试

**创建 `test/performance/load.test.js` 文件**:
```javascript
/**
 * 负载测试
 */
const autocannon = require('autocannon');
const { expect } = require('chai');

describe('性能测试', () => {
  const baseUrl = 'http://localhost:3000';
  
  /**
   * 测试API响应时间
   */
  it('API响应时间应该在可接受范围内', async () => {
    const result = await autocannon({
      url: `${baseUrl}/api/health`,
      connections: 10,
      duration: 10, // 10秒
      pipelining: 1
    });
    
    console.log('性能测试结果:', {
      平均响应时间: `${result.latency.average}ms`,
      最大响应时间: `${result.latency.max}ms`,
      每秒请求数: result.requests.average,
      总请求数: result.requests.total,
      错误数: result.errors
    });
    
    // 断言性能指标
    expect(result.latency.average).to.be.below(100); // 平均响应时间小于100ms
    expect(result.requests.average).to.be.above(50); // 每秒处理超过50个请求
    expect(result.errors).to.equal(0); // 无错误
  });
  
  /**
   * 测试并发用户登录
   */
  it('应该能处理并发用户登录', async () => {
    const result = await autocannon({
      url: `${baseUrl}/api/user/login`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: 'test_code',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      }),
      connections: 20,
      duration: 15
    });
    
    expect(result.latency.average).to.be.below(200);
    expect(result.errors).to.be.below(result.requests.total * 0.01); // 错误率小于1%
  });
  
  /**
   * 测试数据库查询性能
   */
  it('数据库查询性能应该满足要求', async () => {
    const result = await autocannon({
      url: `${baseUrl}/api/products?page=1&limit=20`,
      connections: 15,
      duration: 10
    });
    
    expect(result.latency.average).to.be.below(150);
    expect(result.requests.average).to.be.above(30);
  });
});
```

### 2. 内存泄漏测试

**创建 `test/performance/memory.test.js` 文件**:
```javascript
/**
 * 内存泄漏测试
 */
const { expect } = require('chai');
const request = require('supertest');
const app = require('../../app');

describe('内存泄漏测试', () => {
  /**
   * 测试长时间运行是否存在内存泄漏
   */
  it('长时间运行不应该有内存泄漏', async function() {
    this.timeout(60000); // 设置60秒超时
    
    const initialMemory = process.memoryUsage();
    console.log('初始内存使用:', initialMemory);
    
    // 模拟大量请求
    const requests = [];
    for (let i = 0; i < 1000; i++) {
      requests.push(
        request(app)
          .get('/api/products')
          .expect(200)
      );
      
      // 每100个请求检查一次内存
      if (i % 100 === 0) {
        await Promise.all(requests.splice(0, 100));
        
        // 强制垃圾回收（需要启动时添加 --expose-gc 参数）
        if (global.gc) {
          global.gc();
        }
        
        const currentMemory = process.memoryUsage();
        console.log(`第${i}次请求后内存使用:`, currentMemory);
        
        // 检查内存增长是否过快
        const memoryGrowth = currentMemory.heapUsed - initialMemory.heapUsed;
        expect(memoryGrowth).to.be.below(50 * 1024 * 1024); // 内存增长不超过50MB
      }
    }
    
    // 处理剩余请求
    await Promise.all(requests);
    
    // 最终内存检查
    if (global.gc) {
      global.gc();
    }
    
    const finalMemory = process.memoryUsage();
    console.log('最终内存使用:', finalMemory);
    
    const totalGrowth = finalMemory.heapUsed - initialMemory.heapUsed;
    expect(totalGrowth).to.be.below(100 * 1024 * 1024); // 总内存增长不超过100MB
  });
});
```

## 安全测试

### 1. 认证授权测试

**创建 `test/security/auth.test.js` 文件**:
```javascript
/**
 * 认证授权安全测试
 */
const request = require('supertest');
const { expect } = require('chai');
const app = require('../../app');

describe('认证授权安全测试', () => {
  /**
   * 测试未授权访问
   */
  it('应该拒绝未授权的API访问', async () => {
    const protectedEndpoints = [
      '/api/user/profile',
      '/api/favorites',
      '/api/community/posts'
    ];
    
    for (const endpoint of protectedEndpoints) {
      const response = await request(app)
        .get(endpoint)
        .expect(401);
      
      expect(response.body.code).to.equal(401);
    }
  });
  
  /**
   * 测试无效token
   */
  it('应该拒绝无效token的访问', async () => {
    const invalidTokens = [
      'invalid_token',
      'Bearer invalid_token',
      'Bearer ',
      ''
    ];
    
    for (const token of invalidTokens) {
      const response = await request(app)
        .get('/api/user/profile')
        .set('Authorization', token)
        .expect(401);
      
      expect(response.body.code).to.equal(401);
    }
  });
  
  /**
   * 测试过期token
   */
  it('应该拒绝过期token的访问', async () => {
    // 这里需要生成一个过期的token进行测试
    const expiredToken = 'Bearer expired_token_here';
    
    const response = await request(app)
      .get('/api/user/profile')
      .set('Authorization', expiredToken)
      .expect(401);
    
    expect(response.body.message).to.include('过期');
  });
});
```

### 2. 输入验证测试

**创建 `test/security/validation.test.js` 文件**:
```javascript
/**
 * 输入验证安全测试
 */
const request = require('supertest');
const { expect } = require('chai');
const app = require('../../app');

describe('输入验证安全测试', () => {
  let authToken;
  
  before(async () => {
    // 获取有效的认证token
    const loginResponse = await request(app)
      .post('/api/user/login')
      .send({
        code: 'test_code',
        userInfo: {
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      });
    
    authToken = loginResponse.body.data.token;
  });
  
  /**
   * 测试SQL注入攻击
   */
  it('应该防止SQL注入攻击', async () => {
    const maliciousInputs = [
      "'; DROP TABLE users; --",
      "1' OR '1'='1",
      "admin'--",
      "1; DELETE FROM users WHERE 1=1; --"
    ];
    
    for (const input of maliciousInputs) {
      const response = await request(app)
        .get(`/api/products?search=${encodeURIComponent(input)}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      // 应该正常处理，不应该导致数据库错误
      expect(response.status).to.not.equal(500);
    }
  });
  
  /**
   * 测试XSS攻击防护
   */
  it('应该防止XSS攻击', async () => {
    const xssPayloads = [
      '<script>alert("XSS")</script>',
      'javascript:alert("XSS")',
      '<img src=x onerror=alert("XSS")>',
      '"><script>alert("XSS")</script>'
    ];
    
    for (const payload of xssPayloads) {
      const response = await request(app)
        .post('/api/community/posts')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: payload,
          content: `测试内容 ${payload}`
        });
      
      if (response.status === 200) {
        // 检查返回的内容是否被正确转义
        expect(response.body.data.title).to.not.include('<script>');
        expect(response.body.data.content).to.not.include('<script>');
      }
    }
  });
  
  /**
   * 测试参数长度限制
   */
  it('应该限制输入参数长度', async () => {
    const longString = 'a'.repeat(10000); // 10KB的字符串
    
    const response = await request(app)
      .post('/api/community/posts')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        title: longString,
        content: longString
      })
      .expect(400);
    
    expect(response.body.message).to.include('长度');
  });
});
```

## 用户验收测试

### 1. 功能验收测试清单

#### 1.1 首页功能

**测试用例清单**:

| 测试项目 | 测试步骤 | 预期结果 | 状态 |
|---------|---------|---------|------|
| 页面加载 | 打开小程序首页 | 页面正常显示，轮播图、分类、推荐商家加载完成 | ⬜ |
| 轮播图切换 | 手动滑动轮播图 | 轮播图正常切换，指示器同步更新 | ⬜ |
| 分类点击 | 点击任意分类图标 | 跳转到对应的商品列表页 | ⬜ |
| 商家点击 | 点击推荐商家卡片 | 生成推广链接并跳转到美团小程序 | ⬜ |
| 下拉刷新 | 在首页下拉 | 页面刷新，重新加载数据 | ⬜ |
| 搜索功能 | 点击搜索框 | 跳转到搜索页面 | ⬜ |

#### 1.2 商品列表页功能

| 测试项目 | 测试步骤 | 预期结果 | 状态 |
|---------|---------|---------|------|
| 列表加载 | 进入商品列表页 | 商品列表正常显示 | ⬜ |
| 搜索功能 | 输入关键词搜索 | 显示相关搜索结果 | ⬜ |
| 筛选功能 | 点击筛选条件 | 列表根据筛选条件更新 | ⬜ |
| 上拉加载 | 滑动到列表底部 | 自动加载更多商品 | ⬜ |
| 收藏功能 | 点击收藏按钮 | 商品收藏状态切换 | ⬜ |
| 分享功能 | 点击分享按钮 | 调起分享面板 | ⬜ |
| 商品跳转 | 点击商品卡片 | 跳转到美团小程序对应页面 | ⬜ |

#### 1.3 社区页面功能

| 测试项目 | 测试步骤 | 预期结果 | 状态 |
|---------|---------|---------|------|
| 内容列表 | 进入社区页面 | 显示用户发布的内容列表 | ⬜ |
| 发布内容 | 点击发布按钮 | 进入内容发布页面 | ⬜ |
| 图片上传 | 选择图片发布 | 图片正常上传并显示 | ⬜ |
| 点赞功能 | 点击点赞按钮 | 点赞数增加，按钮状态改变 | ⬜ |
| 评论功能 | 点击评论按钮 | 进入评论页面，可以发表评论 | ⬜ |
| 内容详情 | 点击内容卡片 | 进入内容详情页 | ⬜ |

#### 1.4 个人中心功能

| 测试项目 | 测试步骤 | 预期结果 | 状态 |
|---------|---------|---------|------|
| 用户信息 | 进入个人中心 | 显示用户头像、昵称等信息 | ⬜ |
| 登录功能 | 点击登录按钮 | 调起微信授权，完成登录 | ⬜ |
| 收藏列表 | 点击我的收藏 | 显示用户收藏的商家列表 | ⬜ |
| 浏览记录 | 点击浏览记录 | 显示用户浏览过的商家 | ⬜ |
| 我的发布 | 点击我的发布 | 显示用户发布的社区内容 | ⬜ |
| 设置页面 | 点击设置 | 进入设置页面 | ⬜ |

### 2. 兼容性测试

#### 2.1 设备兼容性

**测试设备清单**:

| 设备类型 | 具体型号 | 屏幕尺寸 | 测试状态 |
|---------|---------|---------|----------|
| iPhone | iPhone 12 Pro | 6.1英寸 | ⬜ |
| iPhone | iPhone SE | 4.7英寸 | ⬜ |
| Android | 华为 P40 | 6.1英寸 | ⬜ |
| Android | 小米 10 | 6.67英寸 | ⬜ |
| Android | OPPO A5 | 6.5英寸 | ⬜ |

#### 2.2 微信版本兼容性

| 微信版本 | 测试状态 | 备注 |
|---------|----------|------|
| 8.0.x | ⬜ | 最新版本 |
| 7.0.x | ⬜ | 主流版本 |
| 6.7.x | ⬜ | 最低支持版本 |

### 3. 用户体验测试

#### 3.1 页面加载性能

**性能指标要求**:

| 指标 | 目标值 | 测试结果 |
|------|--------|----------|
| 首页加载时间 | < 2秒 | ⬜ |
| 列表页加载时间 | < 1.5秒 | ⬜ |
| 图片加载时间 | < 3秒 | ⬜ |
| API响应时间 | < 500ms | ⬜ |

#### 3.2 交互体验

**体验测试项目**:

| 测试项目 | 评估标准 | 测试结果 |
|---------|----------|----------|
| 页面切换流畅度 | 无卡顿，动画自然 | ⬜ |
| 按钮响应速度 | 点击即时响应 | ⬜ |
| 滚动性能 | 滚动流畅，无掉帧 | ⬜ |
| 错误提示 | 提示信息清晰易懂 | ⬜ |
| 加载状态 | 有明确的加载指示 | ⬜ |

## 自动化测试

### 1. 持续集成配置

**创建 `.github/workflows/test.yml` 文件**:
```yaml
# GitHub Actions 自动化测试配置
name: 自动化测试

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [14.x, 16.x, 18.x]
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
    
    - name: 设置 Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: 安装依赖
      run: |
        npm ci
        cd test && npm ci
    
    - name: 等待MySQL启动
      run: |
        while ! mysqladmin ping -h"127.0.0.1" -P3306 -uroot -ptest_password --silent; do
          sleep 1
        done
    
    - name: 初始化测试数据库
      run: |
        mysql -h127.0.0.1 -P3306 -uroot -ptest_password -e "CREATE DATABASE IF NOT EXISTS test_db;"
    
    - name: 运行单元测试
      run: npm run test:unit
      env:
        NODE_ENV: test
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_USER: root
        DB_PASSWORD: test_password
        DB_NAME: test_db
    
    - name: 运行集成测试
      run: npm run test:integration
      env:
        NODE_ENV: test
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_USER: root
        DB_PASSWORD: test_password
        DB_NAME: test_db
    
    - name: 运行性能测试
      run: npm run test:performance
    
    - name: 生成测试报告
      run: npm run test:coverage
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
```

### 2. 测试脚本配置

**在 `package.json` 中添加测试脚本**:
```json
{
  "scripts": {
    "test": "npm run test:unit && npm run test:integration",
    "test:unit": "mocha test/unit/**/*.test.js --timeout 10000",
    "test:integration": "mocha test/integration/**/*.test.js --timeout 30000",
    "test:performance": "mocha test/performance/**/*.test.js --timeout 60000",
    "test:security": "mocha test/security/**/*.test.js --timeout 20000",
    "test:coverage": "nyc --reporter=html --reporter=lcov npm run test",
    "test:watch": "mocha test/**/*.test.js --watch",
    "test:frontend": "cd test && npm run test"
  },
  "nyc": {
    "exclude": [
      "test/**",
      "coverage/**",
      "node_modules/**"
    ],
    "reporter": [
      "text",
      "html",
      "lcov"
    ],
    "check-coverage": true,
    "lines": 80,
    "functions": 80,
    "branches": 70,
    "statements": 80
  }
}
```

## 测试报告

### 1. 测试报告模板

**创建测试报告生成脚本 `scripts/generate-test-report.js`**:
```javascript
/**
 * 测试报告生成脚本
 */
const fs = require('fs');
const path = require('path');

/**
 * 生成测试报告
 */
function generateTestReport() {
  const reportData = {
    projectName: '美团联盟小程序',
    testDate: new Date().toISOString().split('T')[0],
    testEnvironment: process.env.NODE_ENV || 'test',
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      coverage: {
        lines: 0,
        functions: 0,
        branches: 0,
        statements: 0
      }
    },
    testSuites: [
      {
        name: '前端功能测试',
        tests: [],
        status: 'passed'
      },
      {
        name: '后端API测试',
        tests: [],
        status: 'passed'
      },
      {
        name: '集成测试',
        tests: [],
        status: 'passed'
      },
      {
        name: '性能测试',
        tests: [],
        status: 'passed'
      },
      {
        name: '安全测试',
        tests: [],
        status: 'passed'
      }
    ],
    issues: [],
    recommendations: []
  };
  
  const reportHtml = generateHtmlReport(reportData);
  
  // 保存报告
  const reportPath = path.join(__dirname, '../reports/test-report.html');
  fs.mkdirSync(path.dirname(reportPath), { recursive: true });
  fs.writeFileSync(reportPath, reportHtml);
  
  console.log(`测试报告已生成: ${reportPath}`);
}

/**
 * 生成HTML格式的测试报告
 */
function generateHtmlReport(data) {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.projectName} - 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .test-suite { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .issue { background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${data.projectName} - 测试报告</h1>
        <p>测试日期: ${data.testDate}</p>
        <p>测试环境: ${data.testEnvironment}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>总测试数</h3>
            <p>${data.summary.totalTests}</p>
        </div>
        <div class="metric">
            <h3 class="passed">通过</h3>
            <p>${data.summary.passedTests}</p>
        </div>
        <div class="metric">
            <h3 class="failed">失败</h3>
            <p>${data.summary.failedTests}</p>
        </div>
        <div class="metric">
            <h3 class="skipped">跳过</h3>
            <p>${data.summary.skippedTests}</p>
        </div>
    </div>
    
    <div class="coverage">
        <h2>代码覆盖率</h2>
        <p>行覆盖率: ${data.summary.coverage.lines}%</p>
        <p>函数覆盖率: ${data.summary.coverage.functions}%</p>
        <p>分支覆盖率: ${data.summary.coverage.branches}%</p>
        <p>语句覆盖率: ${data.summary.coverage.statements}%</p>
    </div>
    
    <div class="test-suites">
        <h2>测试套件</h2>
        ${data.testSuites.map(suite => `
            <div class="test-suite">
                <h3>${suite.name} - <span class="${suite.status}">${suite.status}</span></h3>
                <p>测试用例数: ${suite.tests.length}</p>
            </div>
        `).join('')}
    </div>
    
    ${data.issues.length > 0 ? `
        <div class="issues">
            <h2>发现的问题</h2>
            ${data.issues.map(issue => `
                <div class="issue">
                    <h4>${issue.title}</h4>
                    <p>${issue.description}</p>
                </div>
            `).join('')}
        </div>
    ` : ''}
    
    <div class="recommendations">
        <h2>改进建议</h2>
        <ul>
            ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
        </ul>
    </div>
</body>
</html>
  `;
}

if (require.main === module) {
  generateTestReport();
}

module.exports = { generateTestReport };
```

### 2. 测试执行指南

#### 2.1 本地测试执行

```bash
# 1. 安装测试依赖
npm install
cd test && npm install

# 2. 启动测试数据库
docker run -d --name test-mysql \
  -e MYSQL_ROOT_PASSWORD=test_password \
  -e MYSQL_DATABASE=test_db \
  -p 3306:3306 mysql:8.0

# 3. 运行所有测试
npm test

# 4. 运行特定类型的测试
npm run test:unit        # 单元测试
npm run test:integration # 集成测试
npm run test:performance # 性能测试
npm run test:security    # 安全测试

# 5. 生成覆盖率报告
npm run test:coverage

# 6. 生成测试报告
node scripts/generate-test-report.js
```

#### 2.2 CI/CD环境测试

测试将在以下情况自动触发:
- 代码推送到 main 或 develop 分支
- 创建 Pull Request
- 定时任务（每日构建）

### 3. 测试结果分析

#### 3.1 关键指标

- **测试通过率**: 目标 > 95%
- **代码覆盖率**: 目标 > 80%
- **性能指标**: API响应时间 < 500ms
- **安全测试**: 无高危漏洞

#### 3.2 问题处理流程

1. **测试失败**: 立即通知开发团队
2. **覆盖率不足**: 补充测试用例
3. **性能问题**: 进行性能优化
4. **安全问题**: 立即修复并重新测试

## 总结

本测试指南涵盖了美团联盟小程序的完整测试流程，包括:

1. **前端测试**: 页面功能、组件测试
2. **后端测试**: API接口、业务逻辑测试
3. **集成测试**: 端到端功能验证
4. **性能测试**: 负载测试、内存泄漏检测
5. **安全测试**: 认证授权、输入验证
6. **用户验收测试**: 功能清单、兼容性测试
7. **自动化测试**: CI/CD集成、持续测试

通过执行这些测试，可以确保小程序的质量、性能和安全性，为用户提供稳定可靠的服务体验。