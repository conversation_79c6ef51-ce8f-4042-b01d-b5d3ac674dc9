# 版本控制指南

## 概述

本文档详细说明了美团联盟小程序项目的版本控制规范和最佳实践，包括Git工作流程、分支管理策略、提交规范等内容。

## Git工作流程

### 分支策略

项目采用Git Flow分支模型，包含以下分支类型：

**主要分支**:
- `main`: 生产环境分支，包含稳定的发布版本
- `develop`: 开发分支，包含最新的开发功能

**辅助分支**:
- `feature/*`: 功能开发分支
- `release/*`: 发布准备分支
- `hotfix/*`: 紧急修复分支

### 分支命名规范

```bash
# 功能分支
feature/user-authentication
feature/product-search
feature/community-posts

# 发布分支
release/v1.2.0
release/v1.3.0

# 修复分支
hotfix/fix-login-bug
hotfix/fix-api-timeout
```

### 工作流程

**1. 功能开发流程**:
```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 开发功能
git add .
git commit -m "feat: 添加新功能"

# 推送到远程仓库
git push origin feature/new-feature

# 创建Pull Request到develop分支
# 代码审查通过后合并
```

**2. 发布流程**:
```bash
# 从develop分支创建发布分支
git checkout develop
git pull origin develop
git checkout -b release/v1.2.0

# 更新版本号和发布说明
npm version minor
git add .
git commit -m "chore: 发布v1.2.0版本"

# 合并到main分支
git checkout main
git merge release/v1.2.0
git tag v1.2.0
git push origin main --tags

# 合并回develop分支
git checkout develop
git merge release/v1.2.0
git push origin develop

# 删除发布分支
git branch -d release/v1.2.0
git push origin --delete release/v1.2.0
```

**3. 紧急修复流程**:
```bash
# 从main分支创建修复分支
git checkout main
git pull origin main
git checkout -b hotfix/fix-critical-bug

# 修复问题
git add .
git commit -m "fix: 修复关键问题"

# 合并到main分支
git checkout main
git merge hotfix/fix-critical-bug
git tag v1.2.1
git push origin main --tags

# 合并到develop分支
git checkout develop
git merge hotfix/fix-critical-bug
git push origin develop

# 删除修复分支
git branch -d hotfix/fix-critical-bug
git push origin --delete hotfix/fix-critical-bug
```

## 提交规范

### 提交消息格式

采用[Conventional Commits](https://www.conventionalcommits.org/)规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 提交类型

- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整（不影响功能）
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动
- `ci`: CI/CD相关
- `build`: 构建系统或外部依赖的变动

### 提交示例

```bash
# 新功能
git commit -m "feat(auth): 添加微信登录功能"

# 修复问题
git commit -m "fix(api): 修复商品列表接口超时问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 性能优化
git commit -m "perf(cache): 优化Redis缓存策略"

# 重构代码
git commit -m "refactor(utils): 重构工具函数"

# 测试
git commit -m "test(auth): 添加登录功能单元测试"

# 构建相关
git commit -m "chore: 更新依赖包版本"
```

### 详细提交示例

```bash
git commit -m "feat(community): 添加社区帖子点赞功能

- 添加点赞API接口
- 实现前端点赞交互
- 添加点赞数量显示
- 支持取消点赞操作

Closes #123"
```

## 代码审查

### Pull Request规范

**PR标题格式**:
```
[类型] 简短描述

示例:
[Feature] 添加用户认证功能
[Fix] 修复商品搜索问题
[Docs] 更新部署文档
```

**PR描述模板**:
```markdown
## 变更类型
- [ ] 新功能
- [ ] 问题修复
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能优化
- [ ] 测试相关

## 变更描述
简要描述本次变更的内容和目的。

## 测试说明
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成

## 相关Issue
Closes #123
Related to #456

## 截图（如适用）
![screenshot](url)

## 检查清单
- [ ] 代码符合项目规范
- [ ] 已添加必要的测试
- [ ] 文档已更新
- [ ] 无破坏性变更
```

### 代码审查清单

**功能性检查**:
- [ ] 功能是否按预期工作
- [ ] 是否处理了边界情况
- [ ] 错误处理是否完善
- [ ] 性能是否可接受

**代码质量检查**:
- [ ] 代码是否易读易懂
- [ ] 是否遵循项目编码规范
- [ ] 是否有重复代码
- [ ] 变量和函数命名是否合理

**安全性检查**:
- [ ] 是否存在安全漏洞
- [ ] 输入验证是否充分
- [ ] 敏感信息是否泄露
- [ ] 权限控制是否正确

**测试检查**:
- [ ] 是否有足够的测试覆盖
- [ ] 测试用例是否合理
- [ ] 是否测试了异常情况

## 版本管理

### 版本号规范

采用[语义化版本](https://semver.org/)规范：`MAJOR.MINOR.PATCH`

- `MAJOR`: 不兼容的API变更
- `MINOR`: 向后兼容的功能新增
- `PATCH`: 向后兼容的问题修复

### 版本发布流程

**1. 准备发布**:
```bash
# 更新版本号
npm version minor  # 或 major/patch

# 更新CHANGELOG.md
echo "## [1.2.0] - $(date +%Y-%m-%d)" >> CHANGELOG.md
echo "### Added" >> CHANGELOG.md
echo "- 新功能描述" >> CHANGELOG.md
echo "### Fixed" >> CHANGELOG.md
echo "- 修复问题描述" >> CHANGELOG.md
```

**2. 创建发布标签**:
```bash
# 创建带注释的标签
git tag -a v1.2.0 -m "发布v1.2.0版本

新功能:
- 添加社区功能
- 优化商品搜索

修复问题:
- 修复登录问题
- 修复页面加载问题"

# 推送标签
git push origin v1.2.0
```

**3. 发布到生产环境**:
```bash
# 部署到生产环境
./deploy.sh v1.2.0

# 创建GitHub Release
# 在GitHub上创建Release，包含发布说明和变更日志
```

### 变更日志

**CHANGELOG.md格式**:
```markdown
# 变更日志

## [1.2.0] - 2024-01-15

### Added
- 添加社区帖子功能
- 添加用户收藏功能
- 添加商品分类筛选

### Changed
- 优化首页加载性能
- 改进搜索算法

### Fixed
- 修复登录状态丢失问题
- 修复图片加载失败问题

### Security
- 修复XSS安全漏洞

## [1.1.0] - 2024-01-01

### Added
- 添加用户认证功能
- 添加商品搜索功能

### Fixed
- 修复页面跳转问题
```

## Git配置

### 全局配置

```bash
# 设置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 设置默认编辑器
git config --global core.editor "code --wait"

# 设置默认分支名
git config --global init.defaultBranch main

# 启用颜色输出
git config --global color.ui auto

# 设置换行符处理
git config --global core.autocrlf input  # Linux/Mac
git config --global core.autocrlf true   # Windows
```

### 项目配置

```bash
# 设置远程仓库
git remote add origin https://github.com/username/meituan-alliance.git

# 设置上游分支
git branch --set-upstream-to=origin/main main
git branch --set-upstream-to=origin/develop develop

# 配置Git钩子
cp .githooks/* .git/hooks/
chmod +x .git/hooks/*
```

### Git别名

```bash
# 常用别名
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'

# 高级别名
git config --global alias.lg "log --color --graph --pretty=format:'%Cred%h%Creset -%C(yellow)%d%Creset %s %Cgreen(%cr) %C(bold blue)<%an>%Creset' --abbrev-commit"
git config --global alias.conflicts "diff --name-only --diff-filter=U"
```

## Git钩子

### 提交前检查

**pre-commit钩子**:
```bash
#!/bin/sh
# .githooks/pre-commit

echo "运行pre-commit检查..."

# 检查代码格式
npm run lint
if [ $? -ne 0 ]; then
  echo "代码格式检查失败，请修复后重新提交"
  exit 1
fi

# 运行测试
npm test
if [ $? -ne 0 ]; then
  echo "测试失败，请修复后重新提交"
  exit 1
fi

echo "pre-commit检查通过"
```

**commit-msg钩子**:
```bash
#!/bin/sh
# .githooks/commit-msg

commit_regex='^(feat|fix|docs|style|refactor|perf|test|chore|ci|build)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "提交消息格式不正确！"
    echo "格式应为: type(scope): description"
    echo "例如: feat(auth): 添加登录功能"
    exit 1
fi
```

### 推送前检查

**pre-push钩子**:
```bash
#!/bin/sh
# .githooks/pre-push

echo "运行pre-push检查..."

# 检查是否在main分支直接推送
current_branch=$(git symbolic-ref HEAD | sed -e 's,.*/\(.*\),\1,')
if [ "$current_branch" = "main" ]; then
    echo "不允许直接推送到main分支！"
    echo "请通过Pull Request合并代码"
    exit 1
fi

# 运行完整测试套件
npm run test:full
if [ $? -ne 0 ]; then
  echo "完整测试失败，请修复后重新推送"
  exit 1
fi

echo "pre-push检查通过"
```

## 常用Git命令

### 基础操作

```bash
# 查看状态
git status

# 查看差异
git diff
git diff --staged
git diff HEAD~1

# 添加文件
git add .
git add -A
git add -p  # 交互式添加

# 提交
git commit -m "提交消息"
git commit --amend  # 修改最后一次提交

# 查看历史
git log
git log --oneline
git log --graph
git log -p  # 显示差异
```

### 分支操作

```bash
# 创建分支
git branch feature/new-feature
git checkout -b feature/new-feature

# 切换分支
git checkout develop
git switch develop  # Git 2.23+

# 合并分支
git merge feature/new-feature
git merge --no-ff feature/new-feature  # 保留分支历史

# 删除分支
git branch -d feature/new-feature
git push origin --delete feature/new-feature

# 查看分支
git branch
git branch -r  # 远程分支
git branch -a  # 所有分支
```

### 远程操作

```bash
# 获取更新
git fetch
git pull
git pull --rebase

# 推送
git push
git push origin feature/new-feature
git push --set-upstream origin feature/new-feature

# 查看远程仓库
git remote -v
git remote show origin
```

### 撤销操作

```bash
# 撤销工作区修改
git checkout -- file.txt
git restore file.txt  # Git 2.23+

# 撤销暂存区修改
git reset HEAD file.txt
git restore --staged file.txt  # Git 2.23+

# 撤销提交
git reset --soft HEAD~1  # 保留修改
git reset --hard HEAD~1  # 丢弃修改
git revert HEAD  # 创建新提交撤销

# 修改提交
git commit --amend
git rebase -i HEAD~3  # 交互式变基
```

## 故障排查

### 常见问题

**1. 合并冲突**:
```bash
# 查看冲突文件
git status
git diff

# 解决冲突后
git add .
git commit

# 或者中止合并
git merge --abort
```

**2. 误删分支**:
```bash
# 查找删除的分支
git reflog

# 恢复分支
git checkout -b recovered-branch <commit-hash>
```

**3. 推送被拒绝**:
```bash
# 先拉取远程更新
git pull --rebase

# 解决冲突后推送
git push
```

**4. 提交历史混乱**:
```bash
# 交互式变基整理提交
git rebase -i HEAD~5

# 压缩提交
# 在编辑器中将pick改为squash
```

### 恢复操作

```bash
# 查看操作历史
git reflog

# 恢复到特定状态
git reset --hard HEAD@{2}

# 恢复删除的文件
git checkout HEAD~1 -- deleted-file.txt

# 恢复删除的提交
git cherry-pick <commit-hash>
```

## 最佳实践

### 提交频率

- 频繁提交小的、逻辑完整的变更
- 每个提交只包含一个逻辑变更
- 提交前确保代码可以正常运行
- 避免提交调试代码和临时文件

### 分支管理

- 保持分支名称简洁明了
- 及时删除已合并的分支
- 定期同步develop分支
- 避免长期存在的功能分支

### 代码审查

- 所有代码变更都需要经过审查
- 审查者应该仔细检查代码质量
- 及时响应审查意见
- 保持友好的审查氛围

### 文档维护

- 及时更新相关文档
- 保持CHANGELOG.md的准确性
- 为重要功能编写详细说明
- 定期检查文档的时效性

## 工具推荐

### Git客户端

- **命令行**: Git Bash, Zsh with Oh My Zsh
- **图形界面**: GitKraken, SourceTree, GitHub Desktop
- **IDE集成**: VS Code Git, IntelliJ Git

### 辅助工具

- **Commitizen**: 规范化提交消息
- **Husky**: Git钩子管理
- **lint-staged**: 暂存文件检查
- **conventional-changelog**: 自动生成变更日志

### 配置示例

**package.json配置**:
```json
{
  "scripts": {
    "commit": "cz",
    "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"
  },
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
```

## 总结

良好的版本控制实践是项目成功的关键因素之一。通过遵循本指南中的规范和最佳实践，团队可以：

1. 保持代码历史的清晰和可追溯性
2. 提高代码质量和团队协作效率
3. 减少合并冲突和部署风险
4. 建立可靠的发布流程

建议团队成员定期回顾和更新这些规范，以适应项目的发展需要。