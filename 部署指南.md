# 美团联盟小程序部署指南

## 目录
- [环境要求](#环境要求)
- [前端部署](#前端部署)
- [后端部署](#后端部署)
- [数据库配置](#数据库配置)
- [域名配置](#域名配置)
- [SSL证书配置](#ssl证书配置)
- [监控与日志](#监控与日志)
- [性能优化](#性能优化)
- [故障排查](#故障排查)

## 环境要求

### 服务器配置

**最低配置**:
- CPU: 2核
- 内存: 4GB
- 存储: 40GB SSD
- 带宽: 5Mbps

**推荐配置**:
- CPU: 4核
- 内存: 8GB
- 存储: 100GB SSD
- 带宽: 10Mbps

### 软件环境

- **操作系统**: Ubuntu 20.04 LTS / CentOS 8
- **Node.js**: v16.x 或更高版本
- **数据库**: MySQL 8.0 / PostgreSQL 13
- **Web服务器**: Nginx 1.18+
- **进程管理**: PM2
- **版本控制**: Git

## 前端部署

### 1. 小程序发布

#### 1.1 代码准备

```bash
# 克隆项目代码
git clone https://github.com/your-repo/meituan-alliance-miniprogram.git
cd meituan-alliance-miniprogram

# 安装依赖（如果有）
npm install
```

#### 1.2 配置文件修改

**修改 `app.js` 配置**:
```javascript
/**
 * 全局配置
 */
App({
  globalData: {
    // 生产环境API地址
    apiBaseUrl: 'https://your-domain.com/api',
    // 美团联盟配置
    meituanConfig: {
      appKey: 'your_production_app_key',
      // 注意：不要在前端暴露appSecret
    },
    // 版本信息
    version: '1.0.0'
  },
  
  /**
   * 小程序初始化完成
   */
  onLaunch: function () {
    console.log('小程序启动，版本:', this.globalData.version);
    
    // 检查更新
    this.checkForUpdate();
  },
  
  /**
   * 检查小程序更新
   */
  checkForUpdate: function() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate(function (res) {
        console.log('检查更新结果:', res.hasUpdate);
      });
      
      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: function (res) {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
    }
  }
});
```

**修改 `app.json` 配置**:
```json
{
  "pages": [
    "pages/index/index",
    "pages/products/products",
    "pages/community/community",
    "pages/topic/topic",
    "pages/profile/profile"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "美团联盟",
    "navigationBarTextStyle": "black"
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "images/icon_home.png",
        "selectedIconPath": "images/icon_home_selected.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/products/products",
        "iconPath": "images/icon_products.png",
        "selectedIconPath": "images/icon_products_selected.png",
        "text": "商品"
      },
      {
        "pagePath": "pages/community/community",
        "iconPath": "images/icon_community.png",
        "selectedIconPath": "images/icon_community_selected.png",
        "text": "社区"
      },
      {
        "pagePath": "pages/profile/profile",
        "iconPath": "images/icon_profile.png",
        "selectedIconPath": "images/icon_profile_selected.png",
        "text": "我的"
      }
    ]
  },
  "permission": {
    "scope.userLocation": {
      "desc": "你的位置信息将用于小程序位置接口的效果展示"
    }
  },
  "requiredBackgroundModes": ["audio"],
  "networkTimeout": {
    "request": 10000,
    "downloadFile": 10000
  }
}
```

#### 1.3 微信开发者工具发布

1. **打开微信开发者工具**
2. **导入项目**
   - 选择项目目录
   - 填入正式的AppID
   - 项目名称：美团联盟小程序

3. **代码检查**
   ```bash
   # 检查代码规范
   # 确保没有console.log等调试代码
   # 检查图片资源大小
   # 验证所有页面功能
   ```

4. **上传代码**
   - 点击"上传"按钮
   - 填写版本号和项目备注
   - 确认上传

5. **提交审核**
   - 登录微信公众平台
   - 进入小程序管理后台
   - 选择开发管理 > 开发版本
   - 点击"提交审核"
   - 填写审核信息

#### 1.4 审核注意事项

**功能描述要点**:
- 明确说明是美团联盟推广小程序
- 详细描述核心功能
- 提供测试账号（如需要）

**常见审核问题**:
- 缺少用户协议和隐私政策
- 功能描述不清晰
- 存在诱导分享行为
- 缺少必要的资质证明

## 后端部署

### 1. 服务器环境搭建

#### 1.1 系统更新

```bash
# Ubuntu系统
sudo apt update && sudo apt upgrade -y

# CentOS系统
sudo yum update -y
```

#### 1.2 安装Node.js

```bash
# 使用NodeSource仓库安装Node.js 16.x
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 1.3 安装PM2

```bash
# 全局安装PM2
npm install -g pm2

# 设置PM2开机自启
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

#### 1.4 安装Nginx

```bash
# Ubuntu
sudo apt install nginx -y

# CentOS
sudo yum install nginx -y

# 启动并设置开机自启
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 后端代码部署

#### 2.1 创建项目目录

```bash
# 创建应用目录
sudo mkdir -p /var/www/meituan-alliance-api
sudo chown $USER:$USER /var/www/meituan-alliance-api
cd /var/www/meituan-alliance-api
```

#### 2.2 克隆代码

```bash
# 克隆后端代码
git clone https://github.com/your-repo/meituan-alliance-api.git .

# 安装依赖
npm install --production
```

#### 2.3 环境配置

**创建 `.env` 文件**:
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=meituan_alliance
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# 美团联盟API配置
MEITUAN_APP_KEY=your_meituan_app_key
MEITUAN_APP_SECRET=your_meituan_app_secret

# 微信小程序配置
WX_APP_ID=your_wx_app_id
WX_APP_SECRET=your_wx_app_secret

# 服务配置
PORT=3000
NODE_ENV=production

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/meituan-alliance/app.log

# Redis配置（如使用）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

#### 2.4 PM2配置

**创建 `ecosystem.config.js` 文件**:
```javascript
/**
 * PM2配置文件
 */
module.exports = {
  apps: [{
    name: 'meituan-alliance-api',
    script: './app.js',
    instances: 'max', // 使用所有CPU核心
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    // 日志配置
    log_file: '/var/log/meituan-alliance/combined.log',
    out_file: '/var/log/meituan-alliance/out.log',
    error_file: '/var/log/meituan-alliance/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // 自动重启配置
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    max_memory_restart: '1G',
    
    // 健康检查
    min_uptime: '10s',
    max_restarts: 10,
    
    // 环境变量文件
    env_file: '.env'
  }]
};
```

#### 2.5 启动应用

```bash
# 创建日志目录
sudo mkdir -p /var/log/meituan-alliance
sudo chown $USER:$USER /var/log/meituan-alliance

# 使用PM2启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 查看应用状态
pm2 status
pm2 logs meituan-alliance-api
```

### 3. Nginx配置

#### 3.1 创建Nginx配置文件

**创建 `/etc/nginx/sites-available/meituan-alliance` 文件**:
```nginx
# 美团联盟API服务器配置
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志配置
    access_log /var/log/nginx/meituan-alliance.access.log;
    error_log /var/log/nginx/meituan-alliance.error.log;
    
    # 限制请求大小
    client_max_body_size 10M;
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:3000/health;
        access_log off;
    }
    
    # 静态文件（如果有）
    location /static/ {
        alias /var/www/meituan-alliance-api/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 防止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log)$ {
        deny all;
    }
}
```

#### 3.2 启用配置

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/meituan-alliance /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 数据库配置

### 1. MySQL安装与配置

#### 1.1 安装MySQL

```bash
# Ubuntu
sudo apt install mysql-server -y

# CentOS
sudo yum install mysql-server -y

# 启动MySQL
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### 1.2 安全配置

```bash
# 运行安全配置脚本
sudo mysql_secure_installation

# 按提示设置：
# - 设置root密码
# - 删除匿名用户
# - 禁止root远程登录
# - 删除test数据库
# - 重新加载权限表
```

#### 1.3 创建数据库和用户

```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE meituan_alliance CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'meituan_user'@'localhost' IDENTIFIED BY 'your_strong_password';

-- 授权
GRANT ALL PRIVILEGES ON meituan_alliance.* TO 'meituan_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

#### 1.4 导入数据库结构

```bash
# 导入数据库结构文件
mysql -u meituan_user -p meituan_alliance < database/schema.sql

# 导入初始数据（如果有）
mysql -u meituan_user -p meituan_alliance < database/data.sql
```

### 2. 数据库优化配置

**编辑 `/etc/mysql/mysql.conf.d/mysqld.cnf`**:
```ini
[mysqld]
# 基础配置
port = 3306
bind-address = 127.0.0.1

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 内存配置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M

# 连接配置
max_connections = 200
max_connect_errors = 10

# 查询缓存
query_cache_type = 1
query_cache_size = 128M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
```

```bash
# 重启MySQL应用配置
sudo systemctl restart mysql
```

## 域名配置

### 1. DNS配置

在域名服务商处添加以下DNS记录：

```
# A记录
your-domain.com.        A    your_server_ip
www.your-domain.com.    A    your_server_ip

# CNAME记录（可选）
api.your-domain.com.    CNAME    your-domain.com.
```

### 2. 微信小程序域名配置

1. **登录微信公众平台**
2. **进入小程序管理后台**
3. **开发 > 开发管理 > 开发设置**
4. **配置服务器域名**：
   - request合法域名：`https://your-domain.com`
   - uploadFile合法域名：`https://your-domain.com`
   - downloadFile合法域名：`https://your-domain.com`

## SSL证书配置

### 1. 使用Let's Encrypt免费证书

#### 1.1 安装Certbot

```bash
# Ubuntu
sudo apt install certbot python3-certbot-nginx -y

# CentOS
sudo yum install certbot python3-certbot-nginx -y
```

#### 1.2 获取证书

```bash
# 自动配置Nginx
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 或手动获取证书
sudo certbot certonly --nginx -d your-domain.com -d www.your-domain.com
```

#### 1.3 自动续期

```bash
# 测试自动续期
sudo certbot renew --dry-run

# 添加定时任务
sudo crontab -e

# 添加以下行（每天检查续期）
0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 使用商业SSL证书

如果使用商业SSL证书，将证书文件放置在安全位置：

```bash
# 创建证书目录
sudo mkdir -p /etc/ssl/certs
sudo mkdir -p /etc/ssl/private

# 复制证书文件
sudo cp your-domain.crt /etc/ssl/certs/
sudo cp your-domain.key /etc/ssl/private/

# 设置权限
sudo chmod 644 /etc/ssl/certs/your-domain.crt
sudo chmod 600 /etc/ssl/private/your-domain.key
```

## 监控与日志

### 1. 应用监控

#### 1.1 PM2监控

```bash
# 实时监控
pm2 monit

# 查看日志
pm2 logs meituan-alliance-api

# 查看应用信息
pm2 show meituan-alliance-api

# 重启应用
pm2 restart meituan-alliance-api
```

#### 1.2 系统监控脚本

**创建 `/opt/monitor.sh` 脚本**:
```bash
#!/bin/bash

# 系统监控脚本
LOG_FILE="/var/log/system-monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查磁盘使用率
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$DATE] WARNING: Disk usage is ${DISK_USAGE}%" >> $LOG_FILE
fi

# 检查内存使用率
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEM_USAGE -gt 80 ]; then
    echo "[$DATE] WARNING: Memory usage is ${MEM_USAGE}%" >> $LOG_FILE
fi

# 检查应用状态
if ! pm2 list | grep -q "meituan-alliance-api.*online"; then
    echo "[$DATE] ERROR: Application is not running" >> $LOG_FILE
    pm2 restart meituan-alliance-api
fi

# 检查Nginx状态
if ! systemctl is-active --quiet nginx; then
    echo "[$DATE] ERROR: Nginx is not running" >> $LOG_FILE
    systemctl start nginx
fi

# 检查MySQL状态
if ! systemctl is-active --quiet mysql; then
    echo "[$DATE] ERROR: MySQL is not running" >> $LOG_FILE
    systemctl start mysql
fi
```

```bash
# 设置执行权限
sudo chmod +x /opt/monitor.sh

# 添加定时任务（每5分钟执行一次）
sudo crontab -e
*/5 * * * * /opt/monitor.sh
```

### 2. 日志管理

#### 2.1 日志轮转配置

**创建 `/etc/logrotate.d/meituan-alliance` 文件**:
```
/var/log/meituan-alliance/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}
```

#### 2.2 日志分析脚本

**创建 `/opt/log-analysis.sh` 脚本**:
```bash
#!/bin/bash

# 日志分析脚本
LOG_DIR="/var/log/meituan-alliance"
REPORT_FILE="/tmp/daily-report.txt"
DATE=$(date '+%Y-%m-%d')

echo "=== 美团联盟API日志分析报告 - $DATE ===" > $REPORT_FILE
echo "" >> $REPORT_FILE

# 错误统计
echo "错误统计:" >> $REPORT_FILE
grep -c "ERROR" $LOG_DIR/error.log >> $REPORT_FILE
echo "" >> $REPORT_FILE

# API调用统计
echo "API调用统计:" >> $REPORT_FILE
grep "POST\|GET\|PUT\|DELETE" $LOG_DIR/out.log | \
    awk '{print $4}' | sort | uniq -c | sort -nr >> $REPORT_FILE
echo "" >> $REPORT_FILE

# 响应时间统计
echo "平均响应时间:" >> $REPORT_FILE
grep "Response time" $LOG_DIR/out.log | \
    awk '{sum+=$NF; count++} END {print sum/count "ms"}' >> $REPORT_FILE

# 发送报告（可选，需要配置邮件服务）
# mail -s "美团联盟API日志报告 - $DATE" <EMAIL> < $REPORT_FILE
```

## 性能优化

### 1. 应用层优化

#### 1.1 Node.js优化

**在 `app.js` 中添加性能优化配置**:
```javascript
/**
 * 性能优化配置
 */
const express = require('express');
const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

const app = express();

// 启用Gzip压缩
app.use(compression());

// 安全中间件
app.use(helmet());

// 请求频率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试'
});
app.use('/api/', limiter);

// 静态文件缓存
app.use(express.static('public', {
  maxAge: '1y',
  etag: true
}));
```

#### 1.2 数据库连接池优化

```javascript
/**
 * 数据库连接池配置
 */
const mysql = require('mysql2/promise');

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
});

module.exports = pool;
```

### 2. 缓存优化

#### 2.1 Redis缓存配置

```bash
# 安装Redis
sudo apt install redis-server -y

# 启动Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**Redis配置优化 `/etc/redis/redis.conf`**:
```
# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 网络配置
bind 127.0.0.1
port 6379
timeout 300
```

#### 2.2 应用缓存实现

```javascript
/**
 * Redis缓存工具
 */
const redis = require('redis');
const client = redis.createClient({
  host: process.env.REDIS_HOST,
  port: process.env.REDIS_PORT,
  password: process.env.REDIS_PASSWORD
});

/**
 * 缓存中间件
 * @param {number} duration - 缓存时间（秒）
 */
function cache(duration = 300) {
  return async (req, res, next) => {
    const key = `cache:${req.originalUrl}`;
    
    try {
      const cached = await client.get(key);
      if (cached) {
        return res.json(JSON.parse(cached));
      }
      
      // 重写res.json方法以缓存响应
      const originalJson = res.json;
      res.json = function(data) {
        client.setex(key, duration, JSON.stringify(data));
        return originalJson.call(this, data);
      };
      
      next();
    } catch (error) {
      console.error('缓存错误:', error);
      next();
    }
  };
}

module.exports = { cache };
```

## 故障排查

### 1. 常见问题诊断

#### 1.1 应用无法启动

```bash
# 检查应用状态
pm2 status

# 查看错误日志
pm2 logs meituan-alliance-api --err

# 检查端口占用
sudo netstat -tlnp | grep :3000

# 检查环境变量
pm2 env meituan-alliance-api
```

#### 1.2 数据库连接问题

```bash
# 测试数据库连接
mysql -u meituan_user -p -h localhost meituan_alliance

# 检查MySQL状态
sudo systemctl status mysql

# 查看MySQL错误日志
sudo tail -f /var/log/mysql/error.log
```

#### 1.3 Nginx配置问题

```bash
# 测试Nginx配置
sudo nginx -t

# 重新加载配置
sudo nginx -s reload

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

### 2. 性能问题排查

#### 2.1 系统资源监控

```bash
# 查看系统负载
top
htop

# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看网络连接
ss -tuln
```

#### 2.2 应用性能分析

```bash
# PM2性能监控
pm2 monit

# 查看应用内存使用
pm2 show meituan-alliance-api

# 生成性能报告
npm run profile  # 需要在package.json中配置
```

### 3. 故障恢复流程

#### 3.1 应用故障恢复

```bash
#!/bin/bash
# 应用故障恢复脚本

echo "开始应用故障恢复..."

# 停止应用
pm2 stop meituan-alliance-api

# 拉取最新代码
cd /var/www/meituan-alliance-api
git pull origin main

# 安装依赖
npm install --production

# 重启应用
pm2 restart meituan-alliance-api

# 检查状态
pm2 status

echo "应用故障恢复完成"
```

#### 3.2 数据库故障恢复

```bash
#!/bin/bash
# 数据库故障恢复脚本

echo "开始数据库故障恢复..."

# 停止应用
pm2 stop meituan-alliance-api

# 重启MySQL
sudo systemctl restart mysql

# 等待MySQL启动
sleep 10

# 检查数据库连接
mysql -u meituan_user -p -e "SELECT 1;"

if [ $? -eq 0 ]; then
    echo "数据库连接正常"
    # 重启应用
    pm2 start meituan-alliance-api
else
    echo "数据库连接失败，需要手动检查"
fi

echo "数据库故障恢复完成"
```

---

**部署检查清单**:

- [ ] 服务器环境配置完成
- [ ] 域名DNS解析正确
- [ ] SSL证书配置成功
- [ ] 数据库创建并导入数据
- [ ] 后端应用启动正常
- [ ] Nginx代理配置正确
- [ ] 小程序域名配置完成
- [ ] 监控和日志配置完成
- [ ] 备份策略制定完成
- [ ] 故障恢复流程测试完成

**维护联系方式**:
- 技术支持：<EMAIL>
- 紧急联系：+86-xxx-xxxx-xxxx
- 文档更新：2024年1月
- 版本：v1.0.0