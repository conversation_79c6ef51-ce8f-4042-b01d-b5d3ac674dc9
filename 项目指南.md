# 美团联盟小程序项目指南

## 项目概述

美团联盟小程序是基于Python + SQLite技术栈的推广应用，支持商品展示、用户管理、内容社区和广告系统。

### 技术架构
- **前端**: 微信小程序原生开发
- **后端**: Python 3.9+ + FastAPI
- **数据库**: SQLite
- **配置**: JSON文件
- **认证**: JWT Token

### 核心功能
- 商品展示和推广链接生成
- 用户登录和信息管理
- 内容社区和收藏功能
- JSON配置的广告系统
- 数据统计和分析

## 快速开始

### 环境要求
- Python 3.9+
- 微信开发者工具
- 美团联盟账号
- 微信小程序账号

### 后端部署

1. **安装依赖**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

2. **配置环境**
```bash
cp .env.example .env
# 编辑.env文件配置API密钥
```

3. **初始化数据库**
```bash
python scripts/init_db.py
```

4. **启动服务**
```bash
uvicorn app.main:app --reload
```

### 前端配置

1. **导入项目**
   - 打开微信开发者工具
   - 导入项目目录
   - 配置小程序AppID

2. **配置API地址**
   - 编辑 `utils/config.js`
   - 设置后端API地址

3. **配置域名**
   - 在小程序后台配置服务器域名
   - 添加request合法域名

## 项目结构

```
zhang_mt/
├── app/                    # Python后端
│   ├── main.py            # FastAPI入口
│   ├── config.py          # 配置管理
│   ├── api/               # API路由
│   ├── models/            # 数据模型
│   ├── services/          # 业务逻辑
│   └── utils/             # 工具函数
├── data/                  # 数据文件
│   ├── database.db        # SQLite数据库
│   ├── ads_config.json    # 广告配置
│   └── logs/              # 日志文件
├── pages/                 # 小程序页面
│   ├── index/             # 首页
│   ├── products/          # 商品页
│   ├── community/         # 社区页
│   └── profile/           # 个人中心
├── utils/                 # 小程序工具
│   └── config.js          # 配置文件
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量示例
└── README.md             # 项目说明
```

## 核心API

### 用户认证
```
POST /api/auth/login       # 微信登录
POST /api/auth/refresh     # 刷新Token
```

### 用户管理
```
GET  /api/users/profile    # 获取用户信息
PUT  /api/users/profile    # 更新用户信息
GET  /api/users/stats      # 用户统计数据
```

### 商品管理
```
GET  /api/products/list    # 商品列表
GET  /api/products/{id}    # 商品详情
POST /api/products/{id}/promotion-link  # 生成推广链接
```

### 收藏管理
```
GET    /api/favorites/     # 收藏列表
POST   /api/favorites/     # 添加收藏
DELETE /api/favorites/{id} # 取消收藏
```

### 广告系统
```
GET  /api/ads/             # 获取广告
POST /api/ads/{id}/impression  # 记录展示
POST /api/ads/{id}/click       # 记录点击
```

## 配置文件

### 环境变量 (.env)
```env
# 数据库
DATABASE_PATH=data/database.db

# JWT认证
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 美团联盟
MEITUAN_APP_KEY=your_app_key
MEITUAN_APP_SECRET=your_app_secret

# 微信小程序
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret
```

### 小程序配置 (utils/config.js)
```javascript
const config = {
  apiBase: 'http://localhost:8000',
  meituan: {
    appKey: 'your_meituan_app_key'
  },
  wechat: {
    appId: 'your_wechat_app_id'
  }
};
```

### 广告配置 (data/ads_config.json)
```json
{
  "ads_config": {
    "ads": [
      {
        "id": "banner_001",
        "type": "banner",
        "position": "home_top",
        "title": "广告标题",
        "image_url": "图片地址",
        "link_url": "跳转地址",
        "status": "active"
      }
    ]
  }
}
```

## 部署指南

### 开发环境
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 生产环境
```bash
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Docker部署
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 常见问题

### Q1: 数据库连接失败
检查DATABASE_PATH配置和data目录权限

### Q2: 美团联盟API调用失败
验证MEITUAN_APP_KEY和MEITUAN_APP_SECRET配置

### Q3: 小程序无法调用API
检查小程序后台域名配置和HTTPS证书

### Q4: 广告不显示
检查ads_config.json文件格式和广告状态

## 开发规范

### 代码规范
- Python代码遵循PEP8规范
- 小程序代码使用ESLint检查
- 统一使用UTF-8编码

### API规范
- 统一返回格式: `{code, message, data}`
- 使用HTTP状态码
- 错误信息要明确具体

### 配置规范
- 敏感信息使用环境变量
- 配置文件使用JSON格式
- 配置项要有默认值

## 更新日志

### v1.0.0
- 初始版本发布
- 实现核心功能
- Python + SQLite架构
- JSON配置系统
