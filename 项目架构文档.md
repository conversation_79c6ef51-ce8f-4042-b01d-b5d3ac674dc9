# 美团联盟小程序项目架构文档

## 目录

- [项目概述](#项目概述)
- [整体架构](#整体架构)
- [前端架构](#前端架构)
- [后端架构](#后端架构)
- [数据库设计](#数据库设计)
- [API设计](#api设计)
- [安全架构](#安全架构)
- [性能优化](#性能优化)
- [监控体系](#监控体系)
- [部署架构](#部署架构)

## 项目概述

美团联盟小程序是一个基于微信小程序平台的推广应用，通过集成美团联盟API实现商品推广和佣金分成。项目采用前后端分离架构，前端使用微信小程序原生开发，后端使用Python + FastAPI构建高性能RESTful API服务，数据存储采用轻量级的SQLite数据库。

### 核心功能模块

- **商品展示**: 通过美团联盟API获取推广商品信息
- **内容聚合**: 社区内容、专题页面等UGC内容
- **用户管理**: 用户注册、登录、个人信息管理
- **数据统计**: 推广效果统计、用户行为分析

## 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │  Python API服务  │    │   美团联盟API    │
│                │    │                │    │                │
│  - 首页        │◄──►│  - 用户管理     │◄──►│  - 推广物料     │
│  - 商品列表     │    │  - 内容管理     │    │  - 转链服务     │
│  - 社区页面     │    │  - 数据统计     │    │  - 订单查询     │
│  - 个人中心     │    │  - 广告服务     │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信服务器     │    │   数据存储      │    │   第三方服务     │
│                │    │                │    │                │
│  - 用户授权     │    │  - SQLite      │    │  - 阿里云OSS    │
│  - 消息推送     │    │  - JSON配置     │    │  - 短信服务     │
│  - 支付服务     │    │  - 日志文件     │    │  - 邮件服务     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈选择

**前端技术栈**:
- 微信小程序原生框架
- WXML + WXSS + JavaScript
- 微信小程序API

**后端技术栈**:
- Python 3.9+ (主要开发语言)
- FastAPI (现代化Web框架)
- SQLite (轻量级数据库)
- SQLAlchemy (ORM框架)
- Pydantic (数据验证)
- JWT (身份认证)
- uvicorn (ASGI服务器)

**基础设施**:
- Nginx (反向代理 + 负载均衡)
- Docker (容器化部署)
- Gunicorn (生产环境WSGI服务器)
- 阿里云/腾讯云 (云服务)

## 前端架构

### 目录结构

```
小程序项目/
├── pages/                 # 页面目录
│   ├── index/             # 首页
│   ├── products/          # 商品列表页
│   ├── community/         # 社区页面
│   ├── topic/             # 专题页面
│   └── profile/           # 个人中心
├── components/            # 自定义组件
│   ├── product-card/      # 商品卡片组件
│   ├── user-avatar/       # 用户头像组件
│   └── loading/           # 加载组件
├── utils/                 # 工具函数
│   ├── api.js            # API请求封装
│   ├── auth.js           # 认证相关
│   ├── storage.js        # 本地存储
│   └── common.js         # 通用工具
├── styles/               # 全局样式
├── images/               # 图片资源
├── app.js               # 应用入口
├── app.json             # 应用配置
└── app.wxss             # 全局样式
```

### 组件化设计

**基础组件**:
```javascript
// components/product-card/product-card.js
Component({
  /**
   * 商品卡片组件
   * 用于展示商品信息，支持点击跳转
   */
  properties: {
    product: {
      type: Object,
      value: {}
    },
    showPrice: {
      type: Boolean,
      value: true
    }
  },

  methods: {
    /**
     * 处理商品点击事件
     */
    onProductTap() {
      const { product } = this.data;

      // 触发父组件事件
      this.triggerEvent('productTap', {
        product: product
      });

      // 统计点击事件
      this.recordClickEvent(product.id);
    },

    /**
     * 记录点击事件
     */
    recordClickEvent(productId) {
      wx.request({
        url: `${app.globalData.apiBase}/api/statistics/click`,
        method: 'POST',
        data: {
          productId: productId,
          timestamp: Date.now()
        }
      });
    }
  }
});
```

### 状态管理

**全局状态管理**:
```javascript
// utils/store.js
/**
 * 简单的状态管理器
 * 用于管理全局状态和数据共享
 */
class Store {
  constructor() {
    this.state = {
      user: null,
      cart: [],
      favorites: [],
      settings: {}
    };
    this.listeners = [];
  }

  /**
   * 获取状态
   */
  getState() {
    return this.state;
  }

  /**
   * 更新状态
   */
  setState(newState) {
    this.state = { ...this.state, ...newState };
    this.notify();
  }

  /**
   * 订阅状态变化
   */
  subscribe(listener) {
    this.listeners.push(listener);

    // 返回取消订阅函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有监听器
   */
  notify() {
    this.listeners.forEach(listener => listener(this.state));
  }
}

const store = new Store();
module.exports = store;
```

### API请求封装

```javascript
// utils/api.js
/**
 * API请求封装
 * 统一处理请求和响应，包含认证、错误处理等
 */
class ApiClient {
  constructor() {
    this.baseURL = getApp().globalData.apiBase;
    this.timeout = 10000;
  }

  /**
   * 通用请求方法
   */
  async request(options) {
    const {
      url,
      method = 'GET',
      data = {},
      needAuth = true
    } = options;

    // 构建请求头
    const header = {
      'Content-Type': 'application/json'
    };

    // 添加认证token
    if (needAuth) {
      const token = wx.getStorageSync('token');
      if (token) {
        header['Authorization'] = `Bearer ${token}`;
      }
    }

    try {
      const response = await this.wxRequest({
        url: `${this.baseURL}${url}`,
        method,
        data,
        header,
        timeout: this.timeout
      });

      return this.handleResponse(response);

    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 微信请求Promise封装
   */
  wxRequest(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...options,
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 处理响应
   */
  handleResponse(response) {
    const { statusCode, data } = response;

    if (statusCode === 200) {
      if (data.code === 0) {
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message };
      }
    } else if (statusCode === 401) {
      // token过期，跳转登录
      this.redirectToLogin();
      return { success: false, error: '登录已过期' };
    } else {
      return { success: false, error: '网络请求失败' };
    }
  }

  /**
   * 处理错误
   */
  handleError(error) {
    console.error('API请求错误:', error);
    return { success: false, error: '网络连接失败' };
  }

  /**
   * 跳转到登录页面
   */
  redirectToLogin() {
    wx.removeStorageSync('token');
    wx.redirectTo({
      url: '/pages/login/login'
    });
  }

  // API方法定义

  /**
   * 获取商品列表
   */
  async getProducts(params) {
    return this.request({
      url: '/api/products',
      method: 'GET',
      data: params
    });
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    return this.request({
      url: '/api/user/info',
      method: 'GET'
    });
  }

  /**
   * 用户登录
   */
  async login(code) {
    return this.request({
      url: '/api/auth/login',
      method: 'POST',
      data: { code },
      needAuth: false
    });
  }
}

const apiClient = new ApiClient();
module.exports = apiClient;
```

## 后端架构

### Python项目结构

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用入口
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接和配置
│   ├── models/              # SQLAlchemy数据模型
│   │   ├── __init__.py
│   │   ├── user.py         # 用户模型
│   │   ├── content.py      # 内容模型
│   │   ├── favorite.py     # 收藏模型
│   │   └── statistics.py   # 统计模型
│   ├── schemas/             # Pydantic数据模式
│   │   ├── __init__.py
│   │   ├── user.py         # 用户数据模式
│   │   ├── content.py      # 内容数据模式
│   │   ├── response.py     # 响应数据模式
│   │   └── request.py      # 请求数据模式
│   ├── api/                 # API路由
│   │   ├── __init__.py
│   │   ├── auth.py         # 认证相关API
│   │   ├── users.py        # 用户管理API
│   │   ├── products.py     # 商品相关API
│   │   ├── content.py      # 内容管理API
│   │   ├── favorites.py    # 收藏管理API
│   │   └── ads.py          # 广告管理API
│   ├── services/            # 业务逻辑服务
│   │   ├── __init__.py
│   │   ├── auth_service.py # 认证服务
│   │   ├── user_service.py # 用户服务
│   │   ├── meituan_service.py # 美团联盟服务
│   │   ├── ad_service.py   # 广告服务
│   │   └── content_service.py # 内容服务
│   ├── utils/               # 工具函数
│   │   ├── __init__.py
│   │   ├── security.py     # 安全相关工具
│   │   ├── logger.py       # 日志工具
│   │   ├── helpers.py      # 通用辅助函数
│   │   ├── exceptions.py   # 异常处理
│   │   └── error_codes.py  # 错误码定义
│   └── middleware/          # 中间件
│       ├── __init__.py
│       ├── auth.py         # 认证中间件
│       ├── cors.py         # 跨域中间件
│       └── logging.py      # 日志中间件
├── data/
│   ├── database.db          # SQLite数据库文件
│   ├── ads_config.json      # 广告配置文件
│   └── logs/               # 日志文件目录
├── tests/                   # 测试文件
│   ├── __init__.py
│   ├── test_auth.py
│   ├── test_users.py
│   └── test_products.py
├── scripts/                 # 脚本文件
│   ├── init_db.py          # 数据库初始化
│   ├── backup_db.py        # 数据库备份
│   └── migrate.py          # 数据迁移
├── requirements.txt         # Python依赖
├── Dockerfile              # Docker配置
├── docker-compose.yml      # Docker Compose配置
├── .env.example            # 环境变量示例
├── .gitignore             # Git忽略文件
└── README.md              # 项目说明
```

### 分层架构设计

**控制器层 (Controllers)**:
```javascript
// src/controllers/product.js
/**
 * 商品控制器
 * 处理商品相关的HTTP请求
 */
const productService = require('../services/productService');
const { validationResult } = require('express-validator');

class ProductController {
  /**
   * 获取商品列表
   */
  async getProducts(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          code: 400,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const { page = 1, limit = 20, category, keyword } = req.query;

      // 调用服务层
      const result = await productService.getProducts({
        page: parseInt(page),
        limit: parseInt(limit),
        category,
        keyword
      });

      res.json({
        code: 0,
        message: 'success',
        data: result
      });

    } catch (error) {
      console.error('获取商品列表失败:', error);
      res.status(500).json({
        code: 500,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 获取商品详情
   */
  async getProductDetail(req, res) {
    try {
      const { id } = req.params;

      const product = await productService.getProductById(id);

      if (!product) {
        return res.status(404).json({
          code: 404,
          message: '商品不存在'
        });
      }

      res.json({
        code: 0,
        message: 'success',
        data: product
      });

    } catch (error) {
      console.error('获取商品详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 生成推广链接
   */
  async generatePromotionLink(req, res) {
    try {
      const { productId } = req.body;
      const userId = req.user.id;

      const promotionLink = await productService.generatePromotionLink({
        productId,
        userId
      });

      res.json({
        code: 0,
        message: 'success',
        data: {
          promotionLink
        }
      });

    } catch (error) {
      console.error('生成推广链接失败:', error);
      res.status(500).json({
        code: 500,
        message: '生成推广链接失败'
      });
    }
  }
}

module.exports = new ProductController();
```

**服务层 (Services)**:
```javascript
// src/services/productService.js
/**
 * 商品服务层
 * 处理商品相关的业务逻辑
 */
const meituanService = require('./meituanService');
const redisClient = require('../utils/redis');
const logger = require('../utils/logger');

class ProductService {
  /**
   * 获取商品列表
   */
  async getProducts(params) {
    const { page, limit, category, keyword } = params;

    // 构建缓存key
    const cacheKey = `products:${page}:${limit}:${category || ''}:${keyword || ''}`;

    try {
      // 尝试从缓存获取
      const cachedData = await redisClient.get(cacheKey);
      if (cachedData) {
        logger.info('从缓存获取商品列表');
        return JSON.parse(cachedData);
      }

      // 从美团联盟API获取数据
      const products = await meituanService.getPromotionProducts({
        page,
        limit,
        category,
        keyword
      });

      // 数据处理和格式化
      const formattedProducts = this.formatProducts(products);

      const result = {
        list: formattedProducts,
        pagination: {
          page,
          limit,
          total: products.total || 0
        }
      };

      // 缓存结果（5分钟）
      await redisClient.setex(cacheKey, 300, JSON.stringify(result));

      return result;

    } catch (error) {
      logger.error('获取商品列表失败:', error);
      throw error;
    }
  }

  /**
   * 格式化商品数据
   */
  formatProducts(products) {
    return products.map(product => ({
      id: product.id,
      title: product.title,
      image: product.image,
      price: product.price,
      originalPrice: product.originalPrice,
      commission: product.commission,
      commissionRate: product.commissionRate,
      sales: product.sales,
      rating: product.rating,
      shop: {
        name: product.shopName,
        logo: product.shopLogo
      },
      tags: product.tags || [],
      category: product.category
    }));
  }

  /**
   * 获取商品详情
   */
  async getProductById(productId) {
    const cacheKey = `product:${productId}`;

    try {
      // 尝试从缓存获取
      const cachedData = await redisClient.get(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData);
      }

      // 从美团联盟API获取详情
      const product = await meituanService.getProductDetail(productId);

      if (product) {
        // 缓存详情（10分钟）
        await redisClient.setex(cacheKey, 600, JSON.stringify(product));
      }

      return product;

    } catch (error) {
      logger.error('获取商品详情失败:', error);
      throw error;
    }
  }

  /**
   * 生成推广链接
   */
  async generatePromotionLink(params) {
    const { productId, userId } = params;

    try {
      // 调用美团联盟转链API
      const promotionLink = await meituanService.generatePromotionLink({
        productId,
        userId
      });

      // 记录推广链接生成日志
      logger.info('生成推广链接', {
        productId,
        userId,
        promotionLink
      });

      return promotionLink;

    } catch (error) {
      logger.error('生成推广链接失败:', error);
      throw error;
    }
  }
}

module.exports = new ProductService();
```

**美团联盟服务**:
```javascript
// src/services/meituanService.js
/**
 * 美团联盟API服务
 * 封装美团联盟API调用
 */
const crypto = require('crypto');
const axios = require('axios');
const config = require('../config/app');
const logger = require('../utils/logger');

class MeituanService {
  constructor() {
    this.appKey = config.meituan.appKey;
    this.appSecret = config.meituan.appSecret;
    this.baseURL = config.meituan.baseURL;
  }

  /**
   * 生成API签名
   */
  generateSignature(params) {
    // 按key排序
    const sortedKeys = Object.keys(params).sort();

    // 构建签名字符串
    let signString = '';
    sortedKeys.forEach(key => {
      signString += `${key}${params[key]}`;
    });

    // 添加app_secret
    signString = this.appSecret + signString + this.appSecret;

    // MD5加密并转大写
    return crypto.createHash('md5').update(signString).digest('hex').toUpperCase();
  }

  /**
   * 发送API请求
   */
  async sendRequest(method, params) {
    const requestParams = {
      app_key: this.appKey,
      method,
      timestamp: Math.floor(Date.now() / 1000),
      v: '1.0',
      format: 'json',
      ...params
    };

    // 生成签名
    requestParams.sign = this.generateSignature(requestParams);

    try {
      const response = await axios.post(this.baseURL, requestParams, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      if (response.data.code === 0) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '美团联盟API调用失败');
      }

    } catch (error) {
      logger.error('美团联盟API请求失败:', {
        method,
        params,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取推广商品
   */
  async getPromotionProducts(params) {
    const { page = 1, limit = 20, category, keyword } = params;

    const apiParams = {
      page,
      limit,
      ...(category && { category }),
      ...(keyword && { keyword })
    };

    return this.sendRequest('meituan.promotion.products.get', apiParams);
  }

  /**
   * 获取商品详情
   */
  async getProductDetail(productId) {
    return this.sendRequest('meituan.product.detail.get', {
      product_id: productId
    });
  }

  /**
   * 生成推广链接
   */
  async generatePromotionLink(params) {
    const { productId, userId } = params;

    const result = await this.sendRequest('meituan.promotion.link.generate', {
      product_id: productId,
      user_id: userId
    });

    return result.promotion_link;
  }

  /**
   * 获取订单数据
   */
  async getOrderData(params) {
    const { startDate, endDate, page = 1, limit = 100 } = params;

    return this.sendRequest('meituan.order.data.get', {
      start_date: startDate,
      end_date: endDate,
      page,
      limit
    });
  }
}

module.exports = new MeituanService();
```

## 数据库设计

### SQLite数据库架构

采用SQLite作为数据存储方案，具有以下优势：
- **轻量级**: 无需独立的数据库服务器
- **零配置**: 开箱即用，简化部署
- **高性能**: 对于中小型应用性能优异
- **可靠性**: 支持ACID事务
- **跨平台**: 支持多种操作系统

### 核心表结构

**用户表 (users)**:
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    openid VARCHAR(100) UNIQUE NOT NULL,
    unionid VARCHAR(100),
    nickname VARCHAR(100),
    avatar VARCHAR(500),
    phone VARCHAR(20),
    gender INTEGER DEFAULT 0,  -- 0:未知, 1:男, 2:女
    city VARCHAR(50),
    province VARCHAR(50),
    country VARCHAR(50),
    status BOOLEAN DEFAULT 1,  -- 0:禁用, 1:正常
    total_commission REAL DEFAULT 0.0,
    total_orders INTEGER DEFAULT 0,
    last_login_time DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_users_unionid ON users(unionid);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

**内容表 (contents)**:
```sql
CREATE TABLE contents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    type VARCHAR(20) NOT NULL,  -- article, video, image
    title VARCHAR(200) NOT NULL,
    content TEXT,
    images JSON,  -- 图片列表
    video_url VARCHAR(500),
    tags JSON,    -- 标签列表
    category_id INTEGER,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,  -- 0:草稿, 1:已发布, 2:已删除
    is_featured BOOLEAN DEFAULT 0,
    published_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_contents_user_id ON contents(user_id);
CREATE INDEX idx_contents_type ON contents(type);
CREATE INDEX idx_contents_category_id ON contents(category_id);
CREATE INDEX idx_contents_status ON contents(status);
CREATE INDEX idx_contents_published_at ON contents(published_at);
CREATE INDEX idx_contents_is_featured ON contents(is_featured);
```

**收藏表 (favorites)**:
```sql
CREATE TABLE favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    target_type VARCHAR(20) NOT NULL,  -- product, content
    target_id VARCHAR(50) NOT NULL,
    target_data JSON,  -- 目标数据快照
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(user_id, target_type, target_id)
);

-- 创建索引
CREATE INDEX idx_favorites_user_id ON favorites(user_id);
CREATE INDEX idx_favorites_target_type ON favorites(target_type);
CREATE INDEX idx_favorites_target_id ON favorites(target_id);
CREATE INDEX idx_favorites_created_at ON favorites(created_at);
```

**统计表 (statistics)**:
```sql
CREATE TABLE statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    event_type VARCHAR(50) NOT NULL,  -- click, view, share, purchase等
    target_type VARCHAR(20),          -- product, content, ad等
    target_id VARCHAR(50),
    extra_data JSON,                  -- 额外数据
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_statistics_user_id ON statistics(user_id);
CREATE INDEX idx_statistics_event_type ON statistics(event_type);
CREATE INDEX idx_statistics_target ON statistics(target_type, target_id);
CREATE INDEX idx_statistics_created_at ON statistics(created_at);
```

**广告展示记录表 (ad_impressions)**:
```sql
CREATE TABLE ad_impressions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ad_id VARCHAR(50) NOT NULL,
    user_id INTEGER,
    position VARCHAR(50),
    impression_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_ad_impressions_ad_id ON ad_impressions(ad_id);
CREATE INDEX idx_ad_impressions_user_id ON ad_impressions(user_id);
CREATE INDEX idx_ad_impressions_time ON ad_impressions(impression_time);
```

**广告点击记录表 (ad_clicks)**:
```sql
CREATE TABLE ad_clicks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ad_id VARCHAR(50) NOT NULL,
    user_id INTEGER,
    position VARCHAR(50),
    click_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_ad_clicks_ad_id ON ad_clicks(ad_id);
CREATE INDEX idx_ad_clicks_user_id ON ad_clicks(user_id);
CREATE INDEX idx_ad_clicks_time ON ad_clicks(click_time);
```

### SQLite数据库优化策略

**索引优化**:
```sql
-- 为常用查询字段创建索引
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_users_status_created ON users(status, created_at);
CREATE INDEX idx_contents_user_status ON contents(user_id, status);
CREATE INDEX idx_favorites_user_type ON favorites(user_id, target_type);
CREATE INDEX idx_statistics_event_time ON statistics(event_type, created_at);

-- 复合索引优化多字段查询
CREATE INDEX idx_contents_composite ON contents(status, is_featured, published_at DESC);
CREATE INDEX idx_statistics_composite ON statistics(user_id, event_type, created_at);
```

**SQLite性能优化配置**:
```python
# app/database.py
from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
import sqlite3

# SQLite性能优化配置
@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    if isinstance(dbapi_connection, sqlite3.Connection):
        cursor = dbapi_connection.cursor()
        # 启用WAL模式提高并发性能
        cursor.execute("PRAGMA journal_mode=WAL")
        # 设置同步模式为NORMAL提高性能
        cursor.execute("PRAGMA synchronous=NORMAL")
        # 增加缓存大小
        cursor.execute("PRAGMA cache_size=10000")
        # 设置临时存储为内存
        cursor.execute("PRAGMA temp_store=MEMORY")
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()

# 创建数据库引擎
engine = create_engine(
    f"sqlite:///{settings.DATABASE_PATH}",
    connect_args={
        "check_same_thread": False,
        "timeout": 20
    },
    pool_pre_ping=True,
    echo=settings.DEBUG
)
```

**数据库连接池管理**:
```python
# app/utils/db_manager.py
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, engine):
        self.engine = engine
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=engine
        )

    @contextmanager
    def get_db_session(self):
        """获取数据库会话上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()

    async def execute_transaction(self, operations):
        """执行事务操作"""
        with self.get_db_session() as session:
            try:
                results = []
                for operation in operations:
                    result = operation(session)
                    results.append(result)
                return results
            except Exception as e:
                logger.error(f"事务执行失败: {e}")
                raise

    def health_check(self):
        """数据库健康检查"""
        try:
            with self.get_db_session() as session:
                session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False

# 全局数据库管理器实例
db_manager = DatabaseManager(engine)
```

**数据归档策略**:
```python
# scripts/archive_data.py
import sqlite3
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class DataArchiver:
    def __init__(self, db_path: str):
        self.db_path = db_path

    def archive_old_statistics(self, days_to_keep: int = 90):
        """归档旧的统计数据"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 创建归档表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS statistics_archive (
                    id INTEGER PRIMARY KEY,
                    user_id INTEGER,
                    event_type VARCHAR(50),
                    target_type VARCHAR(20),
                    target_id VARCHAR(50),
                    extra_data JSON,
                    ip_address VARCHAR(45),
                    user_agent VARCHAR(500),
                    created_at DATETIME,
                    archived_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 移动旧数据到归档表
            cursor.execute("""
                INSERT INTO statistics_archive
                SELECT *, CURRENT_TIMESTAMP FROM statistics
                WHERE created_at < ?
            """, (cutoff_date,))

            # 删除原表中的旧数据
            cursor.execute("""
                DELETE FROM statistics WHERE created_at < ?
            """, (cutoff_date,))

            archived_count = cursor.rowcount
            conn.commit()

            logger.info(f"归档了 {archived_count} 条统计记录")
            return archived_count

    def cleanup_old_logs(self, days_to_keep: int = 30):
        """清理旧的广告展示和点击记录"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 清理旧的广告展示记录
            cursor.execute("""
                DELETE FROM ad_impressions WHERE impression_time < ?
            """, (cutoff_date,))
            impression_deleted = cursor.rowcount

            # 清理旧的广告点击记录
            cursor.execute("""
                DELETE FROM ad_clicks WHERE click_time < ?
            """, (cutoff_date,))
            click_deleted = cursor.rowcount

            conn.commit()

            logger.info(f"清理了 {impression_deleted} 条广告展示记录和 {click_deleted} 条点击记录")
            return impression_deleted + click_deleted

    def vacuum_database(self):
        """压缩数据库文件"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("VACUUM")
            logger.info("数据库压缩完成")

# 定期执行数据归档
if __name__ == "__main__":
    archiver = DataArchiver("data/database.db")
    archiver.archive_old_statistics(90)
    archiver.cleanup_old_logs(30)
    archiver.vacuum_database()
```
```

## API设计

### RESTful API规范

**URL设计规范**:
```
GET    /api/products              # 获取商品列表
GET    /api/products/:id          # 获取商品详情
POST   /api/products/:id/link     # 生成推广链接

GET    /api/contents              # 获取内容列表
POST   /api/contents              # 创建内容
GET    /api/contents/:id          # 获取内容详情
PUT    /api/contents/:id          # 更新内容
DELETE /api/contents/:id          # 删除内容

GET    /api/user/profile          # 获取用户信息
PUT    /api/user/profile          # 更新用户信息
GET    /api/user/favorites        # 获取收藏列表
POST   /api/user/favorites        # 添加收藏
DELETE /api/user/favorites/:id    # 取消收藏
```

**响应格式规范**:
```javascript
// 成功响应
{
  "code": 0,
  "message": "success",
  "data": {
    // 响应数据
  },
  "timestamp": 1640995200000
}

// 错误响应
{
  "code": 400,
  "message": "参数错误",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ],
  "timestamp": 1640995200000
}

// 分页响应
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  },
  "timestamp": 1640995200000
}
```

### API版本管理

```javascript
// src/routes/index.js
/**
 * API路由管理
 * 支持版本控制
 */
const express = require('express');
const router = express.Router();

// v1 API路由
const v1Routes = require('./v1');
router.use('/v1', v1Routes);

// v2 API路由（未来版本）
// const v2Routes = require('./v2');
// router.use('/v2', v2Routes);

// 默认路由指向最新版本
router.use('/', v1Routes);

module.exports = router;
```

### API文档生成

```javascript
// 使用Swagger生成API文档
/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: 获取商品列表
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 商品分类
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 成功获取商品列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Product'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 */
```

## 安全架构

### 认证授权

**JWT Token认证**:
```javascript
// src/middleware/auth.js
/**
 * JWT认证中间件
 */
const jwt = require('jsonwebtoken');
const config = require('../config/app');
const userService = require('../services/userService');

class AuthMiddleware {
  /**
   * 验证JWT Token
   */
  async verifyToken(req, res, next) {
    try {
      const token = this.extractToken(req);

      if (!token) {
        return res.status(401).json({
          code: 401,
          message: '缺少认证token'
        });
      }

      // 验证token
      const decoded = jwt.verify(token, config.jwt.secret);

      // 检查用户是否存在
      const user = await userService.getUserById(decoded.userId);
      if (!user) {
        return res.status(401).json({
          code: 401,
          message: '用户不存在'
        });
      }

      // 检查用户状态
      if (user.status !== 1) {
        return res.status(401).json({
          code: 401,
          message: '用户已被禁用'
        });
      }

      // 将用户信息添加到请求对象
      req.user = user;
      next();

    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          code: 401,
          message: 'Token已过期'
        });
      } else if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          code: 401,
          message: 'Token无效'
        });
      } else {
        console.error('Token验证错误:', error);
        return res.status(500).json({
          code: 500,
          message: '服务器内部错误'
        });
      }
    }
  }

  /**
   * 提取Token
   */
  extractToken(req) {
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return null;
  }

  /**
   * 可选认证（Token可选）
   */
  async optionalAuth(req, res, next) {
    try {
      const token = this.extractToken(req);

      if (token) {
        const decoded = jwt.verify(token, config.jwt.secret);
        const user = await userService.getUserById(decoded.userId);

        if (user && user.status === 1) {
          req.user = user;
        }
      }

      next();

    } catch (error) {
      // 可选认证失败时继续执行
      next();
    }
  }
}

module.exports = new AuthMiddleware();
```

### 输入验证

```javascript
// src/middleware/validation.js
/**
 * 输入验证中间件
 */
const { body, query, param, validationResult } = require('express-validator');
const xss = require('xss');

class ValidationMiddleware {
  /**
   * 商品列表查询验证
   */
  validateProductQuery() {
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是大于0的整数'),
      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('每页数量必须是1-100之间的整数'),
      query('category')
        .optional()
        .isLength({ max: 50 })
        .withMessage('分类名称不能超过50个字符'),
      query('keyword')
        .optional()
        .isLength({ max: 100 })
        .withMessage('搜索关键词不能超过100个字符')
        .customSanitizer(value => xss(value))
    ];
  }

  /**
   * 内容创建验证
   */
  validateContentCreation() {
    return [
      body('title')
        .notEmpty()
        .withMessage('标题不能为空')
        .isLength({ max: 200 })
        .withMessage('标题不能超过200个字符')
        .customSanitizer(value => xss(value)),
      body('content')
        .notEmpty()
        .withMessage('内容不能为空')
        .isLength({ max: 10000 })
        .withMessage('内容不能超过10000个字符')
        .customSanitizer(value => xss(value)),
      body('type')
        .isIn(['article', 'video', 'image'])
        .withMessage('内容类型无效'),
      body('tags')
        .optional()
        .isArray()
        .withMessage('标签必须是数组')
        .custom(tags => {
          if (tags.length > 10) {
            throw new Error('标签数量不能超过10个');
          }
          return true;
        })
    ];
  }

  /**
   * 处理验证结果
   */
  handleValidationErrors(req, res, next) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    next();
  }

  /**
   * SQL注入检测
   */
  detectSQLInjection(value) {
    const sqlPatterns = [
      /('|(\-\-)|(;)|(\||\|)|(\*|\*))/i,
      /(union|select|insert|delete|update|drop|create|alter|exec|execute)/i
    ];

    return sqlPatterns.some(pattern => pattern.test(value));
  }

  /**
   * 自定义安全验证
   */
  securityValidation(req, res, next) {
    // 检查请求体中的所有字符串字段
    const checkObject = (obj) => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          if (this.detectSQLInjection(obj[key])) {
            return res.status(400).json({
              code: 400,
              message: '检测到潜在的安全威胁'
            });
          }
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          checkObject(obj[key]);
        }
      }
    };

    if (req.body) {
      checkObject(req.body);
    }

    next();
  }
}

module.exports = new ValidationMiddleware();
```

### 限流防护

```javascript
// src/middleware/rateLimit.js
/**
 * 限流中间件
 */
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const redisClient = require('../utils/redis');

class RateLimitMiddleware {
  /**
   * 通用限流
   */
  createRateLimit(options = {}) {
    const {
      windowMs = 15 * 60 * 1000, // 15分钟
      max = 100, // 最大请求次数
      message = '请求过于频繁，请稍后再试',
      keyGenerator = null
    } = options;

    return rateLimit({
      store: new RedisStore({
        client: redisClient,
        prefix: 'rl:'
      }),
      windowMs,
      max,
      message: {
        code: 429,
        message
      },
      keyGenerator: keyGenerator || ((req) => {
        return req.ip + ':' + (req.user?.id || 'anonymous');
      }),
      skip: (req) => {
        // 跳过内部请求
        return req.ip === '127.0.0.1' || req.ip === '::1';
      }
    });
  }

  /**
   * API限流
   */
  apiRateLimit() {
    return this.createRateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 1000, // 1000次请求
      message: 'API请求过于频繁'
    });
  }

  /**
   * 登录限流
   */
  loginRateLimit() {
    return this.createRateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 5, // 5次登录尝试
      message: '登录尝试过于频繁，请15分钟后再试',
      keyGenerator: (req) => {
        return req.ip + ':login';
      }
    });
  }

  /**
   * 内容创建限流
   */
  contentCreationRateLimit() {
    return this.createRateLimit({
      windowMs: 60 * 60 * 1000, // 1小时
      max: 10, // 10篇内容
      message: '内容发布过于频繁，请稍后再试',
      keyGenerator: (req) => {
        return (req.user?.id || req.ip) + ':content';
      }
    });
  }
}

module.exports = new RateLimitMiddleware();
```

## 性能优化

### 缓存策略

**Redis缓存实现**:
```javascript
// src/utils/cache.js
/**
 * 缓存管理器
 * 统一管理Redis缓存操作
 */
const redisClient = require('./redis');
const logger = require('./logger');

class CacheManager {
  constructor() {
    this.defaultTTL = 300; // 5分钟默认过期时间
  }

  /**
   * 获取缓存
   */
  async get(key) {
    try {
      const value = await redisClient.get(key);

      if (value) {
        logger.debug(`缓存命中: ${key}`);
        return JSON.parse(value);
      }

      logger.debug(`缓存未命中: ${key}`);
      return null;

    } catch (error) {
      logger.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置缓存
   */
  async set(key, value, ttl = this.defaultTTL) {
    try {
      const serializedValue = JSON.stringify(value);

      if (ttl > 0) {
        await redisClient.setex(key, ttl, serializedValue);
      } else {
        await redisClient.set(key, serializedValue);
      }

      logger.debug(`缓存设置成功: ${key}, TTL: ${ttl}`);
      return true;

    } catch (error) {
      logger.error('设置缓存失败:', error);
      return false;
    }
  }

  /**
   * 删除缓存
   */
  async del(key) {
    try {
      await redisClient.del(key);
      logger.debug(`缓存删除成功: ${key}`);
      return true;

    } catch (error) {
      logger.error('删除缓存失败:', error);
      return false;
    }
  }

  /**
   * 批量删除缓存
   */
  async delPattern(pattern) {
    try {
      const keys = await redisClient.keys(pattern);

      if (keys.length > 0) {
        await redisClient.del(...keys);
        logger.debug(`批量删除缓存成功: ${pattern}, 数量: ${keys.length}`);
      }

      return keys.length;

    } catch (error) {
      logger.error('批量删除缓存失败:', error);
      return 0;
    }
  }

  /**
   * 缓存装饰器
   */
  cached(keyGenerator, ttl = this.defaultTTL) {
    return (target, propertyName, descriptor) => {
      const originalMethod = descriptor.value;

      descriptor.value = async function(...args) {
        const cacheKey = typeof keyGenerator === 'function'
          ? keyGenerator(...args)
          : keyGenerator;

        // 尝试从缓存获取
        const cachedResult = await this.get(cacheKey);
        if (cachedResult !== null) {
          return cachedResult;
        }

        // 执行原方法
        const result = await originalMethod.apply(this, args);

        // 缓存结果
        if (result !== null && result !== undefined) {
          await this.set(cacheKey, result, ttl);
        }

        return result;
      }.bind(this);

      return descriptor;
    };
  }

  /**
   * 获取缓存统计
   */
  async getStats() {
    try {
      const info = await redisClient.info('memory');
      const keyspace = await redisClient.info('keyspace');

      return {
        memory: this.parseRedisInfo(info),
        keyspace: this.parseRedisInfo(keyspace)
      };

    } catch (error) {
      logger.error('获取缓存统计失败:', error);
      return null;
    }
  }

  /**
   * 解析Redis信息
   */
  parseRedisInfo(info) {
    const lines = info.split('\r\n');
    const result = {};

    lines.forEach(line => {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    });

    return result;
  }
}

module.exports = new CacheManager();
```

### 数据库优化

**连接池优化**:
```javascript
// src/config/database.js
/**
 * 数据库配置
 * 优化连接池参数
 */
module.exports = {
  master: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'meituan_alliance',
    charset: 'utf8mb4',
    timezone: '+08:00',

    // 连接池配置
    connectionLimit: 20,        // 最大连接数
    acquireTimeout: 60000,      // 获取连接超时时间
    timeout: 60000,             // 查询超时时间
    reconnect: true,            // 自动重连

    // 性能优化
    supportBigNumbers: true,
    bigNumberStrings: true,
    dateStrings: false,

    // SSL配置（生产环境）
    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false
  },

  slave: {
    host: process.env.DB_SLAVE_HOST || process.env.DB_HOST || 'localhost',
    port: process.env.DB_SLAVE_PORT || process.env.DB_PORT || 3306,
    user: process.env.DB_SLAVE_USER || process.env.DB_USER || 'root',
    password: process.env.DB_SLAVE_PASSWORD || process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'meituan_alliance',
    charset: 'utf8mb4',
    timezone: '+08:00',

    // 从库连接池配置（读操作较多）
    connectionLimit: 30,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,

    supportBigNumbers: true,
    bigNumberStrings: true,
    dateStrings: false,

    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false
  }
};
```

### 前端性能优化

**图片懒加载组件**:
```javascript
// components/lazy-image/lazy-image.js
/**
 * 图片懒加载组件
 * 优化页面加载性能
 */
Component({
  properties: {
    src: {
      type: String,
      value: ''
    },
    placeholder: {
      type: String,
      value: '/images/placeholder.png'
    },
    width: {
      type: String,
      value: '100%'
    },
    height: {
      type: String,
      value: 'auto'
    }
  },

  data: {
    currentSrc: '',
    loaded: false,
    error: false
  },

  lifetimes: {
    attached() {
      this.setData({
        currentSrc: this.data.placeholder
      });

      // 创建交叉观察器
      this.createIntersectionObserver();
    },

    detached() {
      // 清理观察器
      if (this.observer) {
        this.observer.disconnect();
      }
    }
  },

  methods: {
    /**
     * 创建交叉观察器
     */
    createIntersectionObserver() {
      this.observer = this.createIntersectionObserver({
        rootMargin: '50px'
      });

      this.observer.relativeToViewport().observe('.lazy-image', (res) => {
        if (res.intersectionRatio > 0) {
          this.loadImage();
          this.observer.disconnect();
        }
      });
    },

    /**
     * 加载图片
     */
    loadImage() {
      const { src } = this.data;

      if (!src) return;

      // 预加载图片
      const img = new Image();

      img.onload = () => {
        this.setData({
          currentSrc: src,
          loaded: true
        });
      };

      img.onerror = () => {
        this.setData({
          error: true
        });
      };

      img.src = src;
    },

    /**
     * 图片加载完成事件
     */
    onImageLoad() {
      this.triggerEvent('load');
    },

    /**
     * 图片加载失败事件
     */
    onImageError() {
      this.setData({
        error: true
      });
      this.triggerEvent('error');
    }
  }
});
```

**页面预加载策略**:
```javascript
// utils/preloader.js
/**
 * 页面预加载管理器
 * 提升页面切换体验
 */
class PagePreloader {
  constructor() {
    this.preloadedPages = new Map();
    this.preloadQueue = [];
    this.maxPreloadPages = 3;
  }

  /**
   * 预加载页面
   */
  async preloadPage(url, data = {}) {
    if (this.preloadedPages.has(url)) {
      return this.preloadedPages.get(url);
    }

    try {
      // 预加载页面数据
      const pageData = await this.loadPageData(url, data);

      // 缓存页面数据
      this.preloadedPages.set(url, {
        data: pageData,
        timestamp: Date.now()
      });

      // 清理过期缓存
      this.cleanExpiredCache();

      return pageData;

    } catch (error) {
      console.error('页面预加载失败:', error);
      return null;
    }
  }

  /**
   * 获取预加载的页面数据
   */
  getPreloadedData(url) {
    const cached = this.preloadedPages.get(url);

    if (cached) {
      // 检查是否过期（5分钟）
      if (Date.now() - cached.timestamp < 5 * 60 * 1000) {
        return cached.data;
      } else {
        this.preloadedPages.delete(url);
      }
    }

    return null;
  }

  /**
   * 加载页面数据
   */
  async loadPageData(url, data) {
    // 根据页面类型加载不同数据
    switch (url) {
      case '/pages/products/products':
        return this.loadProductsData(data);
      case '/pages/community/community':
        return this.loadCommunityData(data);
      default:
        return null;
    }
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    const expireTime = 5 * 60 * 1000; // 5分钟

    for (const [url, cached] of this.preloadedPages.entries()) {
      if (now - cached.timestamp > expireTime) {
        this.preloadedPages.delete(url);
      }
    }

    // 限制缓存数量
    if (this.preloadedPages.size > this.maxPreloadPages) {
      const entries = Array.from(this.preloadedPages.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      // 删除最旧的缓存
      const toDelete = entries.slice(0, entries.length - this.maxPreloadPages);
      toDelete.forEach(([url]) => {
        this.preloadedPages.delete(url);
      });
    }
  }
}

const pagePreloader = new PagePreloader();
module.exports = pagePreloader;
```

## 监控体系

### 应用监控

**性能监控**:
```javascript
// src/utils/monitor.js
/**
 * 应用监控工具
 * 收集性能指标和错误信息
 */
const logger = require('./logger');
const redisClient = require('./redis');

class ApplicationMonitor {
  constructor() {
    this.metrics = {
      requests: 0,
      errors: 0,
      responseTime: [],
      memoryUsage: [],
      cpuUsage: []
    };

    // 启动监控
    this.startMonitoring();
  }

  /**
   * 启动监控
   */
  startMonitoring() {
    // 每分钟收集系统指标
    setInterval(() => {
      this.collectSystemMetrics();
    }, 60000);

    // 每5分钟发送指标到监控系统
    setInterval(() => {
      this.sendMetrics();
    }, 5 * 60000);
  }

  /**
   * 记录请求指标
   */
  recordRequest(req, res, responseTime) {
    this.metrics.requests++;
    this.metrics.responseTime.push(responseTime);

    // 记录慢请求
    if (responseTime > 1000) {
      logger.warn('慢请求检测', {
        url: req.url,
        method: req.method,
        responseTime,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }

    // 记录错误请求
    if (res.statusCode >= 400) {
      this.metrics.errors++;

      logger.error('错误请求', {
        url: req.url,
        method: req.method,
        statusCode: res.statusCode,
        responseTime,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
  }

  /**
   * 收集系统指标
   */
  collectSystemMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    this.metrics.memoryUsage.push({
      timestamp: Date.now(),
      rss: memUsage.rss,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external
    });

    this.metrics.cpuUsage.push({
      timestamp: Date.now(),
      user: cpuUsage.user,
      system: cpuUsage.system
    });

    // 限制数组大小
    if (this.metrics.memoryUsage.length > 60) {
      this.metrics.memoryUsage = this.metrics.memoryUsage.slice(-60);
    }

    if (this.metrics.cpuUsage.length > 60) {
      this.metrics.cpuUsage = this.metrics.cpuUsage.slice(-60);
    }

    if (this.metrics.responseTime.length > 1000) {
      this.metrics.responseTime = this.metrics.responseTime.slice(-1000);
    }
  }

  /**
   * 发送指标到监控系统
   */
  async sendMetrics() {
    try {
      const metrics = this.calculateMetrics();

      // 存储到Redis
      await redisClient.lpush('app:metrics', JSON.stringify({
        timestamp: Date.now(),
        ...metrics
      }));

      // 限制列表长度
      await redisClient.ltrim('app:metrics', 0, 288); // 保留24小时数据

      logger.info('监控指标已发送', metrics);

    } catch (error) {
      logger.error('发送监控指标失败:', error);
    }
  }

  /**
   * 计算指标
   */
  calculateMetrics() {
    const { responseTime, memoryUsage, cpuUsage } = this.metrics;

    // 响应时间统计
    const avgResponseTime = responseTime.length > 0
      ? responseTime.reduce((a, b) => a + b, 0) / responseTime.length
      : 0;

    const p95ResponseTime = this.calculatePercentile(responseTime, 0.95);
    const p99ResponseTime = this.calculatePercentile(responseTime, 0.99);

    // 内存使用统计
    const latestMemory = memoryUsage[memoryUsage.length - 1] || {};

    return {
      requests: this.metrics.requests,
      errors: this.metrics.errors,
      errorRate: this.metrics.requests > 0 ? this.metrics.errors / this.metrics.requests : 0,
      avgResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      memoryUsage: latestMemory,
      uptime: process.uptime()
    };
  }

  /**
   * 计算百分位数
   */
  calculatePercentile(arr, percentile) {
    if (arr.length === 0) return 0;

    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil(sorted.length * percentile) - 1;

    return sorted[index] || 0;
  }

  /**
   * 获取监控数据
   */
  async getMetrics() {
    try {
      const metricsData = await redisClient.lrange('app:metrics', 0, -1);

      return metricsData.map(data => JSON.parse(data));

    } catch (error) {
      logger.error('获取监控数据失败:', error);
      return [];
    }
  }
}

module.exports = new ApplicationMonitor();
```

### 错误监控

```javascript
// src/utils/errorHandler.js
/**
 * 全局错误处理器
 * 统一处理和记录错误
 */
const logger = require('./logger');
const monitor = require('./monitor');

class ErrorHandler {
  /**
   * 处理未捕获的异常
   */
  handleUncaughtException() {
    process.on('uncaughtException', (error) => {
      logger.error('未捕获的异常:', {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });

      // 优雅关闭应用
      this.gracefulShutdown('uncaughtException');
    });
  }

  /**
   * 处理未处理的Promise拒绝
   */
  handleUnhandledRejection() {
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝:', {
        reason: reason,
        promise: promise,
        timestamp: new Date().toISOString()
      });

      // 优雅关闭应用
      this.gracefulShutdown('unhandledRejection');
    });
  }

  /**
   * Express错误处理中间件
   */
  expressErrorHandler() {
    return (error, req, res, next) => {
      // 记录错误
      logger.error('Express错误:', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        headers: req.headers,
        body: req.body,
        user: req.user?.id,
        timestamp: new Date().toISOString()
      });

      // 返回错误响应
      const statusCode = error.statusCode || 500;
      const message = process.env.NODE_ENV === 'production'
        ? '服务器内部错误'
        : error.message;

      res.status(statusCode).json({
        code: statusCode,
        message: message,
        timestamp: Date.now()
      });
    };
  }

  /**
   * 优雅关闭应用
   */
  gracefulShutdown(signal) {
    logger.info(`收到${signal}信号，开始优雅关闭...`);

    // 停止接收新请求
    if (global.server) {
      global.server.close(() => {
        logger.info('HTTP服务器已关闭');

        // 关闭数据库连接
        this.closeConnections();

        // 退出进程
        process.exit(1);
      });
    } else {
      process.exit(1);
    }
  }

  /**
   * 关闭数据库连接
   */
  async closeConnections() {
    try {
      // 关闭Redis连接
      const redisClient = require('./redis');
      await redisClient.quit();

      // 关闭MySQL连接
      const database = require('./database');
      await database.end();

      logger.info('数据库连接已关闭');

    } catch (error) {
      logger.error('关闭数据库连接失败:', error);
    }
  }
}

module.exports = new ErrorHandler();
```

## 部署架构

### Docker容器化

**Dockerfile**:
```dockerfile
# Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制应用代码
COPY . .

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 设置文件权限
RUN chown -R nodejs:nodejs /app
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# 启动应用
CMD ["npm", "start"]
```

**Docker Compose配置**:
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    networks:
      - app-network

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=meituan_alliance
      - MYSQL_USER=app
      - MYSQL_PASSWORD=apppassword
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app-network

volumes:
  mysql_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### CI/CD流水线

**GitHub Actions配置**:
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Run linting
      run: npm run lint

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Build Docker image
      run: |
        docker build -t meituan-alliance:${{ github.sha }} .
        docker tag meituan-alliance:${{ github.sha }} meituan-alliance:latest

    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push meituan-alliance:${{ github.sha }}
        docker push meituan-alliance:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /opt/meituan-alliance
          docker-compose pull
          docker-compose up -d
          docker system prune -f
```

### 负载均衡配置

**Nginx配置**:
```nginx
# nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    access_log /var/log/nginx/access.log main;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript
               application/javascript application/xml+rss
               application/json application/xml;

    # 上游服务器
    upstream app_servers {
        least_conn;
        server app1:3000 max_fails=3 fail_timeout=30s;
        server app2:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    server {
        listen 80;
        server_name your-domain.com;

        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API代理
        location /api/ {
            limit_req zone=api burst=20 nodelay;

            proxy_pass http://app_servers;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # 超时设置
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 登录接口特殊限流
        location /api/auth/login {
            limit_req zone=login burst=5 nodelay;

            proxy_pass http://app_servers;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 静态文件
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

## 总结

美团联盟小程序项目采用轻量级的现代化架构，具备以下特点：

1. **前后端分离**: 微信小程序前端 + Python FastAPI后端
2. **轻量级存储**: SQLite数据库，JSON配置文件
3. **安全性**: JWT认证、输入验证、HTTPS
4. **易部署**: 单文件数据库、容器化部署
5. **配置灵活**: JSON文件配置广告和内容
6. **开发友好**: 自动API文档、热重载开发

该架构适合中小型应用快速开发和部署，通过SQLite和JSON配置实现了简单高效的数据管理。